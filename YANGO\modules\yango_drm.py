#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
YANGO DRM Module
Handles DRM operations for YANGO PLAY content
"""

import requests
import base64
import xml.etree.ElementTree as ET
import os
from PySide6.QtCore import QObject, Signal

# Import pywidevine components
import sys
import os

# Add the binaries path to sys.path to import pywidevine
yango_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
binaries_path = os.path.join(yango_root, 'binaries')
if binaries_path not in sys.path:
    sys.path.insert(0, binaries_path)

try:
    from pywidevine.cdm import Cdm, deviceconfig
    PYWIDEVINE_AVAILABLE = True
    print("✅ pywidevine imported successfully")
except ImportError as e:
    PYWIDEVINE_AVAILABLE = False
    print(f"❌ pywidevine not available: {e}")
    print("⚠️ DRM key extraction will not work without pywidevine")

class YangoDRM(QObject):
    # Signals for DRM operations
    keys_extracted = Signal(list)
    drm_error = Signal(str)

    def __init__(self):
        super().__init__()
        self.widevine_proxy_url = "https://widevine-proxy.movies.funtechservices.com/proxy"

    def get_pssh_from_mpd(self, mpd_url):
        """Extract PSSH from MPD file"""
        try:
            print(f"🔍 Extracting PSSH from MPD: {mpd_url}")

            response = requests.get(mpd_url)
            response.raise_for_status()

            # Parse XML
            root = ET.fromstring(response.content)

            # Find PSSH in ContentProtection elements
            for elem in root.iter():
                if 'ContentProtection' in elem.tag:
                    # Look for Widevine PSSH
                    if elem.get('schemeIdUri') == 'urn:uuid:edef8ba9-79d6-4ace-a3c8-27dcd51d21ed':
                        for child in elem:
                            if 'pssh' in child.tag.lower():
                                pssh = child.text
                                if pssh:
                                    print(f"✅ PSSH extracted: {pssh[:50]}...")
                                    return pssh.strip()

            # Alternative method - look for cenc:pssh
            namespaces = {
                'cenc': 'urn:mpeg:cenc:2013',
                'mpd': 'urn:mpeg:dash:schema:mpd:2011'
            }

            pssh_elements = root.findall('.//cenc:pssh', namespaces)
            if pssh_elements:
                pssh = pssh_elements[0].text
                if pssh:
                    print(f"✅ PSSH extracted (alternative method): {pssh[:50]}...")
                    return pssh.strip()

            print("❌ No PSSH found in MPD")
            return None

        except Exception as e:
            error_msg = f"Error extracting PSSH: {str(e)}"
            print(f"❌ {error_msg}")
            self.drm_error.emit(error_msg)
            return None

    def get_kid_from_mpd(self, mpd_url):
        """Extract KID from MPD file"""
        try:
            print(f"🔍 Extracting KID from MPD: {mpd_url}")

            response = requests.get(mpd_url)
            response.raise_for_status()

            # Parse XML
            root = ET.fromstring(response.content)

            # Find KID in ContentProtection elements
            for elem in root.iter():
                if 'ContentProtection' in elem.tag:
                    kid = elem.get('cenc:default_KID')
                    if kid:
                        # Remove dashes and convert to lowercase
                        kid = kid.replace('-', '').lower()
                        print(f"✅ KID extracted: {kid}")
                        return kid

            print("❌ No KID found in MPD")
            return None

        except Exception as e:
            error_msg = f"Error extracting KID: {str(e)}"
            print(f"❌ {error_msg}")
            self.drm_error.emit(error_msg)
            return None

    def extract_drm_keys(self, mpd_url, request_params):
        """Extract DRM keys using the YANGO method"""
        try:
            print(f"🔑 Extracting DRM keys for MPD: {mpd_url}")

            # Extract PSSH from MPD
            pssh = self.get_pssh_from_mpd(mpd_url)
            if not pssh:
                self.drm_error.emit("Failed to extract PSSH from MPD")
                return []

            # Get decryption keys using the original YANGO method
            keys = self.get_decryption_key(pssh, request_params)

            if keys:
                print(f"✅ Extracted {len(keys)} DRM keys")
                self.keys_extracted.emit(keys)
                return keys
            else:
                self.drm_error.emit("Failed to extract DRM keys")
                return []

        except Exception as e:
            error_msg = f"Error extracting DRM keys: {str(e)}"
            print(f"❌ {error_msg}")
            self.drm_error.emit(error_msg)
            return []

    def get_decryption_key(self, pssh, request_params):
        """Get decryption key using the original YANGO method with pywidevine"""
        try:
            if not PYWIDEVINE_AVAILABLE:
                print("❌ pywidevine not available - cannot extract DRM keys")
                return []

            print("🔑 Getting decryption key using YANGO method with pywidevine...")

            # Extract parameters from request_params
            content_id = request_params.get('contentId', '')
            monetization_model = request_params.get('monetizationModel', '')
            expiration_timestamp = request_params.get('expirationTimestamp', '')
            signature = request_params.get('signature', '')
            version = request_params.get('version', '')
            puid = request_params.get('puid', '')
            watch_session_id = request_params.get('watchSessionId', '')
            service_name = request_params.get('serviceName', '')
            product_id = request_params.get('productId', '')
            content_type_id = request_params.get('contentTypeId', '')
            verification_required = request_params.get('verificationRequired', True)

            if not all([content_id, signature, puid, watch_session_id]):
                print("❌ Missing required DRM parameters")
                print(f"   contentId: {bool(content_id)}")
                print(f"   signature: {bool(signature)}")
                print(f"   puid: {bool(puid)}")
                print(f"   watchSessionId: {bool(watch_session_id)}")
                return []

            # Check if device.wvd exists - use current working directory for exe compatibility
            # Try multiple possible locations for device.wvd
            possible_device_paths = [
                os.path.join(os.getcwd(), "binaries", "device.wvd"),  # binaries folder
                os.path.join(os.getcwd(), "device.wvd"),  # Current directory
                "device.wvd",  # Current directory fallback
            ]

            device_path = None
            for path in possible_device_paths:
                if os.path.exists(path):
                    device_path = path
                    print(f"✅ Found device file at: {path}")
                    break

            if not device_path:
                print(f"❌ Device file not found in any of these locations:")
                for path in possible_device_paths:
                    print(f"   - {os.path.abspath(path)}")
                print("   Please ensure device.wvd is available")
                return []

            # Load device and create CDM (adapted for unified pywidevine)
            print("📱 Loading Widevine device...")

            # Use the old-style CDM initialization that matches the unified pywidevine
            cdm = Cdm()

            # Load device config for the old-style API
            device_config = deviceconfig.DeviceConfig(deviceconfig.device_chromecdm_2209)

            # Open CDM session with PSSH
            print("🔓 Opening CDM session...")
            session_id = cdm.open_session(pssh, device_config)

            if session_id == 1:  # Error code
                print("❌ Failed to open CDM session")
                return []

            # Get license request (challenge)
            print("🎯 Creating license challenge...")
            challenge = cdm.get_license_request(session_id)

            # Prepare license request data (following original YANGO format exactly)
            print("📤 Preparing license request...")
            data = (
                '{"puid":' + str(puid) +
                ',"watchSessionId":"' + str(watch_session_id) +
                '","contentId":"' + str(content_id) +
                '","contentTypeId":' + str(content_type_id) +
                ',"serviceName":"' + str(service_name) +
                '","productId":' + str(product_id) +
                ',"monetizationModel":"' + str(monetization_model) +
                '","expirationTimestamp":' + str(expiration_timestamp) +
                ',"verificationRequired":' + 'true' +  # Always 'true' as string like original
                ',"signature":"' + str(signature) +
                '","version":"' + str(version) +
                '","rawLicenseRequestBase64":"' + base64.b64encode(challenge).decode() + '"}'
            )

            # Send license request (exactly like original YANGO script)
            print("🌐 Sending license request to Widevine proxy...")
            print(f"   📤 Request data: {data[:200]}...")  # Debug: show first 200 chars
            print(f"   📤 Data length: {len(data)} bytes")
            print(f"   📤 Challenge length: {len(challenge)} bytes")

            # Send as raw data, exactly like original script
            license_response = requests.post(
                "https://widevine-proxy.movies.funtechservices.com/proxy",
                data=data
            )

            print(f"   📥 Response status: {license_response.status_code}")
            print(f"   📥 Response headers: {dict(license_response.headers)}")
            if license_response.status_code != 200:
                print(f"   📥 Response text: {license_response.text}")

            license_response.raise_for_status()

            # Parse license
            print("🔓 Parsing license response...")
            cdm.provide_license(session_id, base64.b64encode(license_response.content))

            # Extract keys (following original YANGO script exactly)
            print("🔑 Extracting decryption keys...")
            keys = []
            session_keys = cdm.get_keys(session_id)
            if session_keys != 1:  # Not an error code
                for key in session_keys:
                    if "CONTENT" in key.type:
                        # Use key.kid.hex() and key.key.hex() methods
                        key_string = f"{key.kid.hex()}:{key.key.hex()}"
                        keys.append(key_string)
                        print(f"   ✅ Key: {key_string}")

            # Close CDM session
            cdm.close_session(session_id)

            if keys:
                print(f"✅ Successfully extracted {len(keys)} DRM keys")
                return keys
            else:
                print("❌ No content keys found")
                return []

        except Exception as e:
            error_msg = f"Error in get_decryption_key: {str(e)}"
            print(f"❌ {error_msg}")
            return []

    def save_keys_to_file(self, keys, title="Unknown"):
        """Save DRM keys to file"""
        try:
            import os

            # Create keys directory
            keys_dir = "KEYS"
            os.makedirs(keys_dir, exist_ok=True)

            keys_file = os.path.join(keys_dir, "YANGO_KEYS.txt")

            with open(keys_file, "a", encoding="utf-8") as f:
                f.write(f"\n# {title}\n")
                for key in keys:
                    f.write(f"{key}\n")

            print(f"✅ Keys saved to {keys_file}")

        except Exception as e:
            error_msg = f"Error saving keys to file: {str(e)}"
            print(f"❌ {error_msg}")

    def format_keys_for_player(self, keys):
        """Format keys for the player"""
        try:
            if not keys:
                return []

            formatted_keys = []
            for key in keys:
                if isinstance(key, str) and ':' in key:
                    formatted_keys.append(key)
                elif isinstance(key, dict):
                    # Handle dictionary format if needed
                    kid = key.get('kid', '')
                    key_value = key.get('key', '')
                    if kid and key_value:
                        formatted_keys.append(f"{kid}:{key_value}")

            return formatted_keys

        except Exception as e:
            error_msg = f"Error formatting keys: {str(e)}"
            print(f"❌ {error_msg}")
            return []

    def validate_drm_keys(self, keys):
        """Validate DRM keys format"""
        try:
            if not keys:
                return False

            for key in keys:
                if not isinstance(key, str):
                    return False
                if ':' not in key:
                    return False
                parts = key.split(':')
                if len(parts) != 2:
                    return False
                # Check if both parts are hex strings
                try:
                    int(parts[0], 16)
                    int(parts[1], 16)
                except ValueError:
                    return False

            return True

        except Exception as e:
            print(f"❌ Error validating keys: {str(e)}")
            return False

    def get_mpd_info(self, mpd_url):
        """Get comprehensive information from MPD file"""
        try:
            print(f"🔍 Getting MPD info: {mpd_url}")

            response = requests.get(mpd_url)
            response.raise_for_status()

            # Parse XML
            root = ET.fromstring(response.content)

            info = {
                'pssh': self.get_pssh_from_mpd(mpd_url),
                'kid': self.get_kid_from_mpd(mpd_url),
                'video_tracks': [],
                'audio_tracks': [],
                'subtitle_tracks': []
            }

            # Extract track information
            namespaces = {'mpd': 'urn:mpeg:dash:schema:mpd:2011'}

            # Find adaptation sets
            adaptation_sets = root.findall('.//mpd:AdaptationSet', namespaces)

            for adaptation_set in adaptation_sets:
                content_type = adaptation_set.get('contentType', '')
                mime_type = adaptation_set.get('mimeType', '')

                if content_type == 'video' or 'video' in mime_type:
                    # Video tracks
                    representations = adaptation_set.findall('.//mpd:Representation', namespaces)
                    for rep in representations:
                        track_info = {
                            'id': rep.get('id', ''),
                            'bandwidth': rep.get('bandwidth', ''),
                            'width': rep.get('width', ''),
                            'height': rep.get('height', ''),
                            'codecs': rep.get('codecs', '')
                        }
                        info['video_tracks'].append(track_info)

                elif content_type == 'audio' or 'audio' in mime_type:
                    # Audio tracks
                    representations = adaptation_set.findall('.//mpd:Representation', namespaces)
                    for rep in representations:
                        track_info = {
                            'id': rep.get('id', ''),
                            'bandwidth': rep.get('bandwidth', ''),
                            'language': adaptation_set.get('lang', ''),
                            'codecs': rep.get('codecs', '')
                        }
                        info['audio_tracks'].append(track_info)

                elif content_type == 'text' or 'text' in mime_type:
                    # Subtitle tracks
                    track_info = {
                        'language': adaptation_set.get('lang', ''),
                        'mime_type': mime_type
                    }
                    info['subtitle_tracks'].append(track_info)

            print(f"✅ MPD info extracted: {len(info['video_tracks'])} video, {len(info['audio_tracks'])} audio, {len(info['subtitle_tracks'])} subtitle tracks")
            return info

        except Exception as e:
            error_msg = f"Error getting MPD info: {str(e)}"
            print(f"❌ {error_msg}")
            return None
