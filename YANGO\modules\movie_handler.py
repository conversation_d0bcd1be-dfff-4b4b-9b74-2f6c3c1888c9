"""
Movie Handler Module for YANGO_NEW
Handles movie-specific functionality including stream processing and DRM key extraction
"""

from PySide6.QtWidgets import QMessageBox
import traceback


class MovieHandler:
    """Handler for movie-specific operations"""

    def __init__(self, main_ui):
        self.main_ui = main_ui

    def handle_continue_for_movie(self, content_id):
        """Handle continue button click for movies - go directly to Available Streams"""
        try:
            print("🎬 Loading streams for Movie...")

            # Switch to Available Streams tab
            self.main_ui.content_tabs.setCurrentIndex(2)  # Available Streams tab

            # Load movie streams
            self.load_movie_streams(content_id)

        except Exception as e:
            error_msg = f"Error handling continue for movie: {str(e)}"
            print(f"❌ {error_msg}")
            self.show_message("Error", error_msg)

    def load_movie_streams(self, content_id):
        """Load streams for a movie"""
        try:
            print(f"🎬 Loading streams for Movie ID: {content_id}")

            # Clear existing streams display
            self.main_ui.clear_streams_display()

            # Use the API to fetch movie streams
            if hasattr(self.main_ui.main_window, 'yango_api'):
                # Call the API method to fetch movie streams
                streams_data = self.main_ui.main_window.yango_api.get_player_base_info(content_id)

                if streams_data:
                    print(f"🎬 Movie streams loaded successfully")
                    self.display_movie_streams(streams_data)
                else:
                    self.show_message("Error", "No streams found for this movie")
            else:
                self.show_message("Error", "API not available")

        except Exception as e:
            error_msg = f"Error loading movie streams: {str(e)}"
            print(f"❌ {error_msg}")
            self.show_message("Error", error_msg)

    def display_movie_streams(self, streams_data):
        """Display movie streams information"""
        try:
            print(f"🎬 Displaying streams for Movie")

            # Extract streams information from the response (following original YANGO script)
            content_data = streams_data.get('data', {}).get('content', {})

            if not content_data:
                self.show_message("Error", "No content data found in streams response")
                return

            # For movies, get filmOtt data (following original YANGO script pattern)
            # Try both filmOtt and movieOtt to handle different API responses
            film_ott = content_data.get('filmOtt', {})
            if not film_ott:
                film_ott = content_data.get('movieOtt', {})
            online_streams = film_ott.get('onlineStreams', {})

            if not online_streams:
                self.show_message("Info", "No online streams available for this movie")
                return

            # Extract streams information
            streams = online_streams.get('streams', [])

            if not streams:
                self.show_message("Info", "No streams found for this movie")
                return

            print(f"🎬 Found {len(streams)} streams for Movie")

            # Process and display streams (following original script logic)
            self.process_movie_streams(streams)

        except Exception as e:
            error_msg = f"Error displaying movie streams: {str(e)}"
            print(f"❌ {error_msg}")
            self.show_message("Error", error_msg)

    def process_movie_streams(self, streams):
        """Process and display movie streams (following original YANGO script logic)"""
        try:
            print(f"🔄 Processing {len(streams)} streams for Movie")

            # Initialize collections (same logic as original script)
            qualities = set()
            audio_tracks = set()
            subtitle_tracks = set()
            decryption_keys = set()
            mpd_url = ""

            # Step 1: Extract DRM keys first (following original YANGO script pattern)
            # Find the stream with DRM config (usually streams[1] in original script)
            drm_stream = None
            for i, stream in enumerate(streams):
                stream_meta = stream.get('streamMeta', {})
                stream_uri = stream.get('uri', '')
                drm_config = stream_meta.get('drmConfig', {})

                if drm_config and stream_uri and 'manifest.mpd' in stream_uri:
                    drm_stream = stream
                    mpd_url = stream_uri
                    print(f"🔍 Found DRM stream {i+1}/{len(streams)}")
                    print(f"   🔗 MPD URL: {mpd_url[:100]}...")
                    break

            # Extract DRM keys if DRM stream found
            if drm_stream:
                stream_meta = drm_stream.get('streamMeta', {})
                drm_config = stream_meta.get('drmConfig', {})
                request_params = drm_config.get('requestParams', {})

                if request_params:
                    print(f"🔑 Extracting real DRM keys for movie")

                    # Use YangoDRM to extract real keys
                    from .yango_drm import YangoDRM
                    drm_handler = YangoDRM()

                    # Extract PSSH from MPD
                    pssh = drm_handler.get_pssh_from_mpd(mpd_url)
                    if pssh:
                        print(f"   ✅ PSSH extracted: {pssh[:50]}...")

                        # Get real decryption keys
                        keys = drm_handler.get_decryption_key(pssh, request_params)
                        if keys:
                            # Format keys for display
                            for key in keys:
                                formatted_key = f"[+] Key: {key}"
                                decryption_keys.add(formatted_key)
                                print(f"   🔑 Real DRM Key: {formatted_key}")
                        else:
                            print("   ❌ No real keys extracted")
                    else:
                        print("   ❌ No PSSH found in MPD")

            # Step 2: Extract video qualities, audio tracks, and subtitles from ALL streams
            # (following original script pattern - process all streams for metadata)
            for i, stream in enumerate(streams):
                print(f"🔍 Processing stream {i+1}/{len(streams)} for metadata")

                stream_meta = stream.get('streamMeta', {})
                stream_uri = stream.get('uri', '')

                # Extract MPD URL if not found yet
                if stream_uri and '.mpd' in stream_uri and not mpd_url:
                    mpd_url = stream_uri
                    print(f"   🔗 Found MPD URL: {mpd_url[:100]}...")

                # Extract video qualities with detailed information
                video_metas = stream_meta.get('videoMetas', [])
                for video_meta in video_metas:
                    height = video_meta.get('height', 0)
                    width = video_meta.get('width', 0)

                    if height:
                        # Map actual YANGO resolutions to display names (following original code pattern)
                        original_resolution = f"{width}x{height}"

                        if height == 960:  # 1920x960
                            quality_str = '1080p'
                        elif height == 812:  # 1920x812 (new format)
                            quality_str = '1080p'
                        elif height == 804:  # 1920x804 (another format)
                            quality_str = '1080p'
                        elif height == 640:  # 1280x640
                            quality_str = '720p'
                        elif height == 542:  # 1280x542 (new format)
                            quality_str = '720p'
                        elif height == 536:  # 1280x536 (another format)
                            quality_str = '720p'
                        elif height == 512:  # 1024x512
                            quality_str = '520p'
                        elif height == 434:  # 1024x434 (new format)
                            quality_str = '430p'
                        elif height == 428:  # 854x428 or 1024x428
                            quality_str = '430p'
                        elif height == 362:  # 854x362 (new format)
                            quality_str = '360p'
                        elif height == 358:  # 854x358 (another format)
                            quality_str = '360p'
                        elif height == 320:  # 640x320
                            quality_str = '320p'
                        elif height == 270:  # 640x270 (new format)
                            quality_str = '260p'
                        elif height == 268:  # 640x268 (another format)
                            quality_str = '260p'
                        elif height >= 2160:
                            quality_str = '2160p'
                        elif height >= 1080:
                            quality_str = '1080p'
                        elif height >= 720:
                            quality_str = '720p'
                        else:
                            quality_str = f'{height}p'

                        # Store both display quality and original resolution
                        quality_with_resolution = f"{quality_str}|{original_resolution}"
                        qualities.add(quality_with_resolution)
                        print(f"   📺 Video: {quality_str} ({original_resolution})")

                # Extract audio tracks with detailed information
                audio_metas = stream_meta.get('audioMetas', [])
                for audio_meta in audio_metas:
                    language = audio_meta.get('languageName', 'Unknown')
                    quality = audio_meta.get('quality', '')
                    channels = audio_meta.get('audioChannelsNumber', 2)

                    # Format audio track display
                    if quality == 'SURROUND_51':
                        audio_str = f'{language} - 5.1 Surround ({channels} channels)'
                    elif quality == 'STEREO':
                        audio_str = f'{language} - Stereo ({channels} channels)'
                    else:
                        audio_str = f'{language} - {quality} ({channels} channels)' if quality else f'{language} ({channels} channels)'

                    audio_tracks.add(audio_str)
                    print(f"   🔊 Audio: {audio_str}")

                # Extract subtitle tracks
                subtitle_metas = stream_meta.get('subtitleMetas', [])
                for subtitle_meta in subtitle_metas:
                    language = subtitle_meta.get('languageName', 'Unknown')
                    subtitle_type = subtitle_meta.get('type', '')

                    # Format subtitle display
                    if subtitle_type:
                        subtitle_str = f'{language} ({subtitle_type})'
                    else:
                        subtitle_str = language

                    subtitle_tracks.add(subtitle_str)
                    print(f"   📝 Subtitle: {subtitle_str}")

            # Store MPD URL and decryption keys for download
            self.main_ui.current_mpd_url = mpd_url
            self.main_ui.current_decryption_keys = list(decryption_keys)

            # Display the information for movie
            self.display_movie_streams_info(qualities, audio_tracks, subtitle_tracks, mpd_url, decryption_keys)

        except Exception as e:
            error_msg = f"Error processing movie streams: {str(e)}"
            print(f"❌ {error_msg}")
            import traceback
            traceback.print_exc()
            self.show_message("Error", error_msg)

    def display_movie_streams_info(self, qualities, audio_tracks, subtitle_tracks, mpd_url, decryption_keys):
        """Display movie streams information in the UI"""
        try:
            print(f"🎬 Displaying movie streams info")
            print(f"   📺 Qualities: {len(qualities)}")
            print(f"   🔊 Audio tracks: {len(audio_tracks)}")
            print(f"   📝 Subtitles: {len(subtitle_tracks)}")
            print(f"   🔑 Keys: {len(decryption_keys)}")

            # Sort qualities from highest to lowest before displaying
            sorted_qualities = self.sort_qualities_with_resolution(list(qualities))
            print(f"   🔄 Sorted qualities: {[q.split('|')[0] if '|' in q else q for q in sorted_qualities]}")

            # Store MPD URL and keys for download
            self.main_ui.current_mpd_url = mpd_url
            self.main_ui.current_decryption_keys = list(decryption_keys)

            # Save keys to file (following original YANGO script pattern)
            if decryption_keys:
                self.save_keys_to_file(decryption_keys)

            # Use the existing display_movie_streams_info method from main_ui
            # This method handles the UI display with checkboxes
            self.main_ui.display_movie_streams_info(sorted_qualities, audio_tracks, subtitle_tracks, mpd_url, decryption_keys)

            print(f"✅ Movie streams displayed successfully")

        except Exception as e:
            error_msg = f"Error displaying movie streams info: {str(e)}"
            print(f"❌ {error_msg}")
            self.show_message("Error", error_msg)

    def sort_qualities(self, qualities):
        """Sort video qualities in descending order"""
        def quality_to_number(quality):
            # Extract number from quality string (e.g., "1080p" -> 1080)
            try:
                return int(quality.replace('p', ''))
            except:
                return 0

        return sorted(qualities, key=quality_to_number, reverse=True)

    def sort_qualities_with_resolution(self, qualities):
        """Sort video qualities with resolution data in descending order"""
        def quality_to_number(quality_data):
            # Extract display quality from format "1080p|1920x804" or just "1080p"
            if '|' in quality_data:
                display_quality = quality_data.split('|')[0]
            else:
                display_quality = quality_data

            # Extract number from quality string (e.g., "1080p" -> 1080)
            try:
                return int(display_quality.replace('p', ''))
            except:
                return 0

        return sorted(qualities, key=quality_to_number, reverse=True)

    def save_keys_to_file(self, decryption_keys):
        """Save decryption keys to KEYS.txt file (following original YANGO script pattern)"""
        try:
            import os

            # Create KEYS directory if it doesn't exist
            keys_dir = os.path.join(os.getcwd(), "KEYS")
            os.makedirs(keys_dir, exist_ok=True)

            # Path to KEYS.txt file
            keys_file = os.path.join(keys_dir, "KEYS.txt")

            # Write keys to file
            with open(keys_file, 'w', encoding='utf-8') as f:
                for key in decryption_keys:
                    # Remove the "[+] Key: " prefix for the file
                    clean_key = key.replace("[+] Key: ", "")
                    f.write(f"{clean_key}\n")

            print(f"🔑 Saved {len(decryption_keys)} keys to {keys_file}")

        except Exception as e:
            print(f"❌ Error saving keys to file: {str(e)}")

    def show_message(self, title, message):
        """Show message dialog"""
        try:
            msg_box = QMessageBox()
            msg_box.setWindowTitle(title)
            msg_box.setText(message)
            if title.lower() == "error":
                msg_box.setIcon(QMessageBox.Critical)
            elif title.lower() == "warning":
                msg_box.setIcon(QMessageBox.Warning)
            else:
                msg_box.setIcon(QMessageBox.Information)
            msg_box.exec()
        except Exception as e:
            print(f"❌ Error showing message: {str(e)}")
