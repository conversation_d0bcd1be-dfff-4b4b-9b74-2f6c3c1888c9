"""
Chrome Embedded Player Module
Embeds Google Chrome browser directly in the application window.
"""

import os
import sys
import subprocess
import threading
import socketserver
import urllib.parse
import http.server
import winreg
from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QFrame
from PySide6.QtCore import Qt, QTimer, Signal, QObject, QEvent
from PySide6.QtGui import QWindow
import win32gui
import win32con
import win32process


class PlayerHTTPHandler(http.server.SimpleHTTPRequestHandler):
    """Custom HTTP handler with CORS support."""

    def log_message(self, format, *args):
        """Log HTTP requests."""
        print(f"[HTTP] {format % args}")

    def end_headers(self):
        """Add CORS headers."""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS, PUT, DELETE')
        self.send_header('Access-Control-Allow-Headers', '*')
        self.send_header('Access-Control-Allow-Credentials', 'true')
        super().end_headers()


class PlayerSignals(QObject):
    """Signals for player events."""
    player_started = Signal(bool)
    player_error = Signal(str)
    page_loaded = Signal()


class ChromePlayerWidget(QWidget):
    """Widget that embeds Chrome browser for playing content."""

    def __init__(self):
        super().__init__()
        self.signals = PlayerSignals()
        self.http_server = None
        self.server_thread = None
        self.chrome_process = None
        self.chrome_hwnd = None
        self.player_url = None

        self.setup_ui()

    def setup_ui(self):
        """Setup the user interface."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)

        # Header with controls
        header = QFrame()
        header.setFixedHeight(40)
        header.setStyleSheet("""
            QFrame {
                background-color: #2b2b2b;
                border-bottom: 1px solid #444;
            }
        """)

        header_layout = QHBoxLayout(header)
        header_layout.setContentsMargins(10, 5, 10, 5)

        # Title
        title_label = QLabel("🌐 Chrome Player (Embedded)")
        title_label.setStyleSheet("color: white; font-weight: bold;")
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # Control buttons
        self.refresh_btn = QPushButton("🔄 Refresh")
        self.refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 5px 15px;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        self.refresh_btn.clicked.connect(self.refresh_browser)
        header_layout.addWidget(self.refresh_btn)

        self.devtools_btn = QPushButton("🔧 DevTools")
        self.devtools_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 5px 15px;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        self.devtools_btn.clicked.connect(self.open_devtools)
        header_layout.addWidget(self.devtools_btn)

        self.close_btn = QPushButton("❌ Close")
        self.close_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 5px 15px;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
        """)
        self.close_btn.clicked.connect(self.close_player)
        header_layout.addWidget(self.close_btn)

        layout.addWidget(header)

        # Browser container
        self.browser_container = QFrame()
        self.browser_container.setMinimumSize(800, 600)
        self.browser_container.setStyleSheet("""
            QFrame {
                background-color: #000;
                border: 2px solid #444;
            }
        """)

        # Install event filter to handle resize events
        self.browser_container.installEventFilter(self)

        browser_layout = QVBoxLayout(self.browser_container)
        browser_layout.setContentsMargins(0, 0, 0, 0)

        # Status label
        self.status_label = QLabel("🚀 Initializing Chrome Browser...")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 20px;
            }
        """)
        browser_layout.addWidget(self.status_label)

        layout.addWidget(self.browser_container)

    def find_chrome_path(self):
        """Find Chrome installation path."""
        possible_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe"),
        ]

        # Try registry
        try:
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\chrome.exe") as key:
                chrome_path = winreg.QueryValue(key, "")
                if os.path.exists(chrome_path):
                    return chrome_path
        except:
            pass

        # Try common paths
        for path in possible_paths:
            if os.path.exists(path):
                return path

        return None

    def start_http_server(self, port=8003):
        """Start HTTP server for serving player files."""
        try:
            player_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "player")

            print(f"Trying to start HTTP server on port {port}...")
            print(f"Serving files from directory: {player_dir}")

            if not os.path.exists(player_dir):
                raise Exception(f"Player directory not found: {player_dir}")

            # List files in player directory
            files = os.listdir(player_dir)
            print(f"Files in player directory: {files}")

            # Check if shahid_player.html exists
            player_html = os.path.join(player_dir, "shahid_player.html")
            if not os.path.exists(player_html):
                raise Exception(f"Player HTML not found: {player_html}")
            print(f"✅ Found shahid_player.html at: {player_html}")

            # Create server
            os.chdir(player_dir)

            class ThreadedHTTPServer(socketserver.ThreadingMixIn, http.server.HTTPServer):
                daemon_threads = True

            self.http_server = ThreadedHTTPServer(("localhost", port), PlayerHTTPHandler)

            # Start server in thread
            self.server_thread = threading.Thread(target=self.http_server.serve_forever, daemon=True)
            self.server_thread.start()

            print(f"✅ HTTP server started on port {port}")
            print(f"🌐 Server URL: http://localhost:{port}/")

            return port

        except OSError as e:
            if e.errno == 10048:  # Port already in use
                print(f"⚠️ Port {port} is busy, trying {port + 1}...")
                return self.start_http_server(port + 1)
            else:
                raise e

    def load_player(self, mpd_url, drm_key):
        """Load the player with MPD URL and DRM key."""
        try:
            print("🚀 Loading Chrome player...")
            print(f"MPD URL: {mpd_url}")
            print(f"DRM Key: {drm_key}")

            # Start HTTP server
            port = self.start_http_server()

            # Parse DRM key
            key_parts = drm_key.split(':')
            key_id = key_parts[0] if len(key_parts) > 0 else ""
            key = key_parts[1] if len(key_parts) > 1 else ""

            # Build query parameters
            query_params = {
                'mpd': mpd_url,
                'keyId': key_id,
                'key': key
            }

            self.player_url = f"http://localhost:{port}/shahid_player.html?{urllib.parse.urlencode(query_params)}"
            print(f"Player URL: {self.player_url}")

            # Create Chrome browser with delay to ensure UI is ready
            QTimer.singleShot(500, self.create_chrome_browser)

            return True

        except Exception as e:
            error_msg = f"Error loading Chrome player: {e}"
            print(f"❌ {error_msg}")
            self.status_label.setText(f"❌ {error_msg}")
            self.signals.player_error.emit(error_msg)
            return False

    def create_chrome_browser(self):
        """Create embedded Chrome browser."""
        try:
            print("🌐 Creating Chrome browser...")

            # Find Chrome path
            chrome_path = self.find_chrome_path()
            if not chrome_path:
                raise Exception("Chrome browser not found! Please install Google Chrome.")

            print(f"🔧 Chrome path: {chrome_path}")

            # Make sure the container is visible and has proper size
            self.browser_container.show()
            self.browser_container.update()

            # Get container window handle
            container_hwnd = int(self.browser_container.winId())
            print(f"🔧 Container HWND: {container_hwnd}")

            # Get container size
            rect = self.browser_container.rect()
            width = max(rect.width(), 800)
            height = max(rect.height(), 600)
            print(f"🔧 Container size: {width}x{height}")

            # Chrome arguments for embedding - optimized for embedding
            chrome_args = [
                chrome_path,
                f"--app={self.player_url}",
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor",
                "--autoplay-policy=no-user-gesture-required",
                "--allow-running-insecure-content",
                "--disable-popup-blocking",
                "--ignore-certificate-errors",
                "--allow-file-access-from-files",
                "--allow-file-access",
                "--disable-background-timer-throttling",
                "--disable-backgrounding-occluded-windows",
                "--disable-renderer-backgrounding",
                "--enable-media-stream",
                "--use-fake-ui-for-media-stream",
                "--no-first-run",
                "--no-default-browser-check",
                "--disable-default-apps",
                "--disable-extensions",
                "--disable-session-crashed-bubble",
                "--disable-infobars",
                "--disable-translate",
                "--disable-features=TranslateUI",
                "--disable-ipc-flooding-protection",
                "--new-window",
                f"--window-size={width},{height}",
                f"--window-position=0,0",
                "--force-app-mode",
                "--disable-pinch"
            ]

            print(f"🌐 Starting Chrome with URL: {self.player_url}")

            # Start Chrome process
            self.chrome_process = subprocess.Popen(chrome_args)

            # Wait a bit for Chrome to start
            QTimer.singleShot(2000, self.embed_chrome_window)

            print("✅ Chrome browser started successfully!")
            self.status_label.setText("🌐 Loading player...")

            self.signals.player_started.emit(True)

        except Exception as e:
            error_msg = f"Error creating Chrome browser: {e}"
            print(f"❌ {error_msg}")
            self.status_label.setText(f"❌ {error_msg}")
            self.signals.player_error.emit(error_msg)

    def embed_chrome_window(self):
        """Embed Chrome window into container."""
        try:
            print("🔧 Embedding Chrome window...")

            # Wait a bit more for Chrome to fully load
            import time
            time.sleep(1)

            # Find Chrome window with more specific criteria
            def enum_windows_callback(hwnd, windows):
                if win32gui.IsWindowVisible(hwnd):
                    window_text = win32gui.GetWindowText(hwnd)
                    class_name = win32gui.GetClassName(hwnd)

                    # Look for Chrome windows specifically
                    if (("Chrome" in class_name or "Chrome_WidgetWin" in class_name) and
                        ("shahid_player" in window_text or "localhost" in window_text or window_text == "")):

                        # Get process ID to make sure it's our Chrome process
                        try:
                            _, pid = win32process.GetWindowThreadProcessId(hwnd)
                            if self.chrome_process and pid == self.chrome_process.pid:
                                windows.append((hwnd, window_text, class_name))
                                print(f"🔍 Found Chrome window: HWND={hwnd}, Text='{window_text}', Class='{class_name}'")
                        except:
                            pass
                return True

            windows = []
            win32gui.EnumWindows(enum_windows_callback, windows)

            if windows:
                # Use the first Chrome window found
                self.chrome_hwnd = windows[0][0]  # Just get the HWND
                container_hwnd = int(self.browser_container.winId())

                print(f"🔧 Embedding Chrome window HWND={self.chrome_hwnd} into container HWND={container_hwnd}")

                # Remove window decorations first
                current_style = win32gui.GetWindowLong(self.chrome_hwnd, win32con.GWL_STYLE)
                new_style = current_style & ~(win32con.WS_CAPTION | win32con.WS_THICKFRAME | win32con.WS_MINIMIZE | win32con.WS_MAXIMIZE | win32con.WS_SYSMENU)
                win32gui.SetWindowLong(self.chrome_hwnd, win32con.GWL_STYLE, new_style)

                # Set Chrome window as child of container
                result = win32gui.SetParent(self.chrome_hwnd, container_hwnd)
                print(f"🔧 SetParent result: {result}")

                # Resize and position Chrome window to fill container
                rect = self.browser_container.rect()
                print(f"🔧 Container size: {rect.width()}x{rect.height()}")

                win32gui.SetWindowPos(
                    self.chrome_hwnd,
                    win32con.HWND_TOP,
                    0, 0, rect.width(), rect.height(),
                    win32con.SWP_SHOWWINDOW | win32con.SWP_FRAMECHANGED
                )

                # Force window to be visible and active
                win32gui.ShowWindow(self.chrome_hwnd, win32con.SW_SHOW)
                win32gui.UpdateWindow(self.chrome_hwnd)

                print("✅ Chrome window embedded successfully!")
                self.status_label.setText("✅ Player loaded successfully!")
                QTimer.singleShot(2000, self.status_label.hide)
                self.signals.page_loaded.emit()

            else:
                print("⚠️ Chrome window not found for embedding")
                # Try again after a longer delay
                QTimer.singleShot(3000, self.embed_chrome_window)

        except Exception as e:
            print(f"⚠️ Error embedding Chrome window: {e}")
            import traceback
            traceback.print_exc()

    def refresh_browser(self):
        """Refresh the Chrome browser."""
        if self.chrome_hwnd:
            print("🔄 Refreshing Chrome browser...")
            # Send F5 key to Chrome window
            win32gui.SetForegroundWindow(self.chrome_hwnd)
            import win32api
            win32api.keybd_event(0x74, 0, 0, 0)  # F5 key down
            win32api.keybd_event(0x74, 0, win32con.KEYEVENTF_KEYUP, 0)  # F5 key up

    def open_devtools(self):
        """Open Chrome DevTools."""
        if self.chrome_hwnd:
            print("🔧 Opening Chrome DevTools...")
            # Send F12 key to Chrome window
            win32gui.SetForegroundWindow(self.chrome_hwnd)
            import win32api
            win32api.keybd_event(0x7B, 0, 0, 0)  # F12 key down
            win32api.keybd_event(0x7B, 0, win32con.KEYEVENTF_KEYUP, 0)  # F12 key up

    def close_player(self):
        """Close the player."""
        print("🔴 Closing Chrome player...")

        # Close Chrome process
        if self.chrome_process:
            try:
                self.chrome_process.terminate()
                self.chrome_process = None
                self.chrome_hwnd = None
            except:
                pass

        # Stop HTTP server
        if self.http_server:
            try:
                self.http_server.shutdown()
                self.http_server = None
            except:
                pass

        self.close()

    def eventFilter(self, obj, event):
        """Handle events for the browser container."""
        if obj == self.browser_container and event.type() == QEvent.Resize:
            # Resize Chrome window when container is resized
            if hasattr(self, 'chrome_hwnd') and self.chrome_hwnd:
                QTimer.singleShot(100, self.resize_chrome_window)
        return super().eventFilter(obj, event)

    def resize_chrome_window(self):
        """Resize Chrome window to match container size."""
        if hasattr(self, 'chrome_hwnd') and self.chrome_hwnd:
            try:
                rect = self.browser_container.rect()
                win32gui.SetWindowPos(
                    self.chrome_hwnd,
                    win32con.HWND_TOP,
                    0, 0, rect.width(), rect.height(),
                    win32con.SWP_NOZORDER | win32con.SWP_NOACTIVATE
                )
                print(f"🔧 Resized Chrome window to {rect.width()}x{rect.height()}")
            except Exception as e:
                print(f"⚠️ Error resizing Chrome window: {e}")

    def closeEvent(self, event):
        """Handle widget close event."""
        self.close_player()
        super().closeEvent(event)
