import os
import requests
import base64
import xml.etree.ElementTree as ET
from PySide6.QtCore import QObject, Signal
from pathlib import Path

class OSNDrm(QObject):
    # Signals for UI updates
    drm_keys_extracted = Signal(dict)  # DRM info
    drm_error = Signal(str)  # error message

    def __init__(self):
        super().__init__()
        self.setup_paths()

        # Initialize settings manager for proxy support
        self.settings_manager = None
        self.load_settings()

    def load_settings(self):
        """Load settings manager for proxy support"""
        try:
            from .settings_manager import SettingsManager
            self.settings_manager = SettingsManager()
            print("✅ OSN DRM Settings loaded")
        except Exception as e:
            print(f"❌ Error loading OSN DRM settings: {e}")
            self.settings_manager = None

    def get_proxy_config(self):
        """Get proxy configuration from settings"""
        try:
            if self.settings_manager:
                proxy_config = self.settings_manager.get_proxy_config()
                if proxy_config:
                    print(f"🌐 OSN DRM Using proxy configuration: {list(proxy_config.keys())}")
                return proxy_config
            return None
        except Exception as e:
            print(f"❌ Error getting OSN DRM proxy config: {e}")
            return None

    def setup_paths(self):
        """Setup DRM paths"""
        self.base_dir = Path(__file__).parent.parent.parent
        self.keys_dir = self.base_dir / "KEYS"
        self.keys_dir.mkdir(exist_ok=True)
        self.keys_file = self.keys_dir / "OSNPLUS_KEYS.txt"

    def get_pssh_from_mpd(self, mpd_url):
        """Extract PSSH from MPD file using multiple methods"""
        try:
            print(f"🔍 Downloading MPD file: {mpd_url}")
            # Get proxy configuration
            proxies = self.get_proxy_config()
            response = requests.get(mpd_url, proxies=proxies)
            response.raise_for_status()

            root = ET.fromstring(response.content)
            namespaces = {
                'mpd': 'urn:mpeg:dash:schema:mpd:2011',
                'cenc': 'urn:mpeg:cenc:2013',
                'mas': 'urn:marlin:mas:1-0:services:schemas:mpd'
            }

            print(f"📄 MPD file downloaded successfully, searching for PSSH...")

            # Method 1: Look for ContentProtection elements with Widevine scheme
            for content_protection in root.findall('.//mpd:ContentProtection', namespaces):
                scheme_id = content_protection.get('schemeIdUri', '')
                print(f"🔍 Found ContentProtection with scheme: {scheme_id}")

                if 'widevine' in scheme_id.lower() or 'edef8ba9-79d6-4ace-a3c8-27dcd51d21ed' in scheme_id.lower():
                    # Look for PSSH data in cenc:pssh element
                    pssh_element = content_protection.find('.//cenc:pssh', namespaces)
                    if pssh_element is not None and pssh_element.text:
                        pssh = pssh_element.text.strip()
                        print(f"✅ Found PSSH via Method 1: {pssh[:50]}...")
                        return pssh

                    # Look for PSSH data in mas:pssh element
                    pssh_element = content_protection.find('.//mas:pssh', namespaces)
                    if pssh_element is not None and pssh_element.text:
                        pssh = pssh_element.text.strip()
                        print(f"✅ Found PSSH via Method 1 (mas): {pssh[:50]}...")
                        return pssh

            # Method 2: Look for any pssh element regardless of namespace
            for pssh_element in root.iter():
                if 'pssh' in pssh_element.tag.lower() and pssh_element.text:
                    pssh = pssh_element.text.strip()
                    print(f"✅ Found PSSH via Method 2: {pssh[:50]}...")
                    return pssh

            # Method 3: Search in text content for base64 PSSH pattern
            mpd_text = response.text
            import re
            pssh_pattern = r'<[^>]*pssh[^>]*>([A-Za-z0-9+/=]+)</[^>]*pssh[^>]*>'
            matches = re.findall(pssh_pattern, mpd_text, re.IGNORECASE)
            if matches:
                pssh = matches[0].strip()
                print(f"✅ Found PSSH via Method 3 (regex): {pssh[:50]}...")
                return pssh

            print(f"❌ No PSSH found in MPD file")
            return None

        except Exception as e:
            print(f"❌ Error extracting PSSH: {str(e)}")
            self.drm_error.emit(f"Error extracting PSSH: {str(e)}")
            return None

    def get_kid_from_mpd(self, mpd_url):
        """Extract KID from MPD file"""
        try:
            # Get proxy configuration
            proxies = self.get_proxy_config()
            response = requests.get(mpd_url, proxies=proxies)
            response.raise_for_status()

            root = ET.fromstring(response.content)
            namespaces = {'mpd': 'urn:mpeg:dash:schema:mpd:2011', 'cenc': 'urn:mpeg:cenc:2013'}

            # Look for ContentProtection elements with default_KID
            for content_protection in root.findall('.//mpd:ContentProtection', namespaces):
                default_kid = content_protection.get('{urn:mpeg:cenc:2013}default_KID')
                if default_kid:
                    # Remove hyphens and return
                    return default_kid.replace('-', '')

            return None

        except Exception as e:
            self.drm_error.emit(f"Error extracting KID: {str(e)}")
            return None

    def extract_drm_keys(self, license_url, pssh, drm_token, headers=None):
        """Extract DRM keys using license server"""
        try:
            # This is a placeholder for DRM key extraction
            # In a real implementation, you would use a library like pywidevine
            # to extract the actual keys from the license server

            print(f"🔓 Extracted 0 DRM keys for player")

            # For now, we'll return a placeholder structure
            drm_info = {
                'pssh': pssh,
                'license_url': license_url,
                'drm_token': drm_token,
                'kid': None,
                'key': None,
                'formatted_key': None
            }

            # Try to extract KID from PSSH if available
            if pssh:
                try:
                    # Decode PSSH to extract KID (simplified)
                    pssh_data = base64.b64decode(pssh)
                    # This is a very basic extraction - in reality you'd need proper PSSH parsing
                    drm_info['kid'] = "placeholder_kid"
                    drm_info['key'] = "placeholder_key"
                    drm_info['formatted_key'] = f"{drm_info['kid']}:{drm_info['key']}"
                except:
                    pass

            # Save keys to file
            self.save_keys_to_file(drm_info)

            self.drm_keys_extracted.emit(drm_info)
            return drm_info

        except Exception as e:
            self.drm_error.emit(f"Error extracting DRM keys: {str(e)}")
            return None

    def save_keys_to_file(self, drm_info):
        """Save DRM keys to keys file"""
        try:
            if not drm_info.get('formatted_key'):
                return

            # Read existing keys
            existing_keys = set()
            if self.keys_file.exists():
                with open(self.keys_file, 'r') as f:
                    existing_keys = set(line.strip() for line in f if line.strip())

            # Add new key if not already present
            new_key = drm_info['formatted_key']
            if new_key not in existing_keys:
                with open(self.keys_file, 'a') as f:
                    f.write(f"{new_key}\n")

        except Exception as e:
            self.drm_error.emit(f"Error saving keys: {str(e)}")

    def process_content_drm(self, content_id, stream_id, access_token):
        """Process DRM for content (movie or episode)"""
        try:
            # Get stream details first
            stream_data = self.get_stream_details(content_id, stream_id, access_token)
            if not stream_data:
                return None

            mpd_url = stream_data.get('mpd_url')
            license_url = stream_data.get('license_url')
            drm_token = stream_data.get('drm_token')

            if not all([mpd_url, license_url, drm_token]):
                self.drm_error.emit("Missing DRM information from stream data")
                return None

            # Extract PSSH and KID from MPD
            pssh = self.get_pssh_from_mpd(mpd_url)
            kid = self.get_kid_from_mpd(mpd_url)

            if not pssh:
                self.drm_error.emit("Could not extract PSSH from MPD file")
                return None

            # Extract DRM keys
            drm_info = self.extract_drm_keys(license_url, pssh, drm_token)
            if drm_info:
                drm_info['kid'] = kid
                drm_info['mpd_url'] = mpd_url

            return drm_info

        except Exception as e:
            self.drm_error.emit(f"Error processing DRM: {str(e)}")
            return None

    def get_stream_details(self, content_id, stream_id, access_token):
        """Get stream details for DRM processing"""
        try:
            url = "https://api.osnplus.com/osn/media/v1/stream"

            headers = {
                'accept': '*/*',
                'accept-language': 'en-US,en;q=0.9',
                'access_token': access_token,
                'client_platform': 'web-osn',
                'client_version': '1.1.1',
                'content-type': 'application/json',
                'device_unique_identifier': '35b4ffb9-654f-4fca-a132-8ed1011d1927',
                'language': 'en',
                'origin': 'https://osnplus.com',
                'referer': 'https://osnplus.com/',
            }

            payload = {
                "contentId": content_id,
                "streamId": stream_id
            }

            print(f"🔄 STREAM DETAILS REQUEST:")
            print(f"🔄 URL: {url}")
            print(f"🔄 Payload: {payload}")

            # Get proxy configuration
            proxies = self.get_proxy_config()
            response = requests.post(url, headers=headers, json=payload, proxies=proxies)

            if response.status_code == 200:
                data = response.json()

                # Print detailed response for debugging
                print(f"📋 FULL STREAM DETAILS RESPONSE:")
                print(f"📋 Status Code: {response.status_code}")
                print(f"📋 Response Keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")

                if isinstance(data, dict):
                    for key, value in data.items():
                        if isinstance(value, (dict, list)) and len(str(value)) > 300:
                            print(f"📋 {key}: [Large object - {type(value).__name__} with {len(value) if hasattr(value, '__len__') else 'unknown'} items]")
                            # Print first few items if it's a list
                            if isinstance(value, list) and len(value) > 0:
                                print(f"📋   First item: {value[0]}")
                            elif isinstance(value, dict):
                                print(f"📋   Dict keys: {list(value.keys())}")
                        else:
                            print(f"📋 {key}: {value}")

                mpd_url = data.get('manifestUrl')
                license_url = data.get('drmLicenseServers', {}).get('widevineLicenseUrl')
                drm_token = data.get('drmToken')
                cdn_token = data.get('cdnToken')

                # Add CDN token to MPD URL if available
                if mpd_url and cdn_token:
                    mpd_url = f"{mpd_url}?token={cdn_token}"

                return {
                    'mpd_url': mpd_url,
                    'license_url': license_url,
                    'drm_token': drm_token,
                    'cdn_token': cdn_token
                }
            else:
                print(f"❌ Failed to get stream details: {response.status_code}")
                print(f"❌ Response: {response.text}")
                self.drm_error.emit(f"Failed to get stream details. Status: {response.status_code}")
                return None

        except Exception as e:
            self.drm_error.emit(f"Error getting stream details: {str(e)}")
            return None

    def load_existing_keys(self):
        """Load existing keys from file"""
        try:
            if not self.keys_file.exists():
                return []

            with open(self.keys_file, 'r') as f:
                keys = [line.strip() for line in f if line.strip()]

            return keys

        except Exception as e:
            self.drm_error.emit(f"Error loading keys: {str(e)}")
            return []
