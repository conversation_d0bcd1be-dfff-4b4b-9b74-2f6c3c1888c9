﻿LOG 2025/05/29
Save Path: F:\TEEFA\MBC\shahid_template\binaries\Logs
Task Start: 2025/05/29 03:16:10
Task CommandLine: F:\TEEFA\MBC\shahid_template\binaries\N_m3u8DL-RE.exe https://mbc-aws-ksa.mncdn.com/out/v1/a0503e80caf74e4ca774e9a4ca1f5936/22166333d51a4d528478144cc30fc048/65aee16b2775429e8add39c54f7b6995/index.mpd?aws.manifestfilter=video_height:144-1080;video_codec:H264&video_height=144-1080&video_codec=H264 -sv id=4:for=best --select-audio lang=ar:for=all --sub-only=false --tmp-dir F:\TEEFA\MBC\shahid_template\cache\episode_0 --save-dir F:\TEEFA\MBC\shahid_template\cache\episode_0 --save-name "<PERSON><PERSON> El Adad ++.S03.E01.468p.SHAHID.VIP.WEB-DL.H264.AAC" --base-url https://mbc-aws-ksa.mncdn.com/out/v1/a0503e80caf74e4ca774e9a4ca1f5936/22166333d51a4d528478144cc30fc048/65aee16b2775429e8add39c54f7b6995/ --decryption-binary-path=F:\TEEFA\MBC\shahid_template\binaries\mp4decrypt.exe --key-text-file=F:\TEEFA\MBC\shahid_template\KEYS\KEYS.txt --log-level INFO --binary-merge --thread-count 16 --download-retry-count 3 --sub-format SRT -mt

03:16:10.200 INFO : N_m3u8DL-RE (Beta version) 20230628
03:16:10.207 EXTRA: ffmpeg => F:\TEEFA\MBC\shahid_template\binaries\ffmpeg.exe
03:16:10.207 INFO : Loading URL: https://mbc-aws-ksa.mncdn.com/out/v1/a0503e80caf74e4ca774e9a4ca1f5936/22166333d51a4d528478144cc30fc048/65aee16b2775429e8add39c54f7b6995/index.mpd?aws.manifestfilter=video_height:144-1080;video_codec:H264&video_height=144-1080&video_codec=H264
03:16:10.421 INFO : Content Matched: Dynamic Adaptive Streaming over HTTP
03:16:10.421 INFO : Parsing streams...
03:16:10.494 WARN : Writing meta json
03:16:10.509 INFO : Extracted, there are 40 streams, with 10 basic streams, 6 audio streams, 24 subtitle streams
03:16:10.509 INFO : Vid *CENC 1920x1080 | 2687 Kbps | 2 | 25 | avc1.640028 | 374 Segments | ~37m21s
03:16:10.510 INFO : Vid *CENC 1280x720 | 1512 Kbps | 3 | 25 | avc1.4D401F | 374 Segments | ~37m21s
03:16:10.510 INFO : Vid *CENC 1024x576 | 1168 Kbps | 1 | 25 | avc1.4D401F | 374 Segments | ~37m21s
03:16:10.511 INFO : Vid *CENC 832x468 | 707 Kbps | 4 | 25 | avc1.4D401F | 374 Segments | ~37m21s
03:16:10.511 INFO : Vid *CENC 640x360 | 528 Kbps | 5 | 25 | avc1.4D401E | 374 Segments | ~37m21s
03:16:10.511 INFO : Vid *CENC 512x288 | 421 Kbps | 6 | 25 | avc1.4D401E | 374 Segments | ~37m21s
03:16:10.511 INFO : Vid *CENC 448x252 | 374 Kbps | 7 | 25 | avc1.4D401E | 374 Segments | ~37m21s
03:16:10.512 INFO : Vid *CENC 448x252 | 333 Kbps | 8 | 25 | avc1.4D401E | 374 Segments | ~37m21s
03:16:10.512 INFO : Vid *CENC 256x144 | 219 Kbps | 9 | 25 | avc1.4D401E | 374 Segments | ~37m21s
03:16:10.512 INFO : Vid *CENC 256x144 | 149 Kbps | 10 | 25 | avc1.4D401E | 374 Segments | ~37m21s
03:16:10.513 INFO : Aud *CENC 11 | 128 Kbps | mp4a.40.2 | ar | 2CH | 374 Segments | ~37m21s
03:16:10.513 INFO : Aud *CENC 12 | 127 Kbps | mp4a.40.2 | qaa | 2CH | 374 Segments | ~37m21s
03:16:10.513 INFO : Aud *CENC 13 | 127 Kbps | mp4a.40.2 | qab | 2CH | 374 Segments | ~37m21s
03:16:10.514 INFO : Aud *CENC 14 | 127 Kbps | mp4a.40.2 | qac | 2CH | 374 Segments | ~37m21s
03:16:10.514 INFO : Aud *CENC 15 | 127 Kbps | mp4a.40.2 | qad | 2CH | 374 Segments | ~37m21s
03:16:10.521 INFO : Aud *CENC 16 | 127 Kbps | mp4a.40.2 | qae | 2CH | 374 Segments | ~37m21s
03:16:10.538 INFO : Sub 17 | en | stpp | 374 Segments | ~37m21s
03:16:10.539 INFO : Sub 18 | ar | stpp | 374 Segments | ~37m21s
03:16:10.539 INFO : Sub 19 | ar-fn | stpp | 374 Segments | ~37m21s
03:16:10.540 INFO : Sub 20 | en-fn | stpp | 374 Segments | ~37m21s
03:16:10.540 INFO : Sub 21 | fr-fn | stpp | 374 Segments | ~37m21s
03:16:10.541 INFO : Sub 22 | ms | stpp | 374 Segments | ~37m21s
03:16:10.541 INFO : Sub 23 | tr | stpp | 374 Segments | ~37m21s
03:16:10.541 INFO : Sub 24 | no | stpp | 374 Segments | ~37m21s
03:16:10.542 INFO : Sub 25 | pl | stpp | 374 Segments | ~37m21s
03:16:10.542 INFO : Sub 26 | nl | stpp | 374 Segments | ~37m21s
03:16:10.542 INFO : Sub 27 | hi | stpp | 374 Segments | ~37m21s
03:16:10.542 INFO : Sub 28 | es | stpp | 374 Segments | ~37m21s
03:16:10.543 INFO : Sub 29 | ja | stpp | 374 Segments | ~37m21s
03:16:10.543 INFO : Sub 30 | pt | stpp | 374 Segments | ~37m21s
03:16:10.543 INFO : Sub 31 | ru | stpp | 374 Segments | ~37m21s
03:16:10.543 INFO : Sub 32 | ko | stpp | 374 Segments | ~37m21s
03:16:10.544 INFO : Sub 33 | ml | stpp | 374 Segments | ~37m21s
03:16:10.544 INFO : Sub 34 | el | stpp | 374 Segments | ~37m21s
03:16:10.545 INFO : Sub 35 | ct | stpp | 374 Segments | ~37m21s
03:16:10.546 INFO : Sub 36 | de | stpp | 374 Segments | ~37m21s
03:16:10.547 INFO : New version detected! v0.3.0-beta
03:16:10.547 INFO : Sub 37 | id | stpp | 374 Segments | ~37m21s
03:16:10.548 INFO : Sub 38 | bn | stpp | 374 Segments | ~37m21s
03:16:10.548 INFO : Sub 39 | tl | stpp | 374 Segments | ~37m21s
03:16:10.548 INFO : Sub 40 | it | stpp | 374 Segments | ~37m21s
03:16:10.549 EXTRA: VideoFilter => GroupIdReg: 4 For: best
03:16:10.549 EXTRA: AudioFilter => LanguageReg: ar For: all
03:16:10.549 INFO : Parsing streams...
03:16:10.550 INFO : Selected streams:
03:16:10.550 INFO : Vid *CENC 832x468 | 707 Kbps | 4 | 25 | avc1.4D401F | 374 Segments | ~37m21s
03:16:10.550 INFO : Aud *CENC 11 | 128 Kbps | mp4a.40.2 | ar | 2CH | 374 Segments | ~37m21s
03:16:10.551 WARN : Writing meta json
03:16:10.554 INFO : Save Name: Kamel El Adad ++.S03.E01.468p.SHAHID.VIP.WEB-DL.H264.AAC
03:16:10.565 INFO : Start downloading...Vid 832x468 | 707 Kbps | 4 | 25 | avc1.4D401F
03:16:10.565 INFO : Start downloading...Aud 11 | 128 Kbps | mp4a.40.2 | ar | 2CH
03:16:10.636 WARN : Type: cenc
03:16:10.637 WARN : PSSH(WV): CAESEFADgUQCzEjQk0omiWCbYhgaBWluc3lzIiQ1MDAzODE0NC0wMmNjLTQ4ZDAtOTM0YS0yNjg5NjA5YjYyMTgqAlNEMgA=
03:16:10.637 WARN : KID: 5003814402cc48d0934a2689609b6218
03:16:10.637 INFO : Trying to search for KEY from text file...
03:16:10.638 INFO : OK 5003814402cc48d0934a2689609b6218:3ae46f3961d7ea4ccb8205ccfab89e3c
03:16:10.638 WARN : Reading media info...
03:16:10.701 WARN : Type: cenc
03:16:10.701 WARN : PSSH(WV): CAESEFADgUQCzEjQk0omiWCbYhgaBWluc3lzIiQ1MDAzODE0NC0wMmNjLTQ4ZDAtOTM0YS0yNjg5NjA5YjYyMTgqAlNEMgA=
03:16:10.702 WARN : KID: 5003814402cc48d0934a2689609b6218
03:16:10.702 INFO : Trying to search for KEY from text file...
03:16:10.703 INFO : OK 5003814402cc48d0934a2689609b6218:3ae46f3961d7ea4ccb8205ccfab89e3c
03:16:10.703 WARN : Reading media info...
03:16:11.155 INFO : [0x1]: Audio, aac (mp4a)
03:16:11.155 INFO : [0x1]: Video, h264 (avc1), 832x468
03:16:33.395 INFO : Binary merging...
03:16:33.524 INFO : Decrypting...
03:17:02.062 INFO : Binary merging...
03:17:02.288 INFO : Decrypting...
03:17:03.117 INFO : Done
