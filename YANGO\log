✅ pywidevine imported successfully
✅ Successfully loaded embedded image: YANGOTO
✅ YANGO sidebar logo loaded from embedded images
Unknown property transform
Unknown property transform
Unknown property transform
Unknown property transform
Unknown property transform
Unknown property transform
Unknown property transform
Unknown property transform
Unknown property transform
✅ Successfully loaded embedded image: YANGO
✅ YANG<PERSON> main logo loaded from embedded images
✅ Found cookies file at: cookies.txt
✅ Loaded 38 cookies from cookies.txt
✅ Settings loaded in API
🔧 Using development mode base path: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO
🔍 Resource path for 'cache': C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\cache
🔧 Using development mode base path: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO
🔍 Resource path for 'downloads': C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\downloads
🔧 Using development mode base path: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO
🔍 Resource path for 'KEYS': C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\KEYS
🔧 Using development mode base path: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO
🔍 Resource path for 'binaries/mkvmerge.exe': C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\binaries/mkvmerge.exe
🔧 Using development mode base path: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO
🔍 Resource path for 'binaries/N_m3u8DL-RE.exe': C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\binaries/N_m3u8DL-RE.exe
🔧 Using development mode base path: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO
🔍 Resource path for 'binaries/aria2c.exe': C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\binaries/aria2c.exe
🔧 Using development mode base path: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO
🔍 Resource path for 'binaries/mp4decrypt.exe': C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\binaries/mp4decrypt.exe
🔧 Using development mode base path: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO
🔍 Resource path for 'binaries/ffmpeg.exe': C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\binaries/ffmpeg.exe
🔧 Setup paths:
   📁 Base dir: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO
   📁 Cache dir: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\cache
   📁 Downloads dir: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\downloads
   🔑 Keys file: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\KEYS\KEYS.txt
   🛠️ N_m3u8DL-RE: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\binaries\N_m3u8DL-RE.exe
   🛠️ aria2c: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\binaries\aria2c.exe
   🛠️ mkvmerge: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\binaries\mkvmerge.exe
   🛠️ mp4decrypt: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\binaries\mp4decrypt.exe
   🛠️ ffmpeg: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\binaries\ffmpeg.exe
🔍 Checking tools:
   N_m3u8DL-RE exists: True
   aria2c exists: True
   mkvmerge exists: True
   mp4decrypt exists: True
   Keys file exists: True
✅ Settings loaded in downloader
✅ Downloader signals connected
✅ Connected search_button
✅ Found and configured url_input
✅ Connected recent_combo
✅ Quality set to: 4K (Using 4K headers with iOS device simulation)
⚠️ Could not update original script quality: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\OSN_NEW\\OSN_NEW\\YANGO\\YANGO PLAY.py'
✅ Quality changed to: 4K
✅ Settings saved successfully
✅ Saved new default quality to settings: 4K
✅ Loaded default quality from settings: 4K
✅ Quality set to: 4K (Using 4K headers with iOS device simulation)
⚠️ Could not update original script quality: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\OSN_NEW\\OSN_NEW\\YANGO\\YANGO PLAY.py'
✅ Connected quality_combo and set initial quality to: 4K
✅ Connected clear_button
✅ Found config file: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\config\recent_urls.json
📂 Loaded 6 recent URLs from C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\config\recent_urls.json
✅ Connected play_button
✅ Connected continue_button
✅ UI connections setup completed
✅ Found config file: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\config\recent_urls.json
📂 Loaded 6 recent URLs from C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\config\recent_urls.json
✅ Using existing content_tabs from widgets
🔄 Cleared existing tabs
📏 Downloads table row height set to: 45px
✅ Downloads tab setup completed with organized design
✅ All tabs setup completed (hidden until search)
🔗 Connecting API signals...
✅ Connected error_occurred signal
✅ Connected movie_found signal
✅ Connected series_found signal
✅ Connected streams_found signal
✅ All API signals connected successfully
Successfully loaded sidebar logo from: images-osn/images/OSN+.png
✅ OSN+ main logo loaded from: images-osn/images/OSN+.png
✅ OSN Settings loaded in API
🍪 Loading cookies from: cookies-osn.txt
🔍 Total lines in cookies file: 28
🔍 Line 1: # Netscape HTTP Cookie File
...
🔍 Line 2: # http://curl.haxx.se/rfc/cookie_spec.html
...
🔍 Line 3: # This file was generated by Cookie-Editor
...
🔍 Line 4: osnplus.com  FALSE   /       FALSE   **********      auth    %7B%22to...
✅ Found auth line at line 4
🔍 Parts count: 7
🔍 Auth cookie length: 1328
🔍 Decoded length: 1180
✅ Successfully parsed JSON on first try
🔍 Auth data keys: ['token', 'refreshToken', 'expiresAt', 'newUser', 'signupDetails', 'userId', 'loggedIn', 'failureMessage', 'isAccountDisabled', 'failureCode']
✅ Found refresh token!
🍪 Loading cookies from: cookies-osn.txt
🔍 Total lines in cookies file: 28
🔍 Line 1: # Netscape HTTP Cookie File
...
✅ OSN DRM Settings loaded
🔍 Line 2: # http://curl.haxx.se/rfc/cookie_spec.html
...
✅ Connected search_button
🔍 Line 3: # This file was generated by Cookie-Editor
...
✅ Setup main search autocomplete
🔍 Line 4: osnplus.com  FALSE   /       FALSE   **********      auth    %7B%22to...
✅ Found url_input
✅ Found auth line at line 4
✅ Connected recent_combo
🔍 Parts count: 7
✅ Connected clear_button
🔍 Auth cookie length: 1328
✅ UI connections setup completed
🔍 Decoded length: 1180
✅ Successfully parsed JSON on first try
📂 Loaded 2 recent URLs for filtering
🔍 Auth data keys: ['token', 'refreshToken', 'expiresAt', 'newUser', 'signupDetails', 'userId', 'loggedIn', 'failureMessage', 'isAccountDisabled', 'failureCode']
✅ Enhanced search system initialized
✅ Found refresh token!
🔍 Combo filtered to 0 items for query: 'Select from recent searches...'
🔍 Combo filtered to 1 items for query: '10343 - Men 30 Sana (2015)'
📂 Loaded 2 recent URLs from C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\modules\OSN\..\config\recent_urls.json (searchable)
🔄 Updated autocomplete with 6 suggestions
⚠️ Sound file not found: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\modules\binaries\alert.wav
✅ Using existing content_tabs from UI
🔄 Cleared existing tabs
Unknown property transform
Unknown property transform
Unknown property transform
Unknown property transform
Unknown property transform
Unknown property transform
✅ All tabs setup completed (hidden until search)
Unknown property transform
Unknown property transform
Unknown property transform
Unknown property transform
Unknown property transform
Unknown property transform
Unknown property transform
Unknown property transform
Unknown property transform
Unknown property transform
Unknown property transform
Unknown property transform
✅ OSN original application integrated successfully
✅ ShahidAPI imported successfully
❌ Error importing ShahidDRM: No module named 'pywidevine.cdm.formats'; 'pywidevine.cdm' is not a package
✅ ShahidDownloader imported successfully
✅ ShahidUI imported successfully
❌ CEF Python not available: No module named 'cefpython3'
✅ ShahidPlayer imported successfully
✅ SettingsManager imported successfully
✅ ShahidUiMainWindow imported successfully
✅ ShahidUIFunctions imported successfully
✅ ShahidSettings imported successfully
✅ ShahidAppFunctions imported successfully
✅ Shahid token loaded from: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\binaries\shahid_token.txt
[SETTINGS] Config directory: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\modules\config
[SETTINGS] Settings file: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\modules\config\settings.json
[SETTINGS] Settings file exists: 737 bytes
[SETTINGS] Cleaned proxy section to ensure valid structure
[SETTINGS] Cleaned ui section to ensure valid structure
[SETTINGS] Cleaned download section to ensure valid structure
[SETTINGS] Cleaned idm section to ensure valid structure
[SETTINGS] Successfully loaded settings from C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\modules\config\settings.json
File C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\modules\proxy.txt not found.
Downloader initialized with base directory: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\modules
[IDM] Found IDM at: C:\Program Files (x86)\Internet Download Manager\IDMan.exe
Cache directory: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\modules\cache
Downloads directory: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\modules\downloads
Keys file: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\modules\KEYS\KEYS.txt
IDM path: C:\Program Files (x86)\Internet Download Manager\IDMan.exe
Player initialized with base directory: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\modules
Player directory: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\modules\player
✅ Shahid components initialized
Error loading sidebar logo image from all paths
Error loading logo image from all paths
QLayout: Attempting to add QLayout "" to QWidget "widgets", which already has a layout
QLayout: Attempting to add QLayout "" to QWidget "new_page", which already has a layout
✅ Shahid UI handler initialized
✅ Applied original Shahid theme
✅ Shahid styling applied
✅ Set default page to home
✅ Hidden original Shahid left menu
✅ Hidden original Shahid extra left box
✅ Hidden original Shahid extra top menu
✅ Hidden original Shahid title bar
✅ Hidden original Shahid window controls
✅ Hidden original Shahid bottom bar
✅ Hidden original Shahid credits
✅ Hidden original Shahid version
✅ Hidden original Shahid size grip
✅ Original Shahid sidebar and extra components hidden
✅ Removed bgApp borders
✅ Removed app margins
✅ Removed pages container borders
✅ Removed content box borders
✅ All borders and separators removed
✅ Original Shahid UI setup completed
✅ Connected btn_home
✅ Connected btn_widgets
✅ Connected btn_new
✅ Connected btn_save
✅ Connected Shahid search signals
✅ Connected Shahid token signals
✅ Connected Shahid clear signals
✅ Shahid signals connected
🔍 Checking token...
🔍 Token value: eyJhbGciOiJIUzI1NiJ9.eyJjYWNoZSI6IlVzZXJfODhmMTFlYjdjOTc0NDI3MzllNWFjYzE4ZTMyMDE0YWMiLCJ1aWQiOiJhcHIxU2hhaGlkRW5RWUpQTUsyTmxCb25xVnZqejJFV3QiLCJkaWQiOiJXZWIiLCJzdWJpZCI6Ijg4ZjExZWI3Yzk3NDQyNzM5ZTVhY2MxOGUzMjAxNGFjIiwic3ViIjoic2hhaGlkLXRva2VuLWVuY29kZSIsImlzcyI6InNoYWhpZC10b2tlbi1lbmNvZGUiLCJpYXQiOjE3NDgxOTU5MTksImV4cCI6MTc3OTczMTkxOX0.JGOiKbYb50dpDsAi79fjoaf_qIr0cW6-CsTZbVoDK9s
🔍 Has shahid_ui: True
🔍 Has token_frame: True
✅ Token found and loaded successfully. Token frame hidden.
🔍 Checking token...
🔍 Token value: eyJhbGciOiJIUzI1NiJ9.eyJjYWNoZSI6IlVzZXJfODhmMTFlYjdjOTc0NDI3MzllNWFjYzE4ZTMyMDE0YWMiLCJ1aWQiOiJhcHIxU2hhaGlkRW5RWUpQTUsyTmxCb25xVnZqejJFV3QiLCJkaWQiOiJXZWIiLCJzdWJpZCI6Ijg4ZjExZWI3Yzk3NDQyNzM5ZTVhY2MxOGUzMjAxNGFjIiwic3ViIjoic2hhaGlkLXRva2VuLWVuY29kZSIsImlzcyI6InNoYWhpZC10b2tlbi1lbmNvZGUiLCJpYXQiOjE3NDgxOTU5MTksImV4cCI6MTc3OTczMTkxOX0.JGOiKbYb50dpDsAi79fjoaf_qIr0cW6-CsTZbVoDK9s
🔍 Has shahid_ui: True
🔍 Has token_frame: True
✅ Token found and loaded successfully. Token frame hidden.
✅ Shahid VIP widget initialized successfully
✅ Shahid VIP original application integrated successfully
✅ Quick access buttons connected
✅ Settings buttons connected
[SETTINGS] Config directory: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\modules\config
[SETTINGS] Settings file: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\modules\config\settings.json
[SETTINGS] Settings file exists: 737 bytes
[SETTINGS] Cleaned proxy section to ensure valid structure
[SETTINGS] Cleaned ui section to ensure valid structure
[SETTINGS] Cleaned download section to ensure valid structure
[SETTINGS] Cleaned idm section to ensure valid structure
[SETTINGS] Successfully loaded settings from C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\modules\config\settings.json
✅ Shahid settings manager loaded
✅ Settings save buttons ready for connection
✅ Settings management setup completed
C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\main.py:956: DeprecationWarning: 'exec_' will be removed in the future. Use 'exec' instead.
  sys.exit(app.exec_())
Unknown property transform
Unknown property transform
Unknown property transform
⭐ Switched to Shahid VIP module
⭐ Quick access to Shahid VIP
C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\main.py:929: DeprecationWarning: Function: 'QMouseEvent.globalPos() const' is marked as deprecated, please check the documentation for more information.
  self.dragPos = event.globalPos()
Mouse click: LEFT CLICK
✅ Shahid token loaded from: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\binaries\shahid_token.txt
File C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\modules\proxy.txt not found.
[API] Input is a numeric ID: 4246631591527
[UI] Successfully extracted content ID: 4246631591527 from input: 4246631591527
[UI] Searching for content with input: 4246631591527

[API] Getting content details for: 4246631591527
[API] Input is a numeric ID: 4246631591527
[API] Trying to get content as SERIES with ID: 4246631591527
[API] Content not found as SERIES, trying as MOVIE with ID: 4246631591527
[API] Content has seasons but was returned from movie endpoint, treating as SERIES
API Response:
{
  "responseCode": 200,
  "success": true,
  "currentDate": "2025-06-14T01:06:06+0000",
  "country": "SAU",
  "productModel": {
    "id": 4246631591527,
    "title": "Harb El Gibali",
    "description": "In the heart of Al-Hareer, Jamaliya, two influential families collide in a sweeping tale of love, power, and vengeance, where every bond conceals a bitter betrayal.",
    "shortDescription": "",
    "image": {
      "thumbnailImage": "https://shahid.mbc.net/mediaObject/dd73b773-2fe4-4a29-a74f-3423b10b2949?height={height}&width={width}&croppingPoint={croppingPoint}&version=1",
      "posterImage": "https://shahid.mbc.net/mediaObject/181bbabf-1b1f-4105-9453-35b86c9ed6e1?height={height}&width={width}&croppingPoint={croppingPoint}&version=1",
      "heroSliderImage": "https://shahid.mbc.net/mediaObject/c3d14d3a-5837-4977-a5c2-09200ea418de?height={height}&width={width}&croppingPoint={croppingPoint}&version=1",
      "landscapeClean": "https://shahid.mbc.net/mediaObject/e15b099d-f807-4a31-8da4-8d4a4b770fa8?height={height}&width={width}&croppingPoint={croppingPoint}&version=1",
      "posterClean": "https://shahid.mbc.net/mediaObject/6eb6f941-a651-4428-836a-ebc1c4f12012?height={height}&width={width}&croppingPoint={croppingPoint}&version=1",
      "posterHero": "https://shahid.mbc.net/mediaObject/3ce6e997-540b-4a4e-b174-a96ff995c69c?height={height}&width={width}&croppingPoint={croppingPoint}&version=1",
      "posterTop10": "",
      "posterBundle": "",
      "onboardingCategoryImage": "",
      "squareImage": ""
    },
    "thumbnailImage": "https://shahid.mbc.net/mediaObject/dd73b773-2fe4-4a29-a74f-3423b10b2949?height={height}&width={width}&croppingPoint={croppingPoint}&version=1",
    "mainImage": "https://shahid.mbc.net/mediaObject/c3d14d3a-5837-4977-a5c2-09200ea418de?height={height}&width={width}&croppingPoint={croppingPoint}&version=1",
    "createdDate": "2025-05-18T00:00:00+0000",
    "modifiedDate": "2025-06-13T11:33:08+0000",
    "productionDate": null,
    "releaseDate": null,
    "productUrl": {
      "url": "https://shahid.mbc.net/en/series/harb-el-gibali/series-4246631591527",
      "shortenUrl": "https://shdl.ink/3ukzRK"
    },
    "productUrls": [
      {
        "url": "https://shahid.mbc.net/fr/series/harb-al-gibali/series-4246631591527",
        "shortenUrl": "https://shdl.ink/3lEstV"
      },
      {
        "url": "https://shahid.mbc.net/ar/series/حرب-الجبالي/series-4246631591527",
        "shortenUrl": "https://shdl.ink/vrIGIW"
      },
      {
        "url": "https://shahid.mbc.net/en/series/harb-el-gibali/series-4246631591527",
        "shortenUrl": "https://shdl.ink/3ukzRK"
      }
    ],
    "dialect": {
      "id": 7915,
      "title": "Egyptian"
    },
    "genres": [
      {
        "id": 7863,
        "title": "Social"
      },
      {
        "id": 7876,
        "title": "Drama"
      },
      {
        "id": 49923064877829,
        "title": "Egyptian"
      }
    ],
    "microGenres": [],
    "channels": [
      {
        "id": 12438,
        "title": "Exclusive"
      }
    ],
    "displayMetadata": [],
    "decades": null,
    "persons": [
      {
        "id": 2785,
        "firstName": "Ahmed",
        "lastName": "Rizk",
        "fullName": "Ahmed Rizk",
        "rank": 1
      },
      {
        "id": 164156,
        "firstName": "Ryad",
        "lastName": "El Khouly",
        "fullName": "Ryad El Khouly",
        "rank": 2
      },
      {
        "id": 170350,
        "firstName": "Ahmed",
        "lastName": "Khaled Saleh",
        "fullName": "Ahmed Khaled Saleh",
        "rank": 3
      },
      {
        "id": 2583,
        "firstName": "Sawsan",
        "lastName": "Badr",
        "fullName": "Sawsan Badr",
        "rank": 4
      },
      {
        "id": 124928,
        "firstName": "Heba",
        "lastName": "Magdi",
        "fullName": "Heba Magdi",
        "rank": 5
      },
      {
        "id": 178674,
        "firstName": "Nesreen",
        "lastName": "Amin",
        "fullName": "Nesreen Amin",
        "rank": 6
      }
    ],
    "writers": [],
    "directors": [
      {
        "id": 173896,
        "firstName": "Mohamed",
        "lastName": "Osama",
        "fullName": "Mohamed Osama",
        "rank": 7
      }
    ],
    "coachs": [],
    "contestants": [],
    "judges": [],
    "presenters": [],
    "producers": [],
    "pricingPlans": [
      {
        "id": 1488015117,
        "name": null,
        "type": "BROWSE_ONLY",
        "planId": 0,
        "startDate": "2025-05-06T00:00:00+0000",
        "endDate": null,
        "offerEndDate": "2072-08-28T00:00:00+0000",
        "availability": {
          "days": 0,
          "hours": 0,
          "minutes": 0,
          "status": "NONE",
          "plus": true
        },
        "kidsAllowed": false,
        "kidsAllowedAge": -1,
        "downloadSetting": {
          "allowDownload": false,
          "name": null,
          "rental": null,
          "playback": null,
          "licenseExpiry": null
        }
      }
    ],
    "subscriptionPackages": [
      {
        "subscriptionId": "1",
        "name": "SHAHID_VIP",
        "isDefault": true
      },
      {
        "subscriptionId": "2",
        "name": "SHAHID_VIP_SPORT",
        "isDefault": false
      },
      {
        "subscriptionId": "3",
        "name": "SHAHID_VIP_SPORT_GOBX",
        "isDefault": false
      },
      {
        "subscriptionId": "4",
        "name": "SHAHID_MOBILE_ONLY",
        "isDefault": false
      },
      {
        "subscriptionId": "5",
        "name": "SHAHID_VIP_GEA",
        "isDefault": false
      },
      {
        "subscriptionId": "6",
        "name": "SHAHID_VIP_GEA_SPORT",
        "isDefault": false
      },
      {
        "subscriptionId": "7",
        "name": "SHAHID_VIP_GEA_SPORT_GOBX",
        "isDefault": false
      }
    ],
    "catalogs": [
      {
        "id": "39172",
        "name": "VIP",
        "isDefault": true
      }
    ],
    "adZone": null,
    "available": true,
    "tag": null,
    "rank": 0,
    "is4K": null,
    "productSubType": "SERIES",
    "showType": "SERIES",
    "seasons": [
      {
        "id": 4238093783527,
        "title": "Harb El Gibali",
        "seasonNumber": "1",
        "seasonName": "1",
        "numberOfAVODEpisodes": 15,
        "plus": true,
        "subscriptionPackages": [
          {
            "subscriptionId": "1",
            "name": "SHAHID_VIP",
            "isDefault": true
          },
          {
            "subscriptionId": "2",
            "name": "SHAHID_VIP_SPORT",
            "isDefault": false
          },
          {
            "subscriptionId": "3",
            "name": "SHAHID_VIP_SPORT_GOBX",
            "isDefault": false
          },
          {
            "subscriptionId": "4",
            "name": "SHAHID_MOBILE_ONLY",
            "isDefault": false
          },
          {
            "subscriptionId": "5",
            "name": "SHAHID_VIP_GEA",
            "isDefault": false
          },
          {
            "subscriptionId": "6",
            "name": "SHAHID_VIP_GEA_SPORT",
            "isDefault": false
          },
          {
            "subscriptionId": "7",
            "name": "SHAHID_VIP_GEA_SPORT_GOBX",
            "isDefault": false
          }
        ],
        "catalogs": [
          {
            "id": "39172",
            "name": "VIP",
            "isDefault": true
          }
        ],
        "streamInfo": {
          "streamState": "VOD",
          "startDate": null,
          "endDate": null
        },
        "catchUp": true,
        "firstEpisodeFree": true,
        "originalAVOD": false
      }
    ],
    "numberOfAvodSeasons": 0,
    "numberOfAvodEpisodeForShow": 15,
    "showOriginallyAVOD": false,
    "avodSeasonNumber": null,
    "season": {
      "id": 4238093783527,
      "title": "Harb El Gibali",
      "description": "In the heart of Al-Hareer, Jamaliya, two influential families collide in a sweeping tale of love, power, and vengeance, where every bond conceals a bitter betrayal.",
      "shortDescription": "",
      "image": {
        "thumbnailImage": "https://shahid.mbc.net/mediaObject/dd73b773-2fe4-4a29-a74f-3423b10b2949?height={height}&width={width}&croppingPoint={croppingPoint}&version=1",
        "posterImage": "https://shahid.mbc.net/mediaObject/181bbabf-1b1f-4105-9453-35b86c9ed6e1?height={height}&width={width}&croppingPoint={croppingPoint}&version=1",
        "heroSliderImage": "https://shahid.mbc.net/mediaObject/c3d14d3a-5837-4977-a5c2-09200ea418de?height={height}&width={width}&croppingPoint={croppingPoint}&version=1",
        "landscapeClean": "https://shahid.mbc.net/mediaObject/e15b099d-f807-4a31-8da4-8d4a4b770fa8?height={height}&width={width}&croppingPoint={croppingPoint}&version=1",
        "posterClean": "https://shahid.mbc.net/mediaObject/6eb6f941-a651-4428-836a-ebc1c4f12012?height={height}&width={width}&croppingPoint={croppingPoint}&version=1",
        "posterHero": "https://shahid.mbc.net/mediaObject/3ce6e997-540b-4a4e-b174-a96ff995c69c?height={height}&width={width}&croppingPoint={croppingPoint}&version=1",
        "posterTop10": "",
        "posterBundle": "",
        "onboardingCategoryImage": "",
        "squareImage": ""
      },
      "thumbnailImage": "https://shahid.mbc.net/mediaObject/dd73b773-2fe4-4a29-a74f-3423b10b2949?height={height}&width={width}&croppingPoint={croppingPoint}&version=1",
      "mainImage": "https://shahid.mbc.net/mediaObject/c3d14d3a-5837-4977-a5c2-09200ea418de?height={height}&width={width}&croppingPoint={croppingPoint}&version=1",
      "createdDate": "2025-05-18T00:00:00+0000",
      "modifiedDate": "2025-06-13T11:33:06+0000",
      "productionDate": null,
      "releaseDate": null,
      "productUrl": {
        "url": "https://shahid.mbc.net/en/series/harb-el-gibali/season-4246631591527-4238093783527",
        "shortenUrl": "https://shdl.ink/D2cYan"
      },
      "productUrls": [
        {
          "url": "https://shahid.mbc.net/en/series/harb-el-gibali/season-4246631591527-4238093783527",
          "shortenUrl": "https://shdl.ink/D2cYan"
        },
        {
          "url": "https://shahid.mbc.net/ar/series/حرب-الجبالي/season-4246631591527-4238093783527",
          "shortenUrl": "https://shdl.ink/feV1U9"
        },
        {
          "url": "https://shahid.mbc.net/fr/series/harb-al-gibali/season-4246631591527-4238093783527",
          "shortenUrl": "https://shdl.ink/bnpGks"
        }
      ],
      "dialect": {
        "id": 7915,
        "title": "Egyptian"
      },
      "genres": [
        {
          "id": 7863,
          "title": "Social"
        },
        {
          "id": 7876,
          "title": "Drama"
        },
        {
          "id": 49923064877829,
          "title": "Egyptian"
        }
      ],
      "microGenres": [],
      "channels": [
        {
          "id": 12438,
          "title": "Exclusive"
        }
      ],
      "displayMetadata": [],
      "decades": null,
      "persons": [
        {
          "id": 2785,
          "firstName": "Ahmed",
          "lastName": "Rizk",
          "fullName": "Ahmed Rizk",
          "rank": 1
        },
        {
          "id": 164156,
          "firstName": "Ryad",
          "lastName": "El Khouly",
          "fullName": "Ryad El Khouly",
          "rank": 2
        },
        {
          "id": 170350,
          "firstName": "Ahmed",
          "lastName": "Khaled Saleh",
          "fullName": "Ahmed Khaled Saleh",
          "rank": 3
        },
        {
          "id": 2583,
          "firstName": "Sawsan",
          "lastName": "Badr",
          "fullName": "Sawsan Badr",
          "rank": 4
        },
        {
          "id": 124928,
          "firstName": "Heba",
          "lastName": "Magdi",
          "fullName": "Heba Magdi",
          "rank": 5
        },
        {
          "id": 178674,
          "firstName": "Nesreen",
          "lastName": "Amin",
          "fullName": "Nesreen Amin",
          "rank": 6
        }
      ],
      "writers": [],
      "directors": [
        {
          "id": 173896,
          "firstName": "Mohamed",
          "lastName": "Osama",
          "fullName": "Mohamed Osama",
          "rank": 7
        }
      ],
      "coachs": [],
      "contestants": [],
      "judges": [],
      "presenters": [],
      "producers": [],
      "pricingPlans": [
        {
          "id": 1488015117,
          "name": null,
          "type": "BROWSE_ONLY",
          "planId": 0,
          "startDate": "2025-05-06T00:00:00+0000",
          "endDate": null,
          "offerEndDate": "2072-08-28T00:00:00+0000",
          "availability": {
            "days": 0,
            "hours": 0,
            "minutes": 0,
            "status": "NONE",
            "plus": true
          },
          "kidsAllowed": false,
          "kidsAllowedAge": -1,
          "downloadSetting": {
            "allowDownload": false,
            "name": null,
            "rental": null,
            "playback": null,
            "licenseExpiry": null
          }
        }
      ],
      "subscriptionPackages": [
        {
          "subscriptionId": "1",
          "name": "SHAHID_VIP",
          "isDefault": true
        },
        {
          "subscriptionId": "2",
          "name": "SHAHID_VIP_SPORT",
          "isDefault": false
        },
        {
          "subscriptionId": "3",
          "name": "SHAHID_VIP_SPORT_GOBX",
          "isDefault": false
        },
        {
          "subscriptionId": "4",
          "name": "SHAHID_MOBILE_ONLY",
          "isDefault": false
        },
        {
          "subscriptionId": "5",
          "name": "SHAHID_VIP_GEA",
          "isDefault": false
        },
        {
          "subscriptionId": "6",
          "name": "SHAHID_VIP_GEA_SPORT",
          "isDefault": false
        },
        {
          "subscriptionId": "7",
          "name": "SHAHID_VIP_GEA_SPORT_GOBX",
          "isDefault": false
        }
      ],
      "catalogs": [
        {
          "id": "39172",
          "name": "VIP",
          "isDefault": true
        }
      ],
      "adZone": null,
      "available": true,
      "tag": "New episodes return on June 15",
      "rank": 0,
      "is4K": false,
      "showItem": {
        "id": 4246631591527,
        "productSubType": "SERIES",
        "showType": "SERIES",
        "title": "Harb El Gibali"
      },
      "seasonNumber": "1",
      "seasonName": "1",
      "playlists": [
        {
          "id": "423809378352749923223104262",
          "productSubType": "EPISODE",
          "type": "EPISODE",
          "title": "Episodes",
          "count": 17,
          "sortNumber": 1
        },
        {
          "id": "423809378352749923223104264",
          "productSubType": "CLIP",
          "type": "CLIP",
          "title": "Promos",
          "count": 1,
          "sortNumber": 3
        },
        {
          "id": "423809378352749923223104262",
          "productSubType": "EPISODE",
          "type": "EPISODE",
          "title": "Free Episodes",
          "count": 15,
          "sortNumber": 2,
          "playlistType": "FREE_EPISODES"
        }
      ],
      "allPlaylist": null,
      "numberOfAssets": 18,
      "numberOfclips": 1,
      "bcmSeasonID": null,
      "dialectCombined": "Egyptian",
      "genresCombined": "Social-Drama-Egyptian",
      "endMarker": 0,
      "posterImage": "https://shahid.mbc.net/mediaObject/181bbabf-1b1f-4105-9453-35b86c9ed6e1?height={height}&width={width}&croppingPoint={croppingPoint}&version=1",
      "promoItem": {
        "id": 9801005861
      },
      "catchUp": true,
      "explicitContent": false,
      "originalContent": false,
      "productContractId": null,
      "viewStatus": "VIEW_NOW",
      "logoTitleImage": "https://shahid.mbc.net/mediaObject/cb112d72-b1f6-4c74-bbfe-57ad85182ebb?height={height}&width={width}&croppingPoint={croppingPoint}&version=1",
      "contentOriginalLanguage": "ar",
      "tracks": {
        "audios": null,
        "subtitles": null,
        "audioLabels": null
      },
      "comingSoon": {
        "comingSoonDay": "",
        "comingSoonMonth": ""
      },
      "eventType": null,
      "optaId": null,
      "numberOfEpisodes": 50,
      "numberOfAVODEpisodes": 15,
      "streamInfo": {
        "streamState": "VOD",
        "startDate": null,
        "endDate": null
      },
      "productType": "SEASON",
      "firstEpisodeFree": true
    },
    "numberOfSeasons": 1,
    "availableOnTV": true,
    "sortDate": "2025-05-18T00:00:00+0000",
    "logoTitleImage": "https://shahid.mbc.net/mediaObject/6a49be57-1a81-4528-9fa1-1e24177d2397?height={height}&width={width}&croppingPoint={croppingPoint}&version=1",
    "totalNumberOfEpisodes": 18,
    "eventType": null,
    "optaId": null,
    "eventSubType": null,
    "productType": "SHOW"
  }
}
[UI] Extracted title: Harb El Gibali
✅ Displayed content details for: Harb El Gibali
Mouse click: LEFT CLICK