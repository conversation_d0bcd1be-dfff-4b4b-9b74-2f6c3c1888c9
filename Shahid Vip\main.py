# ///////////////////////////////////////////////////////////////
#
# SHAHID PRO - Modern UI
# PROJECT MADE WITH: Qt Designer and PySide6
# V: 1.0.0
#
# This project allows downloading content from Shahid.net
# with a modern UI interface.
#
# ///////////////////////////////////////////////////////////////

import sys
import os
import time
import requests
import re
import shutil

# IMPORT / GUI AND MODULES AND WIDGETS
# ///////////////////////////////////////////////////////////////
from modules import *
from widgets import *
from modules.ui_functions import UIFunctions
from modules.app_settings import Settings
from modules.app_functions import AppFunctions
from modules.ui_main import Ui_MainWindow
from modules.shahid_api import Shah<PERSON><PERSON><PERSON>
from modules.shahid_drm import ShahidDRM
from modules.shahid_downloader import ShahidDownloader
from modules.shahid_ui import ShahidUI
from modules.settings_manager import SettingsManager
from modules.settings_ui import SettingsUI
from modules.shahid_player import ShahidPlayer
from PySide6.QtCore import *
from PySide6.QtCore import QTimer  # Importación explícita para QTimer.singleShot
from PySide6.QtGui import *
from PySide6.QtWidgets import *
from PySide6.QtWidgets import QPushButton  # Importación explícita para findChildren
import os
os.environ["QT_FONT_DPI"] = "96" # FIX Problem for High DPI and Scale above 100%

# SET AS GLOBAL WIDGETS
# ///////////////////////////////////////////////////////////////
widgets = None

# Variables globales para manejar múltiples episodios
global_selected_quality = None
global_selected_series_title = None
global_selected_seasons = []
current_season_index = 0

# Variables globales para manejar la cola de descarga secuencial
global_download_queue = []  # Lista para almacenar episodios en espera
global_currently_downloading = False  # Bandera para rastrear si hay una descarga en progreso
global_download_options = {}  # Almacenar opciones de descarga seleccionadas (calidad, audio, subtítulos)

class MainWindow(QMainWindow):
    def __init__(self):
        QMainWindow.__init__(self)

        # SET AS GLOBAL WIDGETS
        # ///////////////////////////////////////////////////////////////
        self.ui = Ui_MainWindow()
        self.ui.setupUi(self)
        global widgets
        widgets = self.ui

        # USE CUSTOM TITLE BAR | USE AS "False" FOR MAC OR LINUX
        # ///////////////////////////////////////////////////////////////
        Settings.ENABLE_CUSTOM_TITLE_BAR = True

        # APP NAME
        # ///////////////////////////////////////////////////////////////
        title = "SHAHID PRO - Modern UI"
        description = "SHAHID PRO - Modern UI Design with Dark Theme."
        # APPLY TEXTS
        self.setWindowTitle(title)
        widgets.titleRightInfo.setText(description)

        # INITIALIZE SETTINGS MANAGER
        # ///////////////////////////////////////////////////////////////
        self.settings_manager = SettingsManager()
        print("[MAIN] Settings manager initialized")

        # INITIALIZE SHAHID MODULES
        # ///////////////////////////////////////////////////////////////
        self.token = self.load_token()
        # Update token in settings if it exists
        if self.token:
            self.settings_manager.set_setting("token", self.token)

        # Ensure settings are saved
        self.settings_manager.save_settings()

        # Inicializar módulos de Shahid primero
        self.shahid_api = ShahidAPI(token=self.token)
        self.shahid_drm = ShahidDRM(token=self.token, api=self.shahid_api)
        self.shahid_downloader = ShahidDownloader(settings_manager=self.settings_manager)
        self.shahid_player = ShahidPlayer()
        self.shahid_ui = ShahidUI(self, widgets)

        # Initialize settings UI
        self.settings_ui = SettingsUI(self, widgets, self.settings_manager)

        # Apply UI settings from saved configuration después de inicializar todos los módulos
        self.apply_saved_settings()

        # Set tab names to match the design
        self.shahid_ui.content_tabs.setTabText(0, "Content Info")
        self.shahid_ui.content_tabs.setTabText(1, "Seasons & Episodes")
        self.shahid_ui.content_tabs.setTabText(2, "Download Options")
        self.shahid_ui.content_tabs.setTabText(3, "Downloads")

        # TOGGLE MENU
        # ///////////////////////////////////////////////////////////////
        widgets.toggleButton.clicked.connect(lambda: UIFunctions.toggleMenu(self, True))

        # SET UI DEFINITIONS
        # ///////////////////////////////////////////////////////////////
        UIFunctions.uiDefinitions(self)

        # QTableWidget PARAMETERS
        # ///////////////////////////////////////////////////////////////
        widgets.tableWidget.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)

        # BUTTONS CLICK
        # ///////////////////////////////////////////////////////////////

        # LEFT MENUS
        widgets.btn_home.clicked.connect(self.buttonClick)
        widgets.btn_widgets.clicked.connect(self.buttonClick)
        widgets.btn_new.clicked.connect(self.buttonClick)
        widgets.btn_save.clicked.connect(self.buttonClick)

        # EXTRA LEFT BOX
        def openCloseLeftBox():
            UIFunctions.toggleLeftBox(self, True)
        widgets.toggleLeftBox.clicked.connect(openCloseLeftBox)
        widgets.extraCloseColumnBtn.clicked.connect(openCloseLeftBox)

        # TOGGLE SIDEBAR
        def toggleSidebar():
            UIFunctions.toggleMenu(self, True)
        widgets.toggleButton.clicked.connect(toggleSidebar)

        # EXTRA RIGHT BOX
        def openCloseRightBox():
            # Check if right box is already open
            is_open = self.ui.extraRightBox.width() > 0

            # Toggle right box
            UIFunctions.toggleRightBox(self, True)

            # If opening the right box, update settings UI
            if not is_open and self.ui.extraRightBox.width() > 0:
                print("[MAIN] Opening settings panel, updating UI")
                # Setup settings UI
                self.settings_ui.setup_ui()

                # Ensure settings are applied
                try:
                    # Apply current sidebar visibility settings
                    show_movies = self.settings_manager.get_setting("ui", "show_movies_tab")
                    show_series = self.settings_manager.get_setting("ui", "show_series_tab")
                    show_settings = self.settings_manager.get_setting("ui", "show_settings_tab")

                    print(f"[MAIN] Applying sidebar visibility in openCloseRightBox: Movies={show_movies}, Series={show_series}, Settings={show_settings}")

                    # Update sidebar buttons visibility
                    widgets.btn_widgets.setVisible(show_movies)
                    widgets.btn_new.setVisible(show_series)
                    widgets.btn_save.setVisible(show_settings)
                except Exception as e:
                    print(f"[MAIN] Error applying settings in openCloseRightBox: {e}")

        widgets.settingsTopBtn.clicked.connect(openCloseRightBox)

        # APPLY SETTINGS AGAIN AFTER UI IS FULLY INITIALIZED
        # ///////////////////////////////////////////////////////////////
        QTimer.singleShot(500, self.apply_saved_settings)

        # SETUP CEF MESSAGE LOOP
        # ///////////////////////////////////////////////////////////////
        self.setup_cef_message_loop()

        # SHOW APP
        # ///////////////////////////////////////////////////////////////
        self.show()

        # SET CUSTOM THEME
        # ///////////////////////////////////////////////////////////////
        useCustomTheme = False
        themeFile = "themes\py_dracula_light.qss"

        # SET THEME AND HACKS
        if useCustomTheme:
            # LOAD AND APPLY STYLE
            UIFunctions.theme(self, themeFile, True)

            # SET HACKS
            AppFunctions.setThemeHack(self)

        # CONNECT UI SIGNALS
        # ///////////////////////////////////////////////////////////////
        # Connect token button
        self.shahid_ui.token_button.clicked.connect(self.save_token_from_ui)

        # Connect search buttons
        self.shahid_ui.search_button.clicked.connect(self.search_content)
        self.shahid_ui.clear_button.clicked.connect(self.clear_recent_urls)
        self.shahid_ui.recent_combo.activated.connect(self.load_from_recent)

        # Connect content navigation buttons
        self.shahid_ui.continue_button.clicked.connect(self.show_seasons_tab)
        self.shahid_ui.play_button.clicked.connect(self.play_selected_content)
        self.shahid_ui.seasons_play_button.clicked.connect(self.play_selected_content)
        self.shahid_ui.seasons_continue_button.clicked.connect(self.show_download_options_tab)
        self.shahid_ui.download_options_play_button.clicked.connect(self.play_selected_content)
        self.shahid_ui.add_to_queue_button.clicked.connect(self.add_to_download_queue)

        # Connect episode selection buttons
        self.shahid_ui.select_all_button.clicked.connect(self.select_all_episodes)
        self.shahid_ui.select_none_button.clicked.connect(self.select_none_episodes)
        self.shahid_ui.select_range_button.clicked.connect(self.select_episode_range)

        # Connect download option buttons
        self.shahid_ui.select_all_options_button.clicked.connect(self.select_all_options)
        self.shahid_ui.select_none_options_button.clicked.connect(self.select_none_options)

        # Connect download control buttons
        self.shahid_ui.start_download_button.clicked.connect(self.start_download)
        self.shahid_ui.clear_completed_button.clicked.connect(self.clear_completed_downloads)
        self.shahid_ui.clear_all_button.clicked.connect(self.clear_all_downloads)

        # Connect IDM download button
        self.shahid_ui.download_with_idm_button.clicked.connect(self.download_with_idm)

        # Connect seasons list selection
        self.shahid_ui.seasons_list.itemClicked.connect(self.load_episodes)

        # Connect movies buttons
        self.shahid_ui.movies_refresh_button.clicked.connect(self.refresh_movies_list)
        self.shahid_ui.movies_add_button.clicked.connect(self.add_movie)

        # Connect series buttons
        self.shahid_ui.series_refresh_button.clicked.connect(self.refresh_series_list)
        self.shahid_ui.series_add_button.clicked.connect(self.add_series)

        # Connect tab change signal
        self.shahid_ui.content_tabs.currentChanged.connect(self.on_tab_changed)

        # SET HOME PAGE AND SELECT MENU
        # ///////////////////////////////////////////////////////////////
        widgets.stackedWidget.setCurrentWidget(widgets.home)
        widgets.btn_home.setStyleSheet(UIFunctions.selectMenu(widgets.btn_home.styleSheet()))

        # Load recent URLs
        self.load_recent_urls()

        # Check if token exists and show/hide token input
        self.check_token()

        # Apply saved settings
        self.apply_saved_settings()

        # Update IDM button visibility
        self.update_idm_button_visibility()

    def setup_cef_message_loop(self):
        """Setup CEF message loop timer for proper CEF integration."""
        try:
            from modules.cef_player import cef_message_loop_work

            # Create timer for CEF message loop
            self.cef_timer = QTimer(self)
            self.cef_timer.timeout.connect(cef_message_loop_work)
            self.cef_timer.start(10)  # Run every 10ms for smooth CEF operation

            print("✅ CEF message loop timer started")

        except ImportError:
            print("ℹ️ CEF not available, skipping message loop setup")
        except Exception as e:
            print(f"❌ Error setting up CEF message loop: {e}")

    def closeEvent(self, event):
        """Handle application close event."""
        try:
            # Shutdown CEF properly
            from modules.cef_player import shutdown_cef
            shutdown_cef()
            print("🔄 CEF shutdown completed")
        except ImportError:
            pass
        except Exception as e:
            print(f"Error shutting down CEF: {e}")

        # Close any embedded players
        if hasattr(self, 'current_player_widget') and self.current_player_widget:
            try:
                self.current_player_widget.close()
            except:
                pass

        if hasattr(self, 'current_browser_player_widget') and self.current_browser_player_widget:
            try:
                self.current_browser_player_widget.close_browser()
            except:
                pass

        # Accept the close event
        event.accept()
        super().closeEvent(event)


    # Load token from file
    def load_token(self):
        """Load token from file if it exists."""
        # First try to get token from settings manager (if initialized)
        if hasattr(self, 'settings_manager'):
            token = self.settings_manager.get_setting("token")
            if token:
                print(f"Token loaded from settings manager")
                return token

        # If not found in settings, try to load from file
        # Use a path relative to the current script directory
        script_dir = os.path.dirname(os.path.abspath(__file__))
        token_path = os.path.join(script_dir, 'binaries', 'login', 'token.txt')
        token = None

        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(token_path), exist_ok=True)

        if os.path.exists(token_path):
            with open(token_path, 'r') as token_file:
                token = token_file.read().strip()
            print(f"Token loaded from: {token_path}")
        else:
            print(f"Token file not found at: {token_path}")

        return token

    # Save token to file
    def save_token(self, token):
        """Save token to file."""
        # Use a path relative to the current script directory
        script_dir = os.path.dirname(os.path.abspath(__file__))
        token_path = os.path.join(script_dir, 'binaries', 'login', 'token.txt')

        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(token_path), exist_ok=True)

        with open(token_path, 'w') as token_file:
            token_file.write(token)

        print(f"Token saved to: {token_path}")

        # Update the token in the API and DRM modules
        self.token = token
        self.shahid_api.token = token
        self.shahid_drm.token = token

    # BUTTONS CLICK
    # Post here your functions for clicked buttons
    # ///////////////////////////////////////////////////////////////
    def buttonClick(self):
        # GET BUTTON CLICKED
        btn = self.sender()
        btnName = btn.objectName()

        # SHOW HOME PAGE
        if btnName == "btn_home":
            widgets.stackedWidget.setCurrentWidget(widgets.home)
            UIFunctions.resetStyle(self, btnName)
            btn.setStyleSheet(UIFunctions.selectMenu(btn.styleSheet()))

        # SHOW MOVIES PAGE
        if btnName == "btn_widgets":
            widgets.stackedWidget.setCurrentWidget(widgets.widgets)
            UIFunctions.resetStyle(self, btnName)
            btn.setStyleSheet(UIFunctions.selectMenu(btn.styleSheet()))

            # Load movies list
            movies_list = self.shahid_api.get_movies_list()
            self.shahid_ui.populate_movies_table(movies_list)

        # SHOW SERIES PAGE
        if btnName == "btn_new":
            widgets.stackedWidget.setCurrentWidget(widgets.new_page) # SET PAGE
            UIFunctions.resetStyle(self, btnName) # RESET ANOTHERS BUTTONS SELECTED
            btn.setStyleSheet(UIFunctions.selectMenu(btn.styleSheet())) # SELECT MENU

            # Load series list
            series_list = self.shahid_api.get_series_list()
            self.shahid_ui.populate_series_table(series_list)

        # SHOW SETTINGS PAGE
        if btnName == "btn_save":
            print("Settings BTN clicked!")
            widgets.stackedWidget.setCurrentWidget(widgets.home)  # Use home page as base
            UIFunctions.resetStyle(self, btnName)
            btn.setStyleSheet(UIFunctions.selectMenu(btn.styleSheet()))

            # Apply current settings before opening panel
            try:
                # Apply current sidebar visibility settings
                show_movies = self.settings_manager.get_setting("ui", "show_movies_tab")
                show_series = self.settings_manager.get_setting("ui", "show_series_tab")
                show_settings = self.settings_manager.get_setting("ui", "show_settings_tab")

                print(f"[MAIN] Applying sidebar visibility in buttonClick: Movies={show_movies}, Series={show_series}, Settings={show_settings}")

                # Update sidebar buttons visibility
                widgets.btn_widgets.setVisible(show_movies)
                widgets.btn_new.setVisible(show_series)
                widgets.btn_save.setVisible(show_settings)
            except Exception as e:
                print(f"[MAIN] Error applying settings in buttonClick: {e}")

            # Open settings panel
            UIFunctions.toggleRightBox(self, True)

        # PRINT BTN NAME
        print(f'Button "{btnName}" pressed!')


    # UI ACTION METHODS
    # ///////////////////////////////////////////////////////////////
    def save_token_from_ui(self):
        """Save token from UI input."""
        token = self.shahid_ui.token_input.text().strip()
        if token:
            # Save token to file
            self.save_token(token)

            # Update token in settings manager
            self.settings_manager.set_setting("token", token)

            QMessageBox.information(self, "Token Saved", "Your token has been saved successfully.")
            # Hide token frame if token is saved
            self.shahid_ui.token_frame.setVisible(False)
        else:
            QMessageBox.warning(self, "Invalid Token", "Please enter a valid token.")

    def check_token(self):
        """Check if token exists and show/hide token input accordingly."""
        if self.token:
            self.shahid_ui.token_frame.setVisible(False)
            print(f"Token found and loaded successfully.")
        else:
            self.shahid_ui.token_frame.setVisible(True)
            print(f"No token found. Please enter your Shahid token.")
            QMessageBox.warning(self, "Token Required", "Please enter your Shahid token to continue.")

    def apply_saved_settings(self):
        """Apply saved settings to the application."""
        try:
            print("[MAIN] Applying saved settings...")

            # Apply sidebar visibility settings
            show_movies = self.settings_manager.get_setting("ui", "show_movies_tab")
            show_series = self.settings_manager.get_setting("ui", "show_series_tab")
            show_settings = self.settings_manager.get_setting("ui", "show_settings_tab")

            print(f"[MAIN] Sidebar visibility: Movies={show_movies}, Series={show_series}, Settings={show_settings}")

            # Update sidebar buttons visibility
            try:
                if hasattr(self, 'ui'):
                    self.ui.btn_widgets.setVisible(show_movies)
                    self.ui.btn_new.setVisible(show_series)
                    self.ui.btn_save.setVisible(show_settings)
                    print("[MAIN] Applied sidebar visibility settings to self.ui")
                elif 'widgets' in globals() and widgets is not None:
                    widgets.btn_widgets.setVisible(show_movies)
                    widgets.btn_new.setVisible(show_series)
                    widgets.btn_save.setVisible(show_settings)
                    print("[MAIN] Applied sidebar visibility settings to global widgets")
                else:
                    print("[MAIN] Could not find UI widgets to apply sidebar visibility")
            except Exception as e:
                print(f"[MAIN] Error applying sidebar visibility: {e}")

            # Apply theme
            theme = self.settings_manager.get_setting("ui", "theme")
            print(f"[MAIN] Theme: {theme}")

            try:
                if theme == "light":
                    # Apply light theme
                    theme_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "themes", "py_dracula_light.qss")
                    if os.path.exists(theme_path):
                        with open(theme_path, "r") as f:
                            self.setStyleSheet(f.read())
                        print("[MAIN] Applied light theme")
                    else:
                        print(f"[MAIN] Theme file not found: {theme_path}")
                else:
                    # Apply dark theme
                    theme_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "themes", "py_dracula_dark.qss")
                    if os.path.exists(theme_path):
                        with open(theme_path, "r") as f:
                            self.setStyleSheet(f.read())
                        print("[MAIN] Applied dark theme")
                    else:
                        print(f"[MAIN] Theme file not found: {theme_path}")
            except Exception as e:
                print(f"[MAIN] Error applying theme: {e}")

            print("[MAIN] Settings applied successfully")
        except Exception as e:
            print(f"[MAIN] Error applying settings: {e}")

    def update_idm_button_visibility(self):
        """Update IDM button visibility based on settings."""
        try:
            # Check if IDM is enabled in settings
            idm_enabled = self.settings_manager.get_setting("idm", "enabled")

            # Show/hide IDM button based on settings
            if hasattr(self.shahid_ui, 'download_with_idm_button'):
                self.shahid_ui.download_with_idm_button.setVisible(idm_enabled)
                print(f"[MAIN] IDM button visibility set to: {idm_enabled}")
            else:
                print("[MAIN] IDM button not found in UI")

        except Exception as e:
            print(f"[MAIN] Error updating IDM button visibility: {e}")

    # SEARCH AND CONTENT METHODS
    # ///////////////////////////////////////////////////////////////
    def search_content(self):
        """Search for content using URL or ID."""
        input_text = self.shahid_ui.url_input.text().strip()
        if not input_text:
            QMessageBox.warning(self, "Invalid Input", "Please enter a valid URL or content ID.")
            return

        # Check if token exists
        if not self.token:
            QMessageBox.warning(self, "Token Required", "Please enter your Shahid token first.")
            return

        # If input starts with "ID:", extract just the ID
        if input_text.startswith("ID: "):
            input_text = input_text.replace("ID: ", "")
            print(f"[UI] Extracted ID from input: {input_text}")

        # Show loading indicator
        self.shahid_ui.url_input.setDisabled(True)
        self.shahid_ui.search_button.setDisabled(True)
        QApplication.setOverrideCursor(Qt.WaitCursor)

        try:
            # Test URL extraction first
            content_id = self.shahid_api.extract_content_id_from_url(input_text)
            if content_id:
                print(f"[UI] Successfully extracted content ID: {content_id} from input: {input_text}")
            else:
                print(f"[UI] Failed to extract content ID from input: {input_text}")

            # Get content details
            print(f"[UI] Searching for content with input: {input_text}")
            content_details, content_type = self.shahid_api.get_content_details(input_text)

            if not content_details:
                QMessageBox.warning(self, "Content Not Found", "Could not find content with the provided URL or ID.")
                return
        finally:
            # Restore UI state
            self.shahid_ui.url_input.setDisabled(False)
            self.shahid_ui.search_button.setDisabled(False)
            QApplication.restoreOverrideCursor()

        # Print full API response for debugging
        import json
        print("API Response:")
        print(json.dumps(content_details, indent=2, ensure_ascii=False))

        # Print poster URL specifically
        if 'productModel' in content_details and 'image' in content_details['productModel']:
            print("Poster URL from image.posterImage:")
            print(content_details['productModel']['image'].get('posterImage', 'Not found'))

        # Print extracted poster URL
        poster_url = self.shahid_api.extract_poster_url(content_details)
        print("Extracted Poster URL:")
        print(poster_url)

        # Obtener el título del contenido
        title = None
        if content_details and 'productModel' in content_details:
            title = content_details['productModel'].get('title', None)
            print(f"[UI] Extracted title: {title}")

        # Add to recent URLs if not already there
        self.add_to_recent_urls(input_text, title)

        # Display content details
        self.display_content_details(content_details, content_type)

        # Show content tabs
        self.shahid_ui.content_tabs.setVisible(True)
        self.shahid_ui.content_tabs.setCurrentIndex(0)  # Show Content Info tab

        # Enable continue button only
        self.shahid_ui.continue_button.setEnabled(True)
        # Play button will be enabled after MPD and DRM info are extracted

        # Store content info for later use
        self.current_content = {
            'details': content_details,
            'type': content_type,
            'url': input_text
        }

    def add_to_recent_urls(self, url, title=None):
        """Add URL to recent URLs combobox with title if available."""
        # Si no tenemos título pero tenemos contenido actual, intentar obtenerlo
        if title is None and hasattr(self, 'current_content') and self.current_content:
            content_details = self.current_content.get('details', {})
            if content_details and 'productModel' in content_details:
                title = content_details['productModel'].get('title', None)

        # Extraer el ID si es una URL
        content_id = url if url.isdigit() else self.shahid_api.extract_content_id_from_url(url)

        # Si no tenemos ID válido, simplemente agregar la URL tal cual
        if not content_id:
            # Verificar si ya existe
            if self.shahid_ui.recent_combo.findText(url) != -1:
                self.shahid_ui.recent_combo.setCurrentText(url)
                return

            # Agregar la URL completa
            self.shahid_ui.recent_combo.addItem(url)
            self.shahid_ui.recent_combo.setCurrentText(url)
            self.save_recent_urls()
            return

        # Crear el texto a mostrar con ID y título si está disponible
        if title:
            id_display = f"ID: {content_id} - {title}"
        else:
            id_display = f"ID: {content_id}"

        # Buscar si ya existe este ID (con o sin título)
        for i in range(self.shahid_ui.recent_combo.count()):
            item_text = self.shahid_ui.recent_combo.itemText(i)
            # Verificar si el item contiene este ID
            if f"ID: {content_id}" in item_text:
                # Si ya existe pero sin título y ahora tenemos título, actualizarlo
                if title and " - " not in item_text:
                    self.shahid_ui.recent_combo.removeItem(i)
                    self.shahid_ui.recent_combo.addItem(id_display)
                    self.shahid_ui.recent_combo.setCurrentText(id_display)
                else:
                    # Ya existe, solo seleccionarlo
                    self.shahid_ui.recent_combo.setCurrentText(item_text)
                self.save_recent_urls()
                return

        # Si llegamos aquí, el ID no existe en la lista, agregarlo
        self.shahid_ui.recent_combo.addItem(id_display)
        self.shahid_ui.recent_combo.setCurrentText(id_display)

        # Ya no agregamos la URL completa, solo guardamos el ID con el título
        # Esto evita duplicados y mantiene la lista más limpia

        # Save recent URLs to a file for persistence
        self.save_recent_urls()

    def clear_recent_urls(self):
        """Clear recent URLs combobox."""
        self.shahid_ui.recent_combo.clear()
        # Also clear the saved file
        self.save_recent_urls()

    def load_from_recent(self):
        """Load content from selected recent URL."""
        url = self.shahid_ui.recent_combo.currentText()
        if url:
            # Si el elemento seleccionado es un ID con prefijo, extraer solo el ID
            if url.startswith("ID: "):
                # Puede tener formato "ID: 123456789 - Título"
                # Necesitamos extraer solo el ID numérico
                id_part = url.replace("ID: ", "")

                # Si tiene un título, separar el ID
                if " - " in id_part:
                    id_part = id_part.split(" - ")[0]

                url = id_part
            # Si es una URL completa, intentar extraer el ID
            elif "shahid.mbc.net" in url:
                content_id = self.shahid_api.extract_content_id_from_url(url)
                if content_id:
                    # Si pudimos extraer el ID, usarlo directamente
                    url = content_id
                    print(f"[UI] Extracted ID {content_id} from URL for search")

            self.shahid_ui.url_input.setText(url)
            self.search_content()

    def save_recent_urls(self):
        """Save recent URLs to a file."""
        try:
            # Create directory if it doesn't exist
            script_dir = os.path.dirname(os.path.abspath(__file__))
            config_dir = os.path.join(script_dir, "config")
            os.makedirs(config_dir, exist_ok=True)

            # Save to file
            recent_urls_file = os.path.join(config_dir, "recent_urls.txt")
            with open(recent_urls_file, 'w', encoding='utf-8') as f:
                # Get all items from combobox
                urls = [self.shahid_ui.recent_combo.itemText(i) for i in range(self.shahid_ui.recent_combo.count())]
                # Write each URL on a new line
                for url in urls:
                    f.write(f"{url}\n")

            print(f"[UI] Saved {len(urls)} recent URLs to {recent_urls_file}")
        except Exception as e:
            print(f"[UI] Error saving recent URLs: {e}")

    def load_recent_urls(self):
        """Load recent URLs from file."""
        try:
            # Check if file exists
            script_dir = os.path.dirname(os.path.abspath(__file__))
            recent_urls_file = os.path.join(script_dir, "config", "recent_urls.txt")

            if os.path.exists(recent_urls_file):
                with open(recent_urls_file, 'r', encoding='utf-8') as f:
                    urls = f.read().splitlines()

                # Limpiar el combobox primero
                self.shahid_ui.recent_combo.clear()

                # Filtrar URLs válidas y eliminar duplicados
                valid_urls = []
                seen_ids = set()  # Para rastrear IDs ya vistos (sin importar el título)
                seen_urls = set()  # Para rastrear URLs ya vistas

                for url in urls:
                    url = url.strip()
                    if not url:  # Saltar líneas vacías
                        continue

                    # Verificar si es una palabra clave no deseada (como "Downloads")
                    if url.lower() in ["downloads", "download"]:
                        continue

                    # Si es un ID con prefijo, extraer el ID (puede tener formato "ID: 123456789 - Título")
                    if url.startswith("ID: "):
                        id_part = url.replace("ID: ", "")

                        # Si tiene un título, separar el ID
                        if " - " in id_part:
                            id_value = id_part.split(" - ")[0]
                        else:
                            id_value = id_part

                        if id_value in seen_ids:
                            continue  # Saltar si ya hemos visto este ID
                        seen_ids.add(id_value)
                        valid_urls.append(url)
                    # Si es un ID numérico
                    elif url.isdigit():
                        if url in seen_ids:
                            continue  # Saltar si ya hemos visto este ID
                        seen_ids.add(url)
                        valid_urls.append(f"ID: {url}")  # Agregar con prefijo para consistencia
                    # Si es una URL
                    else:
                        if url in seen_urls:
                            continue  # Saltar si ya hemos visto esta URL
                        seen_urls.add(url)

                        # Intentar extraer el ID de la URL
                        content_id = self.shahid_api.extract_content_id_from_url(url)
                        if content_id and content_id.isdigit():
                            if content_id in seen_ids:
                                continue  # Saltar si ya hemos visto este ID
                            seen_ids.add(content_id)

                            # Intentar obtener el título del contenido
                            try:
                                content_details, _ = self.shahid_api.get_content_details(content_id)
                                if content_details and 'productModel' in content_details:
                                    title = content_details['productModel'].get('title', None)
                                    if title:
                                        # Agregar con formato ID: ID - Título
                                        valid_urls.append(f"ID: {content_id} - {title}")
                                        continue
                            except Exception as e:
                                print(f"[UI] Error getting title for ID {content_id}: {e}")

                            # Si no pudimos obtener el título, agregar solo el ID
                            valid_urls.append(f"ID: {content_id}")
                            continue

                        # Si no pudimos extraer un ID válido, agregar la URL completa
                        valid_urls.append(url)

                # Agregar URLs válidas al combobox
                for url in valid_urls:
                    self.shahid_ui.recent_combo.addItem(url)

                # Guardar las URLs limpias de vuelta al archivo
                self.save_recent_urls()

                print(f"[UI] Loaded and cleaned {len(valid_urls)} recent URLs from {recent_urls_file}")
        except Exception as e:
            print(f"[UI] Error loading recent URLs: {e}")

    def display_content_details(self, content_details, content_type):
        """Display content details in the UI."""
        if not content_details or 'productModel' not in content_details:
            return

        product = content_details['productModel']

        # Set title with proper formatting
        title = product.get('title', 'Unknown Title')
        self.shahid_ui.title_label.setText(title)

        # Set type with proper formatting
        content_type_text = product.get('productType', content_type)
        if content_type_text == "SERIES":
            content_type_text = "SHOW"
        self.shahid_ui.type_label.setText(f"Type: {content_type_text}")

        # Extract year using the find_year_in_object function
        year = self.shahid_api.find_year_in_object(product)
        self.shahid_ui.year_label.setText(f"Year: {year}")

        # Set episodes count for series
        if content_type_text == "SHOW" or content_type == "SERIES":
            # Prioritize getting the total number of episodes from the season object
            episodes_count = 0

            # First, check directly in the season object (this is the most accurate source)
            if 'season' in product and isinstance(product['season'], dict):
                season = product['season']
                if 'numberOfEpisodes' in season:
                    episodes_count = season.get('numberOfEpisodes', 0)
                    print(f"Found numberOfEpisodes in season: {episodes_count}")

            # If not found in season, check in the main product object
            if not episodes_count:
                if 'numberOfEpisodes' in product:
                    episodes_count = product.get('numberOfEpisodes', 0)
                    print(f"Found numberOfEpisodes in product: {episodes_count}")
                elif 'totalNumberOfEpisodes' in product:
                    episodes_count = product.get('totalNumberOfEpisodes', 0)
                    print(f"Found totalNumberOfEpisodes in product: {episodes_count}")

            # If still not found, try to calculate from playlists
            if not episodes_count and 'season' in product and isinstance(product['season'], dict):
                season = product['season']
                if 'playlists' in season and isinstance(season['playlists'], list):
                    for playlist in season['playlists']:
                        if isinstance(playlist, dict) and playlist.get('productSubType') == 'EPISODE':
                            episodes_count = playlist.get('count', 0)
                            print(f"Found episodes count in playlist: {episodes_count}")
                            break

            if episodes_count:
                self.shahid_ui.episodes_count_label.setText(f"Episodes: {episodes_count}")
                self.shahid_ui.episodes_count_label.setVisible(True)
            else:
                self.shahid_ui.episodes_count_label.setVisible(False)
        else:
            # Hide episodes count for movies
            self.shahid_ui.episodes_count_label.setVisible(False)

        # Set genres with proper formatting
        genres = []
        if 'genres' in product and isinstance(product['genres'], list) and product['genres']:
            for genre in product['genres']:
                if isinstance(genre, dict) and 'title' in genre and genre['title']:
                    genres.append(genre.get('title', ''))

        # Format genres with separator
        if genres:
            genres_text = ' | '.join(genres)
            self.shahid_ui.genres_label.setText(f"Genres: {genres_text}")
        else:
            # Try alternative genre fields
            alt_genres = []
            if 'genresList' in product and isinstance(product['genresList'], list):
                for genre in product['genresList']:
                    if isinstance(genre, str) and genre:
                        alt_genres.append(genre)
            elif 'genresCombined' in product and product['genresCombined']:
                # Try to get from genresCombined field (as seen in the JSON)
                genres_combined = product['genresCombined'].split('-')
                for genre in genres_combined:
                    if genre:
                        alt_genres.append(genre)

            if alt_genres:
                alt_genres_text = ' | '.join(alt_genres)
                self.shahid_ui.genres_label.setText(f"Genres: {alt_genres_text}")
            else:
                self.shahid_ui.genres_label.setText("Genres: Unknown")

        # Set cast with proper formatting
        cast = []
        if 'cast' in product:
            for actor in product['cast']:
                cast.append(actor.get('name', ''))
        elif 'persons' in product:
            for actor in product['persons']:
                cast.append(actor.get('fullName', ''))

        # Format cast list
        if cast:
            cast_text = ', '.join(cast[:5])  # Limit to first 5 actors
            self.shahid_ui.cast_label.setText(f"Cast: {cast_text}")
        else:
            self.shahid_ui.cast_label.setText("Cast: Unknown")

        # Set description with proper formatting
        description = product.get('description', 'No description available.')
        self.shahid_ui.description_text.setText(description)

        # Extract poster URL using the extract_poster_url function
        poster_url = self.shahid_api.extract_poster_url(content_details)

        if poster_url:
            # Download and display poster
            self.load_poster_image(poster_url)
        else:
            # Set default poster
            self.shahid_ui.poster_label.setText("No Poster Available")

    def load_poster_image(self, url):
        """Load poster image from URL and save it to the download directory."""
        try:
            print(f"Attempting to load poster from URL: {url}")

            # Set a placeholder loading message
            self.shahid_ui.poster_label.setText("Loading poster...")

            # Use a session with headers to avoid potential blocking
            session = requests.Session()
            session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9,ar;q=0.8',
                'Referer': 'https://shahid.mbc.net/'
            })

            # Try with proxies if available
            if self.shahid_api.proxies:
                response = session.get(url, proxies=self.shahid_api.proxies, timeout=10)
            else:
                response = session.get(url, timeout=10)

            print(f"Poster response status code: {response.status_code}")

            if response.status_code == 200:
                # Create a directory for cached images if it doesn't exist
                script_dir = os.path.dirname(os.path.abspath(__file__))
                cache_dir = os.path.join(script_dir, "cache", "posters")
                os.makedirs(cache_dir, exist_ok=True)

                # Create a filename based on the URL
                import hashlib
                filename = hashlib.md5(url.encode()).hexdigest() + ".jpg"
                temp_file = os.path.join(cache_dir, filename)

                # Save the image to the cache file
                with open(temp_file, 'wb') as f:
                    f.write(response.content)

                # Load from file
                pixmap = QPixmap(temp_file)

                if not pixmap.isNull():
                    # We're using setScaledContents(True) on the label now, so no need to scale manually
                    self.shahid_ui.poster_label.setPixmap(pixmap)
                    print("Poster loaded successfully")

                    # Save the poster to the download directory if we have content info
                    if hasattr(self, 'current_content') and self.current_content:
                        try:
                            content_title = self.current_content['details']['productModel']['title']
                            # Sanitize the title by removing invalid characters
                            sanitized_title = re.sub(r'[<>:"/\\|?*]', '', content_title)

                            # Create the download directory if it doesn't exist
                            script_dir = os.path.dirname(os.path.abspath(__file__))
                            download_dir = os.path.join(script_dir, "downloads", sanitized_title)
                            os.makedirs(download_dir, exist_ok=True)

                            # Save the poster image
                            poster_path = os.path.join(download_dir, 'poster.jpg')
                            shutil.copy(temp_file, poster_path)
                            print(f"Poster saved to: {poster_path}")
                        except Exception as save_error:
                            print(f"Error saving poster to download directory: {save_error}")
                else:
                    print("Failed to create pixmap from image data")
                    self.shahid_ui.poster_label.setText("Failed to load poster")
            else:
                print(f"Failed to load poster: HTTP {response.status_code}")
                self.shahid_ui.poster_label.setText("Failed to load poster")
        except Exception as e:
            print(f"Error loading poster: {e}")
            self.shahid_ui.poster_label.setText("Error loading poster")

    # SEASONS AND EPISODES METHODS
    # ///////////////////////////////////////////////////////////////
    def show_seasons_tab(self):
        """Show seasons and episodes tab or skip to download options for movies."""
        # If content is a movie, skip directly to download options tab
        if hasattr(self, 'current_content') and self.current_content['type'] == "MOVIE":
            print("[UI] Content is a MOVIE, skipping seasons tab and going directly to download options")
            # First trigger the seasons tab to load the movie data
            self.shahid_ui.content_tabs.setCurrentIndex(1)
            # Then immediately go to download options tab
            QTimer.singleShot(100, self.show_download_options_tab)
        else:
            # For series, show the seasons tab as usual
            self.shahid_ui.content_tabs.setCurrentIndex(1)  # Show Seasons & Episodes tab

        # The rest of the functionality is handled by on_tab_changed

    def load_seasons(self, series_id):
        """Load seasons for a series."""
        print(f"[UI] Loading seasons for series ID: {series_id}")
        seasons = self.shahid_api.get_series_seasons(series_id)
        print(f"[UI] Found {len(seasons)} seasons")

        self.shahid_ui.seasons_list.clear()

        for season_data in seasons:
            # The API now returns more information including total episodes
            if len(season_data) >= 5:
                season_id, season_number, season_name, episode_count, total_episodes = season_data
                print(f"[UI] Season data (5+ fields): ID={season_id}, Number={season_number}, Name={season_name}, Count={episode_count}, Total={total_episodes}")
            else:
                # Fallback for old API format
                season_id, season_number, season_name, episode_count = season_data
                total_episodes = episode_count
                print(f"[UI] Season data (4 fields): ID={season_id}, Number={season_number}, Name={season_name}, Count={episode_count}")

            # If we found total episodes, display it in the season name
            display_name = season_name
            print(f"[UI] Adding season to list: {display_name}")

            item = QListWidgetItem(display_name)
            item.setData(Qt.UserRole, {
                'id': season_id,
                'number': season_number,
                'name': season_name,
                'count': episode_count,
                'total_episodes': total_episodes
            })
            self.shahid_ui.seasons_list.addItem(item)

        # Select first season if available
        if self.shahid_ui.seasons_list.count() > 0:
            print(f"[UI] Selecting first season")
            self.shahid_ui.seasons_list.setCurrentRow(0)
            # Load episodes for first season
            first_item = self.shahid_ui.seasons_list.item(0)
            self.load_episodes(first_item)
        else:
            print(f"[UI WARNING] No seasons found for series ID: {series_id}")

    def load_episodes(self, season_item):
        """Load episodes for a season."""
        season_data = season_item.data(Qt.UserRole)
        season_id = season_data['id']
        episode_count = season_data['count']
        total_episodes = season_data.get('total_episodes', episode_count)

        print(f"[UI] Loading episodes for season ID: {season_id}, Count: {episode_count}, Total: {total_episodes}")

        # Update range spinners
        self.shahid_ui.range_from.setMinimum(1)
        self.shahid_ui.range_from.setMaximum(total_episodes)
        self.shahid_ui.range_from.setValue(1)

        self.shahid_ui.range_to.setMinimum(1)
        self.shahid_ui.range_to.setMaximum(total_episodes)
        self.shahid_ui.range_to.setValue(total_episodes)
        print(f"[UI] Updated range spinners: from 1 to {total_episodes}")

        # If it's a movie (single episode), handle differently
        if self.current_content['type'] == "MOVIE":
            print("[UI] Content is a MOVIE, episodes already handled")
            # Make sure the continue button is enabled
            self.shahid_ui.seasons_continue_button.setEnabled(True)
            # Already handled in show_seasons_tab
            return

        # For series, load episodes from API
        print(f"[UI] Fetching episodes from API for season ID: {season_id}")
        episodes = self.shahid_api.get_season_episodes(season_id)
        print(f"[UI] Found {len(episodes) if episodes else 0} episodes from API")

        self.shahid_ui.episodes_list.clear()

        # If we have episodes from the API
        if episodes:
            # Track episode numbers we've already added to avoid duplicates
            added_episode_numbers = set()
            available_count = 0

            for episode_data in episodes:
                # The API now returns more information including availability
                if len(episode_data) >= 6:
                    episode_id, episode_number, episode_name, episode_title, availability_status, is_available = episode_data
                    print(f"[UI] Episode data (6+ fields): ID={episode_id}, Number={episode_number}, Name={episode_name}, Title={episode_title}, Status={availability_status}, Available={is_available}")
                else:
                    # Fallback for old API format
                    episode_id, episode_number, episode_name, episode_title = episode_data
                    availability_status = "Available"
                    is_available = True
                    print(f"[UI] Episode data (4 fields): ID={episode_id}, Number={episode_number}, Name={episode_name}, Title={episode_title}")

                # Skip if we've already added this episode number
                if episode_number in added_episode_numbers:
                    continue

                # Skip episodes that are not available and don't have a specific release date
                if not is_available and availability_status == "Not yet available":
                    continue

                added_episode_numbers.add(episode_number)

                # Count available episodes
                if is_available:
                    available_count += 1

                # Format display name based on availability
                display_name = f"{episode_name} - {episode_title}"
                if availability_status and availability_status != "Available":
                    display_name += f" - {availability_status}"

                print(f"[UI] Adding episode to list: {display_name}")

                item = QListWidgetItem(display_name)
                item.setData(Qt.UserRole, {
                    'id': episode_id,
                    'number': episode_number,
                    'name': episode_name,
                    'title': episode_title,
                    'available': is_available,
                    'status': availability_status
                })

                # Set item color based on availability
                if not is_available:
                    item.setForeground(QColor("#ff5555"))  # Red for unavailable

                self.shahid_ui.episodes_list.addItem(item)

            print(f"[UI] Added {available_count} available episodes out of {len(added_episode_numbers)} total episodes shown")
        else:
            print(f"[UI] No episodes found from API")

        # Enable continue button if episodes are available
        episodes_count = self.shahid_ui.episodes_list.count()
        self.shahid_ui.seasons_continue_button.setEnabled(episodes_count > 0)
        print(f"[UI] Added {episodes_count} episodes to list, continue button enabled: {episodes_count > 0}")

    def select_all_episodes(self):
        """Select all episodes."""
        for i in range(self.shahid_ui.episodes_list.count()):
            self.shahid_ui.episodes_list.item(i).setSelected(True)

    def select_none_episodes(self):
        """Deselect all episodes."""
        for i in range(self.shahid_ui.episodes_list.count()):
            self.shahid_ui.episodes_list.item(i).setSelected(False)

    def select_episode_range(self):
        """Select episodes in the specified range."""
        start = self.shahid_ui.range_from.value()
        end = self.shahid_ui.range_to.value()

        # Ensure start <= end
        if start > end:
            start, end = end, start

        # Deselect all first
        self.select_none_episodes()

        # Select episodes in range
        for i in range(self.shahid_ui.episodes_list.count()):
            item = self.shahid_ui.episodes_list.item(i)
            episode_data = item.data(Qt.UserRole)
            episode_number = episode_data['number']

            if start <= episode_number <= end:
                item.setSelected(True)

    # DOWNLOAD OPTIONS METHODS
    # ///////////////////////////////////////////////////////////////
    def show_download_options_tab(self):
        """Show download options tab."""
        # Check if any episodes are selected
        if not self.shahid_ui.episodes_list.selectedItems():
            QMessageBox.warning(self, "No Episodes Selected", "Please select at least one episode to continue.")
            return

        # Get the first selected episode to load stream options
        selected_episode = self.shahid_ui.episodes_list.selectedItems()[0]
        episode_data = selected_episode.data(Qt.UserRole)
        episode_id = episode_data['id']

        # Load stream options for the selected episode
        self.load_episode_stream_options(episode_id)

        # Show the download options tab
        self.shahid_ui.content_tabs.setCurrentIndex(2)  # Show Download Options tab

    def load_episode_stream_options(self, episode_id):
        """Load stream options for the selected episode."""
        print(f"[UI] Loading stream options for episode ID: {episode_id}")

        # Show loading indicator
        progress_dialog = QProgressDialog("Loading stream options...", "Cancel", 0, 0, self)
        progress_dialog.setWindowTitle("Loading")
        progress_dialog.setWindowModality(Qt.WindowModal)
        progress_dialog.setMinimumDuration(0)
        progress_dialog.setValue(0)
        progress_dialog.show()
        QApplication.processEvents()

        try:
            # Get episode details from API
            playout_data = self.shahid_api.get_episode_playout_url(episode_id)
            if not playout_data:
                QMessageBox.warning(self, "Error", "Failed to get episode details.")
                progress_dialog.close()
                return

            # Print the full playout data for debugging
            print(f"[UI] Playout data: {playout_data}")

            # Extract available codecs
            available_codecs = playout_data.get('available_codecs', [])
            mpd_urls = playout_data.get('mpd_urls', {})
            audio_tracks = playout_data.get('audio_tracks', [])
            subtitle_tracks = playout_data.get('subtitle_tracks', [])

            print(f"[UI] Available codecs: {available_codecs}")
            print(f"[UI] MPD URLs: {mpd_urls}")
            print(f"[UI] Audio tracks: {audio_tracks}")
            print(f"[UI] Subtitle tracks: {subtitle_tracks}")

            # Store the playout data for later use
            self.current_playout_data = playout_data

            # Extraer información de stream para cada codec disponible
            h264_qualities = []
            h265_qualities = []
            h264_audio_tracks = []
            h265_audio_tracks = []
            h264_subtitle_tracks = []
            h265_subtitle_tracks = []

            # Procesar H264
            if mpd_urls and isinstance(mpd_urls, dict) and 'H264' in mpd_urls:
                try:
                    mpd_url = mpd_urls['H264']
                    if not isinstance(mpd_url, str):
                        print(f"[ERROR] MPD URL is not a string: {type(mpd_url)}")
                    else:
                        # Fix URL if it contains "&" followed by another URL (common error in Shahid URLs)
                        if '&' in mpd_url and ('edgenextcdn.net' in mpd_url.split('&')[1] or 'mncdn.com' in mpd_url.split('&')[1]):
                            original_url = mpd_url
                            mpd_url = mpd_url.split('&')[0]
                            print(f"[UI] Fixed H264 MPD URL from: {original_url}")
                            print(f"[UI] Fixed H264 MPD URL to: {mpd_url}")
                            # Update the URL in mpd_urls
                            mpd_urls['H264'] = mpd_url

                        print(f"[UI] Extracting stream info from H264 MPD URL: {mpd_url}")

                        # Extract stream info from MPD
                        h264_qualities, h264_audio_tracks, h264_subtitle_tracks = self.shahid_downloader.extract_stream_info_from_url(mpd_url)

                        print(f"[UI] Extracted H264 qualities from MPD: {h264_qualities}")
                        print(f"[UI] Extracted H264 audio tracks from MPD: {h264_audio_tracks}")

                        # Guardar la información de H264
                        self.current_playout_data['h264_qualities'] = h264_qualities
                        self.current_playout_data['h264_audio_tracks'] = h264_audio_tracks
                        self.current_playout_data['h264_subtitle_tracks'] = h264_subtitle_tracks

                        # Guardar el MPD URL para reproducción
                        self.current_playout_data['mpd_url'] = mpd_url

                        # Guardar también en las variables generales para compatibilidad
                        self.current_playout_data['mpd_qualities'] = h264_qualities
                        self.current_playout_data['mpd_audio_tracks'] = h264_audio_tracks
                        self.current_playout_data['mpd_subtitle_tracks'] = h264_subtitle_tracks

                        # Procesar información DRM con el MPD de H264
                        self.process_drm_info(episode_id, mpd_url)
                except Exception as e:
                    print(f"[ERROR] Error extracting H264 stream info from MPD: {e}")
                    import traceback
                    traceback.print_exc()

            # Procesar H265
            if mpd_urls and isinstance(mpd_urls, dict) and 'H265' in mpd_urls:
                try:
                    mpd_url = mpd_urls['H265']
                    if not isinstance(mpd_url, str):
                        print(f"[ERROR] H265 MPD URL is not a string: {type(mpd_url)}")
                    else:
                        # Fix URL if it contains "&" followed by another URL (common error in Shahid URLs)
                        if '&' in mpd_url and ('edgenextcdn.net' in mpd_url.split('&')[1] or 'mncdn.com' in mpd_url.split('&')[1]):
                            original_url = mpd_url
                            mpd_url = mpd_url.split('&')[0]
                            print(f"[UI] Fixed H265 MPD URL from: {original_url}")
                            print(f"[UI] Fixed H265 MPD URL to: {mpd_url}")
                            # Update the URL in mpd_urls
                            mpd_urls['H265'] = mpd_url

                        print(f"[UI] Extracting stream info from H265 MPD URL: {mpd_url}")

                        # Extract stream info from MPD
                        h265_qualities, h265_audio_tracks, h265_subtitle_tracks = self.shahid_downloader.extract_stream_info_from_url(mpd_url)

                        print(f"[UI] Extracted H265 qualities from MPD: {h265_qualities}")
                        print(f"[UI] Extracted H265 audio tracks from MPD: {h265_audio_tracks}")

                        # Guardar la información de H265
                        self.current_playout_data['h265_qualities'] = h265_qualities
                        self.current_playout_data['h265_audio_tracks'] = h265_audio_tracks
                        self.current_playout_data['h265_subtitle_tracks'] = h265_subtitle_tracks
                except Exception as e:
                    print(f"[ERROR] Error extracting H265 stream info from MPD: {e}")
                    import traceback
                    traceback.print_exc()

            # Actualizar la interfaz de usuario con las opciones disponibles
            self.update_download_options_ui()

            # Cambiar a la pestaña de opciones de descarga
            self.shahid_ui.content_tabs.setCurrentIndex(2)  # Índice 2 es la pestaña "Download Options"

        except Exception as e:
            print(f"[ERROR] Failed to load stream options: {e}")
            QMessageBox.warning(self, "Error", f"Failed to load stream options: {str(e)}")
        finally:
            progress_dialog.close()

    def update_download_options_ui(self):
        """Update the download options UI with available options from the MPD."""
        if not hasattr(self, 'current_playout_data'):
            return

        # Determinar qué codec está seleccionado actualmente
        selected_codec = "H264"  # Por defecto H264
        if self.shahid_ui.h265_checkbox.isChecked():
            selected_codec = "H265"
        elif self.shahid_ui.h264_checkbox.isChecked():
            selected_codec = "H264"

        # Actualizar las resoluciones disponibles para el codec seleccionado
        self.update_resolutions_for_codec(selected_codec)

        # Actualizar las pistas de audio disponibles, filtrando idiomas desconocidos
        self.update_audio_tracks_for_codec(selected_codec)

        # Actualizar las opciones de subtítulos
        subtitle_tracks = self.current_playout_data.get('mpd_subtitle_tracks', [])

        # Limpiar la lista de subtítulos
        self.shahid_ui.subtitle_list_widget.clear()

        # Actualizar los checkboxes ocultos (para compatibilidad)
        self.shahid_ui.subtitle_ar_checkbox.setChecked(False)
        self.shahid_ui.subtitle_en_checkbox.setChecked(False)

        if not subtitle_tracks:
            # Si no hay subtítulos disponibles, mostrar un mensaje
            print("[UI] No subtitle tracks available")

            # Crear un widget para mostrar el mensaje
            message_widget = QWidget()
            message_layout = QVBoxLayout(message_widget)
            message_layout.setContentsMargins(5, 5, 5, 5)

            message_label = QLabel("No subtitle tracks available")
            message_label.setStyleSheet("color: #f8f8f2; font-style: italic;")
            message_label.setAlignment(Qt.AlignCenter)

            message_layout.addWidget(message_label)

            # Crear un ítem para agregar el widget
            item = QListWidgetItem()
            item.setSizeHint(message_widget.sizeHint())

            # Agregar el ítem y el widget a la lista
            self.shahid_ui.subtitle_list_widget.addItem(item)
            self.shahid_ui.subtitle_list_widget.setItemWidget(item, message_widget)
        else:
            # Imprimir todos los subtítulos disponibles para depuración
            print(f"[UI] Available subtitle tracks: {subtitle_tracks}")

            # Agregar los subtítulos a la lista
            for track in subtitle_tracks:
                code = track.get('code', '')
                name = track.get('name', '')

                # Verificar que el código sea válido (2-3 caracteres)
                if not code or len(code) not in [2, 3]:
                    print(f"[UI] Skipping invalid subtitle code: {code}")
                    continue

                # Asegurarse de que el nombre no esté vacío
                if not name:
                    try:
                        # Intentar obtener el nombre del idioma a partir del código
                        from langcodes import Language
                        name = Language.get(code).display_name()
                    except Exception:
                        # Si no se puede obtener el nombre, usar el código como nombre
                        name = code.upper()

                display = f"{name} ({code})"
                print(f"[UI] Adding subtitle to list: {display}")

                # Agregar a la lista con selección predeterminada para ar y en
                selected = code in ['ar', 'en']
                self.shahid_ui.add_subtitle_to_list(display, selected)

                # Actualizar los checkboxes ocultos para compatibilidad
                if code == 'ar':
                    self.shahid_ui.subtitle_ar_checkbox.setText(display)
                    self.shahid_ui.subtitle_ar_checkbox.setChecked(selected)
                elif code == 'en':
                    self.shahid_ui.subtitle_en_checkbox.setText(display)
                    self.shahid_ui.subtitle_en_checkbox.setChecked(selected)

    def select_all_options(self):
        """Select all download options."""
        self.shahid_ui.h264_checkbox.setChecked(True)
        self.shahid_ui.h265_checkbox.setChecked(True)
        self.shahid_ui.audio_ar_checkbox.setChecked(True)
        self.shahid_ui.audio_en_checkbox.setChecked(True)
        self.shahid_ui.audio_tr_checkbox.setChecked(True)

        # Seleccionar todos los subtítulos en la lista
        for i in range(self.shahid_ui.subtitle_list_widget.count()):
            item = self.shahid_ui.subtitle_list_widget.item(i)
            item.setSelected(True)

        # Actualizar los checkboxes ocultos
        self.shahid_ui.subtitle_ar_checkbox.setChecked(True)
        self.shahid_ui.subtitle_en_checkbox.setChecked(True)

    def select_none_options(self):
        """Deselect all download options."""
        # Deseleccionar los checkboxes de codec
        if hasattr(self.shahid_ui, 'h264_checkbox'):
            self.shahid_ui.h264_checkbox.setChecked(False)
        if hasattr(self.shahid_ui, 'h265_checkbox'):
            self.shahid_ui.h265_checkbox.setChecked(False)

        # Deseleccionar los checkboxes de audio
        if hasattr(self.shahid_ui, 'audio_ar_checkbox') and self.shahid_ui.audio_ar_checkbox is not None:
            self.shahid_ui.audio_ar_checkbox.setChecked(False)
        if hasattr(self.shahid_ui, 'audio_en_checkbox') and self.shahid_ui.audio_en_checkbox is not None:
            self.shahid_ui.audio_en_checkbox.setChecked(False)
        if hasattr(self.shahid_ui, 'audio_tr_checkbox') and self.shahid_ui.audio_tr_checkbox is not None:
            self.shahid_ui.audio_tr_checkbox.setChecked(False)

        # Deseleccionar todos los subtítulos en la lista
        if hasattr(self.shahid_ui, 'subtitle_list_widget'):
            for i in range(self.shahid_ui.subtitle_list_widget.count()):
                item = self.shahid_ui.subtitle_list_widget.item(i)
                item.setSelected(False)

        # Actualizar los checkboxes ocultos
        if hasattr(self.shahid_ui, 'subtitle_ar_checkbox'):
            self.shahid_ui.subtitle_ar_checkbox.setChecked(False)
        if hasattr(self.shahid_ui, 'subtitle_en_checkbox'):
            self.shahid_ui.subtitle_en_checkbox.setChecked(False)

    def add_to_download_queue(self):
        """Add selected episodes to download queue."""
        global global_selected_quality, global_selected_series_title, global_selected_seasons

        # Check if any codec is selected
        if not (self.shahid_ui.h264_checkbox.isChecked() or self.shahid_ui.h265_checkbox.isChecked()):
            QMessageBox.warning(self, "No Codec Selected", "Please select at least one codec.")
            return

        # Get selected episodes
        selected_episodes = self.shahid_ui.episodes_list.selectedItems()
        if not selected_episodes:
            QMessageBox.warning(self, "No Episodes Selected", "Please select at least one episode.")
            return

        # Get selected options
        resolution = self.shahid_ui.resolution_combo.currentText()

        # Store the selected quality globally for future use
        global_selected_quality = resolution

        # Get content title
        content_title = self.current_content['details']['productModel']['title']

        # Store the series title globally
        global_selected_series_title = content_title

        # Get current season
        current_season_item = self.shahid_ui.seasons_list.currentItem()
        season_data = current_season_item.data(Qt.UserRole)
        season_number = season_data['number']

        # Store the season data globally if not already stored
        if not global_selected_seasons or season_data not in global_selected_seasons:
            global_selected_seasons.append(season_data)

        # Add each episode to the queue
        for episode_item in selected_episodes:
            episode_data = episode_item.data(Qt.UserRole)
            episode_id = episode_data['id']
            episode_number = episode_data['number']

            # For each episode, we need to get its specific MPD URL
            print(f"[UI] Getting playout URL for episode {episode_number} (ID: {episode_id})")
            print(f"[UI] Processing episode {episode_number} of {len(selected_episodes)} selected episodes")

            # Get episode-specific MPD URL
            episode_mpd_url = None
            episode_drm_info = None

            # Determine which codec to use
            codec = "H.264" if self.shahid_ui.h264_checkbox.isChecked() else "H.265"

            # Store the episode ID in a list to track which episodes are being processed
            if not hasattr(self, 'processing_episode_ids'):
                self.processing_episode_ids = []
            self.processing_episode_ids.append(episode_id)
            print(f"[UI] Added episode ID {episode_id} to processing list (total: {len(self.processing_episode_ids)})")

            try:
                # Get playout URL for this specific episode
                print(f"[UI] Requesting playout URL for episode ID: {episode_id}")
                playout_data = self.shahid_api.get_episode_playout_url(episode_id)

                # Print a summary of the playout data for debugging
                print(f"[UI] Received playout data for episode {episode_number} (ID: {episode_id})")

                if playout_data:
                    # Check if we have MPD URLs in the playout data
                    if 'mpd_urls' in playout_data and playout_data['mpd_urls']:
                        print(f"[UI] Found {len(playout_data['mpd_urls'])} MPD URLs in playout data")

                        # Use the codec determined earlier
                        codec_key = 'H264' if codec == 'H.264' else 'H265'

                        # Get the MPD URL based on the selected codec
                        if codec_key in playout_data.get('mpd_urls', {}):
                            episode_mpd_url = playout_data['mpd_urls'][codec_key]
                            print(f"[UI] Using MPD URL for {codec_key}: {episode_mpd_url}")
                        else:
                            print(f"[UI] No MPD URL found for codec {codec_key} in episode {episode_number}")
                            # Try the other codec as fallback
                            fallback_codec_key = 'H265' if codec_key == 'H264' else 'H264'
                            if fallback_codec_key in playout_data.get('mpd_urls', {}):
                                episode_mpd_url = playout_data['mpd_urls'][fallback_codec_key]
                                print(f"[UI] Using fallback MPD URL for {fallback_codec_key}: {episode_mpd_url}")
                                # Update the codec to match the fallback
                                codec = "H.265" if fallback_codec_key == 'H265' else "H.264"
                            else:
                                print(f"[UI] No MPD URL found for any codec in episode {episode_number}")
                                continue  # Skip this episode
                    else:
                        print(f"[UI] No MPD URLs found in playout data for episode {episode_number}")
                        continue  # Skip this episode

                    # If we have a valid MPD URL, extract DRM info
                    if episode_mpd_url:
                        print(f"[UI] Got MPD URL for episode {episode_number}: {episode_mpd_url}")

                        # Extract DRM info for this episode
                        print(f"[UI] Extracting DRM info for episode {episode_number} (ID: {episode_id})")
                        try:
                            episode_drm_info = self.shahid_drm.process_episode_drm(episode_id, episode_mpd_url)
                            if episode_drm_info:
                                print(f"[UI] Successfully extracted DRM info for episode {episode_number}")
                                print(f"[UI] DRM KID: {episode_drm_info.get('kid', 'N/A')}")
                                print(f"[UI] DRM KEY: {episode_drm_info.get('formatted_key', 'N/A')}")
                            else:
                                print(f"[UI] Failed to extract DRM info for episode {episode_number}")
                        except Exception as e:
                            print(f"[UI] Error extracting DRM info for episode {episode_number}: {e}")
                            import traceback
                            traceback.print_exc()
                            # Continue even if DRM extraction fails - we'll try again later
                    else:
                        print(f"[UI] Failed to get MPD URL for episode {episode_number}")
                        continue  # Skip this episode
                else:
                    print(f"[UI] Failed to get playout data for episode {episode_number}")
                    continue  # Skip this episode
            except Exception as e:
                print(f"[UI] Error getting playout URL for episode {episode_number}: {e}")
                continue  # Skip this episode

            # Add to downloads table
            row_position = self.shahid_ui.downloads_table.rowCount()
            self.shahid_ui.downloads_table.insertRow(row_position)

            # Set table items with center alignment
            title_item = QTableWidgetItem(content_title)
            title_item.setTextAlignment(Qt.AlignCenter)
            self.shahid_ui.downloads_table.setItem(row_position, 0, title_item)

            season_item = QTableWidgetItem(str(season_number))
            season_item.setTextAlignment(Qt.AlignCenter)
            self.shahid_ui.downloads_table.setItem(row_position, 1, season_item)

            episode_item = QTableWidgetItem(str(episode_number))
            episode_item.setTextAlignment(Qt.AlignCenter)
            self.shahid_ui.downloads_table.setItem(row_position, 2, episode_item)

            resolution_item = QTableWidgetItem(resolution)
            resolution_item.setTextAlignment(Qt.AlignCenter)
            self.shahid_ui.downloads_table.setItem(row_position, 3, resolution_item)

            # Set codec with center alignment
            codec = "H.264" if self.shahid_ui.h264_checkbox.isChecked() else "H.265"
            codec_item = QTableWidgetItem(codec)
            codec_item.setTextAlignment(Qt.AlignCenter)
            self.shahid_ui.downloads_table.setItem(row_position, 4, codec_item)

            # Create progress bar for the Progress column
            progress_bar = QProgressBar()
            progress_bar.setRange(0, 100)
            progress_bar.setValue(0)
            progress_bar.setTextVisible(True)
            progress_bar.setAlignment(Qt.AlignCenter)
            progress_bar.setFixedHeight(25)  # Set fixed height for better appearance
            progress_bar.setStyleSheet("""
                QProgressBar {
                    border: 1px solid #44475a;
                    border-radius: 5px;
                    text-align: center;
                    background-color: #282a36;
                    color: white;
                    font-weight: bold;
                }
                QProgressBar::chunk {
                    background-color: #50fa7b;
                    border-radius: 2px;
                    margin: 0px;
                    width: 1px;
                }
            """)

            # Set a custom property to track the last update time
            progress_bar.setProperty("last_update_time", time.time())
            self.shahid_ui.downloads_table.setCellWidget(row_position, 5, progress_bar)

            # Set status
            status_item = QTableWidgetItem("Queued")
            status_item.setTextAlignment(Qt.AlignCenter)
            self.shahid_ui.downloads_table.setItem(row_position, 6, status_item)

            # Add cancel button
            buttons_widget = QWidget()
            buttons_layout = QHBoxLayout(buttons_widget)
            buttons_layout.setContentsMargins(0, 0, 0, 0)
            buttons_layout.setAlignment(Qt.AlignCenter)

            cancel_button = QPushButton("Cancel")
            cancel_button.setStyleSheet(
                "background-color: #ff5555; color: white; padding: 5px;"
            )
            cancel_button.clicked.connect(lambda _, row=row_position: self.cancel_download(row))
            buttons_layout.addWidget(cancel_button)

            self.shahid_ui.downloads_table.setCellWidget(row_position, 7, buttons_widget)

            # We already have the episode-specific MPD URL, no need to get it from current_playout_data
            print(f"[UI] Using episode-specific MPD URL for episode {episode_number}: {episode_mpd_url}")

            # Get selected audio tracks
            selected_audio_tracks = []

            # Recorrer todos los checkboxes de audio
            for i in range(self.shahid_ui.audio_group_layout.count()):
                widget = self.shahid_ui.audio_group_layout.itemAt(i).widget()
                if isinstance(widget, QCheckBox) and widget.isChecked():
                    text = widget.text()
                    # Extraer el código del idioma del texto (formato: "name (code)")
                    if '(' in text and ')' in text:
                        code = text.split('(')[1].split(')')[0]
                        selected_audio_tracks.append(code)

            # Verificar que se haya seleccionado al menos una pista de audio
            if not selected_audio_tracks:
                # Si no hay pistas de audio seleccionadas, seleccionar árabe por defecto
                selected_audio_tracks = ["ar"]
                print("[UI] No audio tracks selected, defaulting to Arabic (ar)")

            selected_subtitle_tracks = []

            # Obtener los subtítulos seleccionados de la lista
            subtitle_selected = False
            for i in range(self.shahid_ui.subtitle_list_widget.count()):
                item = self.shahid_ui.subtitle_list_widget.item(i)
                if item.isSelected():
                    subtitle_selected = True

                    # Primero intentar obtener el texto de los datos del ítem (más confiable)
                    subtitle_text = item.data(Qt.UserRole)

                    # Si no hay datos almacenados, intentar obtener el texto del widget
                    if not subtitle_text:
                        # Verificar si el ítem tiene un widget personalizado
                        custom_widget = self.shahid_ui.subtitle_list_widget.itemWidget(item)
                        if custom_widget:
                            # Si tiene un widget personalizado, buscar el QLabel con el texto
                            text_label = custom_widget.findChild(QLabel, "subtitle_text_label")

                            if text_label:
                                subtitle_text = text_label.text()
                            else:
                                # Si no se encuentra el QLabel específico, buscar cualquier QLabel que no sea la viñeta
                                for child in custom_widget.findChildren(QLabel):
                                    if child.text() != "•":
                                        subtitle_text = child.text()
                                        break

                        # Si aún no tenemos texto, usar el texto del ítem
                        if not subtitle_text:
                            subtitle_text = item.text()

                    print(f"[UI] Selected subtitle: {subtitle_text}")

                    # Extraer el código del idioma del texto (formato: "name (code)")
                    if subtitle_text and '(' in subtitle_text and ')' in subtitle_text:
                        try:
                            code = subtitle_text.split('(')[1].split(')')[0]
                            print(f"[UI] Extracted subtitle code: {code}")

                            # Verificar que el código sea válido (2-3 caracteres)
                            if len(code) in [2, 3]:
                                selected_subtitle_tracks.append(code)
                            else:
                                print(f"[UI] Invalid subtitle code length: {code}")
                        except Exception as e:
                            print(f"[UI] Error extracting subtitle code from '{subtitle_text}': {e}")
                    else:
                        print(f"[UI] Could not extract subtitle code from '{subtitle_text}'")

            # Mantener compatibilidad con los checkboxes ocultos
            if self.shahid_ui.subtitle_ar_checkbox.isChecked() and 'ar' not in selected_subtitle_tracks:
                selected_subtitle_tracks.append('ar')
                subtitle_selected = True
                print(f"[UI] Added 'ar' subtitle from hidden checkbox")
            if self.shahid_ui.subtitle_en_checkbox.isChecked() and 'en' not in selected_subtitle_tracks:
                selected_subtitle_tracks.append('en')
                subtitle_selected = True
                print(f"[UI] Added 'en' subtitle from hidden checkbox")

            # Mostrar todos los subtítulos seleccionados
            if selected_subtitle_tracks:
                print(f"[UI] Selected subtitle tracks: {selected_subtitle_tracks}")

            # Si no se seleccionó ningún subtítulo, asegurarse de que la lista esté vacía
            if not subtitle_selected:
                selected_subtitle_tracks = []
                print("[UI] No subtitles selected, subtitles will be disabled")

            # Format audio and subtitle selections for display
            audio_display = ", ".join(selected_audio_tracks) if selected_audio_tracks else "None"
            subtitle_display = ", ".join(selected_subtitle_tracks) if selected_subtitle_tracks else "None"

            # Store episode data in the row with all necessary information
            episode_data_for_download = {
                'id': episode_id,
                'content_type': self.current_content['type'],
                'title': content_title,
                'season': season_number,
                'episode': episode_number,
                'resolution': resolution,
                'codec': codec,
                'mpd_url': episode_mpd_url,  # Use episode-specific MPD URL
                'audio_tracks': selected_audio_tracks,
                'subtitle_tracks': selected_subtitle_tracks,
                'audio_display': audio_display,
                'subtitle_display': subtitle_display,
                'drm_info': episode_drm_info,  # Store episode-specific DRM info
                'playout_data': playout_data  # Store the full playout data for this episode
            }

            # Store the data in the row
            self.shahid_ui.downloads_table.item(row_position, 0).setData(Qt.UserRole, episode_data_for_download)

            # Print debug information
            print(f"[UI] Added episode {episode_number} to download queue with ID: {episode_id}")
            print(f"[UI] Episode MPD URL: {episode_mpd_url}")
            if episode_drm_info:
                print(f"[UI] Episode has valid DRM info with KID: {episode_drm_info.get('kid', 'N/A')}")
            else:
                print(f"[UI] Episode does not have DRM info yet, will extract during download")

        # Print summary of processed episodes
        if hasattr(self, 'processing_episode_ids'):
            print(f"[UI] Processed {len(self.processing_episode_ids)} episodes in total")
            print(f"[UI] Episode IDs: {self.processing_episode_ids}")

        # Switch to downloads tab
        self.shahid_ui.content_tabs.setCurrentIndex(3)  # Show Downloads tab

        # Show success message with count of added episodes
        successfully_added = self.shahid_ui.downloads_table.rowCount()
        QMessageBox.information(self, "Added to Queue", f"{successfully_added} item(s) added to download queue.")

    # DOWNLOAD MANAGEMENT METHODS
    # ///////////////////////////////////////////////////////////////
    def start_download(self):
        """Start downloading queued items."""
        global global_selected_quality, global_selected_series_title, global_selected_seasons
        global global_download_queue, global_currently_downloading, global_download_options

        # Check if there are any items in the queue
        if self.shahid_ui.downloads_table.rowCount() == 0:
            QMessageBox.warning(self, "No Downloads", "There are no items in the download queue.")
            return

        # Check if token exists
        if not self.token:
            QMessageBox.warning(self, "Token Required", "Please enter your Shahid token first.")
            return

        # Count items with "Queued" status
        queued_items = 0
        for row in range(self.shahid_ui.downloads_table.rowCount()):
            status = self.shahid_ui.downloads_table.item(row, 6).text()
            if status == "Queued":
                queued_items += 1

        if queued_items == 0:
            QMessageBox.information(self, "No Queued Items", "There are no queued items to download.")
            return

        # Print global variables for debugging
        print(f"[UI] Global selected quality: {global_selected_quality}")
        print(f"[UI] Global selected series title: {global_selected_series_title}")
        print(f"[UI] Global selected seasons: {global_selected_seasons}")

        # Initialize a list to track episode IDs that will be downloaded
        if not hasattr(self, 'downloading_episode_ids'):
            self.downloading_episode_ids = []
        else:
            # Clear the list to start fresh
            self.downloading_episode_ids = []

        # Clear the global download queue
        global_download_queue = []

        print(f"[UI] Starting sequential download for {queued_items} episodes")

        # Ask for confirmation
        confirm = QMessageBox.question(
            self,
            "Confirm Download",
            f"Start downloading {queued_items} queued item(s)?",
            QMessageBox.Yes | QMessageBox.No
        )

        if confirm != QMessageBox.Yes:
            return

        # Add all queued items to the download queue
        for row in range(self.shahid_ui.downloads_table.rowCount()):
            status = self.shahid_ui.downloads_table.item(row, 6).text()

            # Skip items that are not queued
            if status != "Queued":
                continue

            # Get episode data
            episode_data = self.shahid_ui.downloads_table.item(row, 0).data(Qt.UserRole)

            # Skip if no data
            if not episode_data:
                continue

            # Extract required information
            episode_id = episode_data.get('id')
            content_title = episode_data.get('title')
            season_number = episode_data.get('season')
            episode_number = episode_data.get('episode')
            resolution = episode_data.get('resolution')
            codec = episode_data.get('codec')
            mpd_url = episode_data.get('mpd_url')
            audio_tracks = episode_data.get('audio_tracks', [])
            subtitle_tracks = episode_data.get('subtitle_tracks', [])

            # Add to global download queue
            global_download_queue.append({
                'row': row,
                'episode_id': episode_id,
                'content_title': content_title,
                'season_number': season_number,
                'episode_number': episode_number,
                'resolution': resolution,
                'codec': codec,
                'mpd_url': mpd_url,
                'audio_tracks': audio_tracks,
                'subtitle_tracks': subtitle_tracks,
                'episode_data': episode_data
            })

            print(f"[UI] Added episode {episode_number} to sequential download queue")

            # Convert resolution from "360p" to "640x360" format
            if resolution.endswith('p'):
                height = resolution[:-1]  # Remove the 'p'
                # Map common heights to full resolutions
                resolution_map = {
                    "144": "256x144",
                    "240": "426x240",
                    "252": "426x252",
                    "288": "512x288",
                    "360": "640x360",
                    "468": "832x468",
                    "480": "854x480",
                    "576": "1024x576",
                    "720": "1280x720",
                    "1080": "1920x1080",
                    "1440": "2560x1440",
                    "2160": "3840x2160"
                }
                if height in resolution_map:
                    resolution = resolution_map[height]
                    print(f"[UI] Converted resolution from {resolution} to {resolution_map[height]}")
                else:
                    print(f"[UI] Unknown resolution height: {height}, using as is")

            # Skip if missing required information
            if not all([episode_id, content_title, season_number, episode_number, resolution, codec, mpd_url]):
                print(f"[ERROR] Missing required information for row {row}")
                continue

            # Get DRM info from the episode data
            drm_info = episode_data.get('drm_info')

            # Get episode-specific playout data
            playout_data = episode_data.get('playout_data')

            # Print detailed information for debugging
            print(f"[UI] Using episode-specific data for row {row}:")
            print(f"[UI] Episode ID: {episode_id}")
            print(f"[UI] MPD URL: {mpd_url}")
            print(f"[UI] DRM info: {drm_info}")

            # Check if playout data is available
            if playout_data:
                print(f"[UI] Episode has playout data with {len(playout_data.get('mpd_urls', {}))} MPD URLs")
                # If no MPD URL was found earlier, try to get it from playout data
                if not mpd_url and 'mpd_urls' in playout_data:
                    codec_key = 'H264' if codec == 'H.264' else 'H265'
                    if codec_key in playout_data['mpd_urls']:
                        mpd_url = playout_data['mpd_urls'][codec_key]
                        print(f"[UI] Using MPD URL from playout data: {mpd_url}")
            else:
                print(f"[UI] No playout data available for episode {episode_number}")

            if not drm_info:
                print(f"[UI] Warning: No DRM info found for episode {episode_number}, attempting to extract it now")
                if mpd_url and episode_id:
                    try:
                        # Try to extract DRM info from the MPD URL
                        drm_info = self.shahid_drm.process_episode_drm(episode_id, mpd_url)
                        print(f"[UI] Extracted DRM info: {drm_info}")
                    except Exception as e:
                        print(f"[UI] Error extracting DRM info: {e}")
                        import traceback
                        traceback.print_exc()
                else:
                    print(f"[UI] Error: No MPD URL or episode ID available for episode {episode_number}")

            # Start the download
            print(f"[UI] Starting download for row {row}: {content_title} S{season_number}E{episode_number}")

            # Connect signals if not already connected
            if not hasattr(self, 'signals_connected'):
                self.shahid_downloader.signals.progress_updated.connect(self.update_download_progress)
                self.shahid_downloader.signals.status_updated.connect(self.update_download_status)
                self.shahid_downloader.signals.overall_progress_updated.connect(self.update_overall_progress)
                self.signals_connected = True

        # Iniciar la descarga secuencial
        if global_download_queue:
            # Guardar las opciones de descarga para usarlas en todos los episodios
            global_download_options = {
                'resolution': resolution,
                'codec': codec,
                'audio_tracks': audio_tracks,
                'subtitle_tracks': subtitle_tracks
            }

            # Iniciar la descarga del primer episodio
            self.start_next_download()
        else:
            print("[UI] No episodes in download queue")

    def start_next_download(self):
        """Start downloading the next episode in the queue."""
        global global_download_queue, global_currently_downloading, global_download_options

        # Si ya hay una descarga en progreso o no hay episodios en la cola, salir
        if global_currently_downloading or not global_download_queue:
            return

        # Marcar que estamos descargando
        global_currently_downloading = True

        # Obtener el siguiente episodio de la cola
        episode = global_download_queue[0]
        row = episode['row']
        episode_id = episode['episode_id']
        episode_number = episode['episode_number']

        print(f"[UI] Starting download for episode {episode_number} (queue size: {len(global_download_queue)})")

        # Obtener datos de reproducción actualizados para este episodio específico
        print(f"[UI] Refreshing playout data for episode {episode_number} (ID: {episode_id})")
        try:
            # Obtener datos de reproducción actualizados
            playout_data = self.shahid_api.get_episode_playout_url(episode_id)

            if playout_data and 'mpd_urls' in playout_data and playout_data['mpd_urls']:
                # Determinar qué codec usar
                codec = episode['codec']
                codec_key = 'H264' if codec == 'H.264' else 'H265'

                # Obtener la URL de MPD basada en el codec seleccionado
                if codec_key in playout_data['mpd_urls']:
                    mpd_url = playout_data['mpd_urls'][codec_key]
                    print(f"[UI] Refreshed MPD URL for episode {episode_number}: {mpd_url}")
                    episode['mpd_url'] = mpd_url
                else:
                    # Intentar con el codec alternativo como respaldo
                    fallback_codec_key = 'H265' if codec_key == 'H264' else 'H264'
                    if fallback_codec_key in playout_data['mpd_urls']:
                        mpd_url = playout_data['mpd_urls'][fallback_codec_key]
                        print(f"[UI] Using fallback MPD URL for {fallback_codec_key}: {mpd_url}")
                        episode['mpd_url'] = mpd_url
                        # Actualizar el codec para que coincida con el respaldo
                        episode['codec'] = "H.265" if fallback_codec_key == 'H265' else "H.264"

                # Si tenemos una URL de MPD válida, extraer información DRM
                if episode['mpd_url']:
                    print(f"[UI] Extracting fresh DRM info for episode {episode_number}")
                    try:
                        drm_info = self.shahid_drm.process_episode_drm(episode_id, episode['mpd_url'])
                        if drm_info:
                            print(f"[UI] Successfully extracted fresh DRM info for episode {episode_number}")
                            print(f"[UI] DRM KID: {drm_info.get('kid', 'N/A')}")
                            print(f"[UI] DRM KEY: {drm_info.get('formatted_key', 'N/A')}")
                            episode['episode_data']['drm_info'] = drm_info
                        else:
                            print(f"[UI] Failed to extract fresh DRM info for episode {episode_number}")
                    except Exception as e:
                        print(f"[UI] Error extracting fresh DRM info for episode {episode_number}: {e}")
                        import traceback
                        traceback.print_exc()
            else:
                print(f"[UI] Failed to refresh playout data for episode {episode_number}")
        except Exception as e:
            print(f"[UI] Error refreshing data for episode {episode_number}: {e}")
            import traceback
            traceback.print_exc()

        # Verificar que tenemos toda la información necesaria
        if not episode['mpd_url']:
            print(f"[ERROR] No MPD URL available for row {row}, skipping download")
            self.update_download_status(row, "Error: No MPD URL")
            self.finish_current_download()
            return

        # Obtener la información DRM del episodio
        drm_info = episode['episode_data'].get('drm_info')
        if not drm_info:
            print(f"[WARNING] No DRM info available for row {row}, download may fail")

        # Iniciar la descarga según el tipo de contenido
        content_type = episode['episode_data'].get('content_type', 'SERIES')

        # Conectar la señal de descarga completada si no está conectada
        if not hasattr(self, 'download_completed_connected'):
            self.shahid_downloader.signals.download_completed.connect(self.on_download_completed)
            self.download_completed_connected = True

        if content_type == "MOVIE":
            print(f"[UI] Starting movie download for row {row}: {episode['content_title']}")
            # Para películas, usar download_movie con season_number y episode_number en 0
            self.shahid_downloader.download_movie(
                row,
                episode['mpd_url'],
                episode['resolution'],
                episode['audio_tracks'],
                episode['subtitle_tracks'],
                episode['content_title'],
                drm_info
            )
        else:
            print(f"[UI] Starting series download for row {row}: {episode['content_title']} S{episode['season_number']}E{episode['episode_number']}")
            # Para series, usar download_series_with_selected_quality
            self.shahid_downloader.download_series_with_selected_quality(
                row,
                episode['mpd_url'],
                episode['resolution'],
                episode['audio_tracks'],
                episode['subtitle_tracks'],
                episode['content_title'],
                episode['season_number'],
                episode['episode_number'],
                drm_info
            )

        # Store the episode ID in a global list to track which episodes are being downloaded
        self.downloading_episode_ids.append(episode['episode_id'])
        print(f"[UI] Added episode ID {episode['episode_id']} to downloading list")
        print(f"[UI] Total episodes in download queue: {len(self.downloading_episode_ids)}")

        # Update status to "Starting"
        status_item = self.shahid_ui.downloads_table.item(row, 6)
        status_item.setText("Starting")

        # Update progress to 0%
        progress_bar = self.shahid_ui.downloads_table.cellWidget(row, 5)
        if progress_bar and isinstance(progress_bar, QProgressBar):
            progress_bar.setValue(0)

        # Process events to update UI
        QApplication.processEvents()

    def clear_completed_downloads(self):
        """Clear completed downloads from the table."""
        rows_to_remove = []

        for row in range(self.shahid_ui.downloads_table.rowCount()):
            status = self.shahid_ui.downloads_table.item(row, 6).text()
            if status == "Completed":
                rows_to_remove.append(row)

        # Remove rows in reverse order to avoid index issues
        for row in sorted(rows_to_remove, reverse=True):
            self.shahid_ui.downloads_table.removeRow(row)

    def clear_all_downloads(self):
        """Clear all downloads from the table."""
        # Ask for confirmation if there are active downloads
        active_downloads = False
        for row in range(self.shahid_ui.downloads_table.rowCount()):
            status = self.shahid_ui.downloads_table.item(row, 6).text()
            if status in ["Downloading", "Starting", "Merging"]:
                active_downloads = True
                break

        if active_downloads:
            confirm = QMessageBox.question(
                self,
                "Active Downloads",
                "There are active downloads. Cancel them and clear all?",
                QMessageBox.Yes | QMessageBox.No
            )

            if confirm != QMessageBox.Yes:
                return

            # Cancel all active downloads
            for row in range(self.shahid_ui.downloads_table.rowCount()):
                status = self.shahid_ui.downloads_table.item(row, 6).text()
                if status in ["Downloading", "Starting", "Merging"]:
                    self.shahid_downloader.cancel_download(row)

        # Clear the table
        self.shahid_ui.downloads_table.setRowCount(0)
        self.shahid_ui.overall_progress_label.setText("Overall Progress: 0%")

    def update_download_progress(self, row, progress):
        """Update the progress of a download."""
        print(f"[UI] Updating progress for row {row}: {progress}%")
        if row < self.shahid_ui.downloads_table.rowCount():
            progress_bar = self.shahid_ui.downloads_table.cellWidget(row, 5)
            if progress_bar and isinstance(progress_bar, QProgressBar):
                # Ensure progress is within valid range
                progress = max(0, min(100, progress))

                # Get the current time
                current_time = time.time()

                # Get the last update time from the progress bar property
                last_update_time = progress_bar.property("last_update_time") or 0

                # Only update if progress has changed or it's been more than 0.5 seconds since the last update
                current_value = progress_bar.value()
                if progress != current_value or (current_time - last_update_time) > 0.5:
                    # Update the progress bar
                    progress_bar.setValue(progress)

                    # Update the last update time
                    progress_bar.setProperty("last_update_time", current_time)

                    # Update the status text if needed
                    status_item = self.shahid_ui.downloads_table.item(row, 6)
                    if status_item and status_item.text() == "Starting" and progress > 0:
                        status_item.setText("Downloading")

                    # Force update the UI
                    QApplication.processEvents()
                    print(f"[UI] Progress bar updated to {progress}%")

    def update_download_status(self, row, status):
        """Update the status of a download."""
        if row < self.shahid_ui.downloads_table.rowCount():
            status_item = self.shahid_ui.downloads_table.item(row, 6)
            if status_item:
                status_item.setText(status)

    def update_overall_progress(self, progress):
        """Update the overall progress."""
        self.shahid_ui.overall_progress_label.setText(f"Overall Progress: {progress}%")

    def on_download_completed(self, row, success):
        """Handle download completion and start the next download."""
        global global_download_queue, global_currently_downloading

        print(f"[UI] Download completed for row {row} with success: {success}")

        # Actualizar el estado en la interfaz
        if success:
            self.update_download_status(row, "Completed")
            self.update_download_progress(row, 100)  # Asegurar que la barra de progreso esté al 100%
        else:
            self.update_download_status(row, "Failed")

        # Verificar que la cola no esté vacía antes de intentar eliminar un episodio
        if global_download_queue:
            # Guardar información del episodio completado para el registro
            completed_episode = global_download_queue[0]
            episode_number = completed_episode['episode_number']
            episode_id = completed_episode['episode_id']

            # Eliminar el episodio actual de la cola
            global_download_queue.pop(0)
            print(f"[UI] Removed episode {episode_number} (ID: {episode_id}) from queue")
            print(f"[UI] Remaining episodes in queue: {len(global_download_queue)}")

            # Procesar eventos para actualizar la UI
            QApplication.processEvents()
        else:
            print("[UI] Warning: Download queue was empty when trying to remove completed episode")

        # Marcar que ya no estamos descargando
        global_currently_downloading = False

        # Si hay más episodios en la cola, iniciar el siguiente
        if global_download_queue:
            print(f"[UI] Starting next episode in queue after a short delay")
            # Esperar un momento antes de iniciar la siguiente descarga (3 segundos)
            QTimer.singleShot(3000, self.start_next_download)
        else:
            print(f"[UI] All episodes have been downloaded")
            QMessageBox.information(self, "Downloads Complete", "All episodes have been downloaded.")

    def finish_current_download(self):
        """Finish the current download and move to the next one."""
        global global_download_queue, global_currently_downloading

        # Verificar que la cola no esté vacía antes de intentar eliminar un episodio
        if global_download_queue:
            # Guardar información del episodio omitido para el registro
            skipped_episode = global_download_queue[0]
            episode_number = skipped_episode['episode_number']
            episode_id = skipped_episode['episode_id']

            # Eliminar el episodio actual de la cola
            global_download_queue.pop(0)
            print(f"[UI] Skipped episode {episode_number} (ID: {episode_id})")
            print(f"[UI] Remaining episodes in queue: {len(global_download_queue)}")

            # Procesar eventos para actualizar la UI
            QApplication.processEvents()
        else:
            print("[UI] Warning: Download queue was empty when trying to skip episode")

        # Marcar que ya no estamos descargando
        global_currently_downloading = False

        # Si hay más episodios en la cola, iniciar el siguiente
        if global_download_queue:
            print(f"[UI] Starting next episode in queue after skipping")
            # Esperar un momento antes de iniciar la siguiente descarga (2 segundos)
            QTimer.singleShot(2000, self.start_next_download)
        else:
            print(f"[UI] No more episodes in queue after skipping")

    def cancel_download(self, row):
        """Cancel a download."""
        global global_download_queue, global_currently_downloading

        if row >= self.shahid_ui.downloads_table.rowCount():
            return

        status = self.shahid_ui.downloads_table.item(row, 6).text()
        episode_data = self.shahid_ui.downloads_table.item(row, 0).data(Qt.UserRole)

        # Only cancel active downloads
        if status in ["Downloading", "Starting", "Merging", "Queued"]:
            # Ask for confirmation
            confirm = QMessageBox.question(
                self,
                "Cancel Download",
                "Are you sure you want to cancel this download?",
                QMessageBox.Yes | QMessageBox.No
            )

            if confirm != QMessageBox.Yes:
                return

            # If it's an active download, cancel it
            if status in ["Downloading", "Starting", "Merging"]:
                # Si es el episodio actual en la cola, cancelarlo y pasar al siguiente
                if global_currently_downloading and global_download_queue and row == global_download_queue[0]['row']:
                    print(f"[UI] Cancelling current download for row {row}")
                    self.shahid_downloader.cancel_download(row)
                    # Terminar la descarga actual y pasar a la siguiente
                    self.finish_current_download()
                else:
                    # Si no es el episodio actual, solo cancelar la descarga
                    print(f"[UI] Cancelling download for row {row}")
                    self.shahid_downloader.cancel_download(row)
            # If it's queued, remove it from the queue and update the status
            elif status == "Queued":
                # Obtener el ID del episodio para identificarlo en la cola
                if episode_data and 'id' in episode_data:
                    episode_id = episode_data['id']
                    # Buscar y eliminar el episodio de la cola global
                    for i, queued_episode in enumerate(global_download_queue):
                        if queued_episode['episode_id'] == episode_id:
                            removed_episode = global_download_queue.pop(i)
                            print(f"[UI] Removed episode {removed_episode['episode_number']} from queue")
                            break

                # Actualizar el estado en la interfaz
                self.update_download_status(row, "Cancelled")
        else:
            QMessageBox.information(self, "Cannot Cancel", f"Cannot cancel download with status: {status}")

    def process_drm_info(self, episode_id, mpd_url):
        """Process DRM information for the selected episode."""
        print(f"[UI] Processing DRM information for episode ID: {episode_id}")

        # Mostrar un diálogo de progreso
        progress_dialog = QProgressDialog("Obteniendo información DRM...", "Cancelar", 0, 0, self)
        progress_dialog.setWindowTitle("Procesando DRM")
        progress_dialog.setWindowModality(Qt.WindowModal)
        progress_dialog.setMinimumDuration(0)
        progress_dialog.setValue(0)
        progress_dialog.show()
        QApplication.processEvents()

        try:
            # Procesar la información DRM
            drm_info = self.shahid_drm.process_episode_drm(episode_id, mpd_url)

            if drm_info:
                # Guardar la información DRM en el objeto current_playout_data
                self.current_playout_data['drm_info'] = drm_info

                # Mostrar la información DRM en la interfaz
                self.shahid_ui.drm_info_label.setText(
                    f"<b>DRM Info:</b><br>"
                    f"<b>KID:</b> {drm_info['kid']}<br>"
                    f"<b>KEY:</b> {drm_info['formatted_key']}"
                )
                self.shahid_ui.drm_info_label.setVisible(True)

                print(f"[UI] DRM information processed successfully:")
                print(f"[UI] KID: {drm_info['kid']}")
                print(f"[UI] KEY: {drm_info['formatted_key']}")

                # Enable play buttons now that we have MPD URL and DRM key
                self.shahid_ui.play_button.setEnabled(True)
                self.shahid_ui.seasons_play_button.setEnabled(True)
                self.shahid_ui.download_options_play_button.setEnabled(True)
            else:
                print("[UI] Failed to process DRM information")
                self.shahid_ui.drm_info_label.setText("<b>DRM Info:</b> Failed to process DRM information")
                self.shahid_ui.drm_info_label.setVisible(True)
        except Exception as e:
            print(f"[ERROR] Error processing DRM information: {e}")
            import traceback
            traceback.print_exc()
            self.shahid_ui.drm_info_label.setText(f"<b>DRM Error:</b> {str(e)}")
            self.shahid_ui.drm_info_label.setVisible(True)
        finally:
            progress_dialog.close()

    def play_content(self, mpd_url, drm_key=None):
        """Show player options and play content."""
        if not mpd_url:
            QMessageBox.warning(self, "Play Error", "No MPD URL available for playback.")
            return False

        print(f"[UI] Playing content with MPD URL: {mpd_url}")
        if drm_key:
            print(f"[UI] Using DRM key: {drm_key}")

        # Skip player selection dialog and directly open Chrome Player (best option)
        print("[UI] Auto-starting Chrome Player (best performance)...")
        return self.play_chrome_player_direct(mpd_url, drm_key)

    def play_chrome_player_direct(self, mpd_url, drm_key):
        """Play content using Chrome player in browser directly without dialog."""
        # Show a progress dialog
        progress_dialog = QProgressDialog("Starting Chrome Player...", "Cancel", 0, 0, self)
        progress_dialog.setWindowTitle("Starting Player")
        progress_dialog.setWindowModality(Qt.WindowModal)
        progress_dialog.setMinimumDuration(0)
        progress_dialog.setValue(0)
        progress_dialog.show()
        QApplication.processEvents()

        try:
            # Open Chrome player in browser (separate window)
            from modules.shahid_player import play_in_browser

            success = play_in_browser(
                mpd_url=mpd_url,
                drm_key=drm_key,
                parent=self
            )

            if success:
                print("[UI] Chrome Player opened in browser successfully")
                progress_dialog.close()
                return True
            else:
                print("[UI] Failed to open Chrome player in browser")
                QMessageBox.warning(self, "Player Error", "Failed to open the Chrome player in browser. Make sure the HTTP server started correctly.")
                return False

        except Exception as e:
            print(f"[ERROR] Error starting Chrome Player in browser: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "Player Error", f"Error starting Chrome player in browser: {str(e)}")
            return False
        finally:
            progress_dialog.close()

    def play_embedded(self, mpd_url, drm_key, dialog):
        """Play content using the embedded player widget."""
        dialog.accept()

        # Show a progress dialog
        progress_dialog = QProgressDialog("Starting Embedded Player...", "Cancel", 0, 0, self)
        progress_dialog.setWindowTitle("Starting Player")
        progress_dialog.setWindowModality(Qt.WindowModal)
        progress_dialog.setMinimumDuration(0)
        progress_dialog.setValue(0)
        progress_dialog.show()
        QApplication.processEvents()

        try:
            # Create embedded player widget
            from modules.shahid_player import create_embedded_player

            player_widget = create_embedded_player(
                mpd_url=mpd_url,
                drm_key=drm_key,
                parent=self
            )

            if player_widget:
                # Show the embedded player in a new tab or replace current content
                self.show_embedded_player(player_widget)
                print("[UI] Embedded Player started successfully")
                progress_dialog.close()
                return True
            else:
                print("[UI] Failed to create embedded player")
                QMessageBox.warning(self, "Player Error", "Failed to create the embedded player.")
                return False
        except Exception as e:
            print(f"[ERROR] Error starting Embedded Player: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "Player Error", f"Error starting player: {str(e)}")
            return False
        finally:
            progress_dialog.close()

    def play_real_browser(self, mpd_url, drm_key, dialog):
        """Play content using real browser embedded in app."""
        dialog.accept()

        # Show a progress dialog
        progress_dialog = QProgressDialog("Starting Real Browser Player...", "Cancel", 0, 0, self)
        progress_dialog.setWindowTitle("Starting Player")
        progress_dialog.setWindowModality(Qt.WindowModal)
        progress_dialog.setMinimumDuration(0)
        progress_dialog.setValue(0)
        progress_dialog.show()
        QApplication.processEvents()

        try:
            # Create real browser player
            from modules.browser_player import create_browser_player

            player_widget = create_browser_player(
                mpd_url=mpd_url,
                drm_key=drm_key,
                parent=self
            )

            if player_widget:
                # Show the browser player in a new tab or replace current content
                self.show_browser_player(player_widget)
                print("[UI] Real Browser Player started successfully")
                progress_dialog.close()
                return True
            else:
                print("[UI] Failed to create real browser player")
                QMessageBox.warning(self, "Player Error", "Failed to create the real browser player.")
                return False
        except Exception as e:
            print(f"[ERROR] Error starting Real Browser Player: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "Player Error", f"Error starting player: {str(e)}")
            return False
        finally:
            progress_dialog.close()

    def play_chrome_player(self, mpd_url, drm_key, dialog):
        """Play content using Chrome embedded player."""
        dialog.accept()

        # Show a progress dialog
        progress_dialog = QProgressDialog("Starting Chrome Player...", "Cancel", 0, 0, self)
        progress_dialog.setWindowTitle("Starting Player")
        progress_dialog.setWindowModality(Qt.WindowModal)
        progress_dialog.setMinimumDuration(0)
        progress_dialog.setValue(0)
        progress_dialog.show()
        QApplication.processEvents()

        try:
            # Create Chrome player
            from modules.shahid_player import create_chrome_player

            player_widget = create_chrome_player(
                mpd_url=mpd_url,
                drm_key=drm_key,
                parent=self
            )

            if player_widget:
                # Show the embedded player in a new tab or replace current content
                self.show_embedded_player(player_widget)
                print("[UI] Chrome Player started successfully")
                progress_dialog.close()
                return True
            else:
                print("[UI] Failed to create Chrome player")
                QMessageBox.warning(self, "Player Error", "Failed to create the Chrome player. Make sure Google Chrome is installed.")
                return False
        except Exception as e:
            print(f"[ERROR] Error starting Chrome Player: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "Player Error", f"Error starting Chrome player: {str(e)}")
            return False
        finally:
            progress_dialog.close()

    def play_external_browser(self, mpd_url, drm_key, dialog):
        """Play content in external browser window."""
        dialog.accept()

        # Show a progress dialog
        progress_dialog = QProgressDialog("Starting External Browser...", "Cancel", 0, 0, self)
        progress_dialog.setWindowTitle("Starting Player")
        progress_dialog.setWindowModality(Qt.WindowModal)
        progress_dialog.setMinimumDuration(0)
        progress_dialog.setValue(0)
        progress_dialog.show()
        QApplication.processEvents()

        try:
            # Play in browser
            from modules.shahid_player import play_in_browser

            success = play_in_browser(
                mpd_url=mpd_url,
                drm_key=drm_key,
                parent=self
            )

            if success:
                print("[UI] Browser Player started successfully")
                progress_dialog.close()
                QMessageBox.information(self, "Player Started", "Player opened in browser successfully!")
                return True
            else:
                print("[UI] Failed to start browser player")
                QMessageBox.warning(self, "Player Error", "Failed to start the browser player.")
                return False
        except Exception as e:
            print(f"[ERROR] Error starting Browser Player: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "Player Error", f"Error starting player: {str(e)}")
            return False
        finally:
            progress_dialog.close()

    def play_cef_player(self, mpd_url, drm_key, dialog):
        """Play content using CEF (Chromium Embedded Framework) player."""
        dialog.accept()

        # Show a progress dialog
        progress_dialog = QProgressDialog("Starting CEF Player...", "Cancel", 0, 0, self)
        progress_dialog.setWindowTitle("Starting Player")
        progress_dialog.setWindowModality(Qt.WindowModal)
        progress_dialog.setMinimumDuration(0)
        progress_dialog.setValue(0)
        progress_dialog.show()
        QApplication.processEvents()

        try:
            # Create CEF player
            from modules.cef_player import create_cef_player

            player_widget = create_cef_player(
                mpd_url=mpd_url,
                drm_key=drm_key,
                parent=self
            )

            if player_widget:
                # Show the embedded player in a new tab or replace current content
                self.show_embedded_player(player_widget)
                print("[UI] CEF Player started successfully")
                progress_dialog.close()
                return True
            else:
                print("[UI] Failed to create CEF player")
                QMessageBox.warning(self, "Player Error", "Failed to create the CEF player.")
                return False
        except Exception as e:
            print(f"[ERROR] Error starting CEF Player: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "Player Error", f"Error starting player: {str(e)}")
            return False
        finally:
            progress_dialog.close()

    def show_embedded_player(self, player_widget):
        """Show the embedded player widget in the application."""
        # Create a new tab for the player or use existing player area
        if not hasattr(self, 'player_tab'):
            # Create player tab
            self.player_tab = QWidget()
            self.player_layout = QVBoxLayout(self.player_tab)
            self.player_layout.setContentsMargins(0, 0, 0, 0)

            # Add close button
            close_button_layout = QHBoxLayout()
            close_button_layout.addStretch()

            self.close_player_button = QPushButton("Close Player")
            self.close_player_button.setStyleSheet("""
                QPushButton {
                    background-color: #ff5555;
                    color: white;
                    padding: 8px 16px;
                    border-radius: 4px;
                    border: none;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #ff4444;
                }
            """)
            self.close_player_button.clicked.connect(self.close_embedded_player)
            close_button_layout.addWidget(self.close_player_button)

            self.player_layout.addLayout(close_button_layout)

            # Add to stacked widget
            if hasattr(self, 'ui') and hasattr(self.ui, 'stackedWidget'):
                self.ui.stackedWidget.addWidget(self.player_tab)
            elif 'widgets' in globals() and widgets is not None:
                widgets.stackedWidget.addWidget(self.player_tab)

        # Clear existing player if any
        if hasattr(self, 'current_player_widget') and self.current_player_widget:
            self.player_layout.removeWidget(self.current_player_widget)
            self.current_player_widget.deleteLater()

        # Add new player widget
        self.current_player_widget = player_widget
        self.player_layout.addWidget(player_widget)

        # Switch to player tab
        if hasattr(self, 'ui') and hasattr(self.ui, 'stackedWidget'):
            self.ui.stackedWidget.setCurrentWidget(self.player_tab)
        elif 'widgets' in globals() and widgets is not None:
            widgets.stackedWidget.setCurrentWidget(self.player_tab)

        print("[UI] Switched to embedded player view")

    def show_browser_player(self, player_widget):
        """Show the browser player widget in the main interface."""
        try:
            # Create player tab if it doesn't exist
            if not hasattr(self, 'browser_player_tab'):
                self.browser_player_tab = QWidget()
                self.browser_player_layout = QVBoxLayout(self.browser_player_tab)
                self.browser_player_layout.setContentsMargins(0, 0, 0, 0)

                # Add close button
                close_button_layout = QHBoxLayout()
                close_button_layout.addStretch()

                self.close_browser_player_button = QPushButton("Close Browser Player")
                self.close_browser_player_button.setStyleSheet("""
                    QPushButton {
                        background-color: #ff5555;
                        color: white;
                        padding: 8px 16px;
                        border-radius: 4px;
                        border: none;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background-color: #ff4444;
                    }
                """)
                self.close_browser_player_button.clicked.connect(self.close_browser_player)
                close_button_layout.addWidget(self.close_browser_player_button)

                self.browser_player_layout.addLayout(close_button_layout)

                # Add to stacked widget
                if hasattr(self, 'ui') and hasattr(self.ui, 'stackedWidget'):
                    self.ui.stackedWidget.addWidget(self.browser_player_tab)
                elif 'widgets' in globals() and widgets is not None:
                    widgets.stackedWidget.addWidget(self.browser_player_tab)

            # Clear existing player if any
            if hasattr(self, 'current_browser_player_widget') and self.current_browser_player_widget:
                self.browser_player_layout.removeWidget(self.current_browser_player_widget)
                self.current_browser_player_widget.deleteLater()

            # Add new player widget
            self.current_browser_player_widget = player_widget
            self.browser_player_layout.addWidget(player_widget)

            # Switch to browser player tab
            if hasattr(self, 'ui') and hasattr(self.ui, 'stackedWidget'):
                self.ui.stackedWidget.setCurrentWidget(self.browser_player_tab)
            elif 'widgets' in globals() and widgets is not None:
                widgets.stackedWidget.setCurrentWidget(self.browser_player_tab)

            print("[UI] Switched to browser player view")

        except Exception as e:
            print(f"[ERROR] Error showing browser player: {e}")
            import traceback
            traceback.print_exc()

    def close_browser_player(self):
        """Close the browser player and return to previous view."""
        if hasattr(self, 'current_browser_player_widget') and self.current_browser_player_widget:
            self.browser_player_layout.removeWidget(self.current_browser_player_widget)
            self.current_browser_player_widget.close_browser()  # Close the actual browser
            self.current_browser_player_widget.deleteLater()
            self.current_browser_player_widget = None

        # Return to home view
        if hasattr(self, 'ui') and hasattr(self.ui, 'stackedWidget'):
            self.ui.stackedWidget.setCurrentWidget(self.ui.home)
        elif 'widgets' in globals() and widgets is not None:
            widgets.stackedWidget.setCurrentWidget(widgets.home)
        print("[UI] Closed browser player")

    def close_embedded_player(self):
        """Close the embedded player and return to previous view."""
        if hasattr(self, 'current_player_widget') and self.current_player_widget:
            self.player_layout.removeWidget(self.current_player_widget)
            self.current_player_widget.deleteLater()
            self.current_player_widget = None

        # Return to home view
        if hasattr(self, 'ui') and hasattr(self.ui, 'stackedWidget'):
            self.ui.stackedWidget.setCurrentWidget(self.ui.home)
        elif 'widgets' in globals() and widgets is not None:
            widgets.stackedWidget.setCurrentWidget(widgets.home)
        print("[UI] Closed embedded player")

    def play_selected_content(self):
        """Play the selected content using the player."""
        if not hasattr(self, 'current_playout_data') or not self.current_playout_data:
            QMessageBox.warning(self, "Play Error", "No content selected for playback.")
            return

        # Get MPD URL from current playout data
        mpd_url = self.current_playout_data.get('mpd_url')
        if not mpd_url:
            QMessageBox.warning(self, "Play Error", "No MPD URL available for playback. Please select an episode or movie first.")
            return

        # Get DRM info if available
        drm_info = self.current_playout_data.get('drm_info')
        if not drm_info:
            QMessageBox.warning(self, "Play Error", "DRM information not available. Please select an episode or movie first.")
            return

        drm_key = None
        if 'formatted_key' in drm_info:
            drm_key = drm_info['formatted_key']
        else:
            QMessageBox.warning(self, "Play Error", "DRM key not available. Please select an episode or movie first.")
            return

        # Play the content
        self.play_content(mpd_url, drm_key)

    def update_resolutions_for_codec(self, codec):
        """Update available resolutions based on selected codec."""
        if not hasattr(self, 'current_playout_data'):
            return

        # Limpiar el combo de resoluciones
        self.shahid_ui.resolution_combo.clear()

        # Obtener las calidades disponibles para el codec seleccionado
        qualities = []

        # Usar las calidades específicas del codec si están disponibles
        if codec == "H264" and 'h264_qualities' in self.current_playout_data:
            codec_qualities = self.current_playout_data.get('h264_qualities', [])
            print(f"[UI] Using H264-specific qualities: {codec_qualities}")

            # Extraer las resoluciones
            for quality in codec_qualities:
                if 'x' in quality[0]:
                    # Usar _ para ignorar la variable width que no se usa
                    _, height = quality[0].split('x')
                    qualities.append(f"{height}p")
        elif codec == "H265" and 'h265_qualities' in self.current_playout_data:
            codec_qualities = self.current_playout_data.get('h265_qualities', [])
            print(f"[UI] Using H265-specific qualities: {codec_qualities}")

            # Extraer las resoluciones
            for quality in codec_qualities:
                if 'x' in quality[0]:
                    # Usar _ para ignorar la variable width que no se usa
                    _, height = quality[0].split('x')
                    qualities.append(f"{height}p")
        else:
            # Intentar con las calidades generales del MPD
            mpd_qualities = self.current_playout_data.get('mpd_qualities', [])
            if mpd_qualities:
                # Filtrar las calidades por codec si es posible
                for quality in mpd_qualities:
                    if len(quality) > 1 and codec.lower() in quality[1].lower():
                        if 'x' in quality[0]:
                            # Usar _ para ignorar la variable width que no se usa
                            _, height = quality[0].split('x')
                            qualities.append(f"{height}p")

        # Si no hay calidades específicas para este codec, intentar con los codecs disponibles
        if not qualities:
            available_codecs = self.current_playout_data.get('available_codecs', [])
            for codec_info in available_codecs:
                if codec_info.get('name', '').upper() == codec:
                    resolution = codec_info.get('resolution', '')
                    if 'x' in resolution:
                        # Usar _ para ignorar la variable width que no se usa
                        _, height = resolution.split('x')
                        qualities.append(f"{height}p")
                    elif resolution == 'HD':
                        qualities.append("720p")
                    elif resolution == '4K':
                        qualities.append("2160p")

        # Si no hay calidades disponibles, dejar el combobox vacío
        if not qualities:
            print(f"[UI] No qualities available for codec {codec}")
            # Deshabilitar el combobox para indicar que no hay opciones disponibles
            self.shahid_ui.resolution_combo.setEnabled(False)
            # Mostrar un mensaje en el combobox
            self.shahid_ui.resolution_combo.addItem("No qualities available")
            return

        # Eliminar duplicados y ordenar
        unique_qualities = sorted(set(qualities), key=lambda x: int(x[:-1]), reverse=True)

        # Habilitar el combobox ya que hay calidades disponibles
        self.shahid_ui.resolution_combo.setEnabled(True)

        # Agregar al combo
        self.shahid_ui.resolution_combo.addItems(unique_qualities)

        # Seleccionar 360p por defecto si está disponible
        if "360p" in unique_qualities:
            self.shahid_ui.resolution_combo.setCurrentText("360p")
        else:
            # Si no está disponible 360p, seleccionar la resolución más baja
            self.shahid_ui.resolution_combo.setCurrentIndex(len(unique_qualities) - 1)

        print(f"[UI] Updated resolutions for codec {codec}: {unique_qualities}")

    def update_audio_tracks_for_codec(self, codec):
        """Update available audio tracks based on selected codec."""
        if not hasattr(self, 'current_playout_data'):
            return

        # Obtener las pistas de audio disponibles según el codec
        audio_tracks = []
        if codec == "H264" and 'h264_audio_tracks' in self.current_playout_data:
            audio_tracks = self.current_playout_data.get('h264_audio_tracks', [])
            print(f"[UI] Using H264-specific audio tracks: {audio_tracks}")
        elif codec == "H265" and 'h265_audio_tracks' in self.current_playout_data:
            audio_tracks = self.current_playout_data.get('h265_audio_tracks', [])
            print(f"[UI] Using H265-specific audio tracks: {audio_tracks}")
        else:
            # Usar las pistas de audio generales
            audio_tracks = self.current_playout_data.get('mpd_audio_tracks', [])
            print(f"[UI] Using general audio tracks: {audio_tracks}")

        if not audio_tracks:
            print(f"[UI] No audio tracks found for codec {codec}")

            # Limpiar todos los checkboxes de audio existentes
            while self.shahid_ui.audio_group_layout.count() > 0:
                item = self.shahid_ui.audio_group_layout.takeAt(0)
                if item.widget():
                    item.widget().deleteLater()

            # Agregar un mensaje indicando que no hay pistas de audio disponibles
            label = QLabel("No audio tracks available")
            label.setStyleSheet("color: #f8f8f2;")
            self.shahid_ui.audio_group_layout.addWidget(label)
            return

        # Limpiar todos los checkboxes de audio existentes
        while self.shahid_ui.audio_group_layout.count() > 0:
            item = self.shahid_ui.audio_group_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()

        # Filtrar pistas de audio inválidas (como 'qad')
        valid_audio_tracks = []
        for track in audio_tracks:
            code = track.get('code', '')
            # Filtrar códigos de idioma desconocidos o inválidos
            # Solo permitir códigos de idioma estándar (2 o 3 caracteres)
            if code not in ['qad', 'qor', 'qae', 'qaf', 'qag', 'qaa', 'qab', 'qac'] and len(code) in [2, 3]:
                # Verificar si es un idioma real
                try:
                    # Intentar obtener el nombre del idioma para verificar que es válido
                    from langcodes import Language
                    display_name = Language.get(code).display_name()
                    if display_name:
                        valid_audio_tracks.append(track)
                        print(f"[UI] Added valid audio track: {code} ({display_name})")
                except Exception as e:
                    print(f"[UI] Skipping invalid audio track: {code}, error: {e}")
                    continue

        # Si no hay pistas válidas, mostrar un mensaje
        if not valid_audio_tracks:
            label = QLabel("No hay pistas de audio disponibles")
            label.setStyleSheet("color: #f8f8f2;")
            self.shahid_ui.audio_group_layout.addWidget(label)
            return

        # Crear nuevos checkboxes para cada pista de audio válida
        for track in valid_audio_tracks:
            code = track.get('code', '')
            name = track.get('name', '')

            # Capitalizar el nombre del idioma
            if name.lower() == 'arabic':
                display = f"Arabic (ar)"
            elif name.lower() == 'english':
                display = f"English (en)"
            elif name.lower() == 'turkish':
                display = f"Turkish (tr)"
            else:
                display = f"{name.capitalize()} ({code})"

            # Crear checkbox con estilo
            checkbox = QCheckBox(display)
            checkbox.setStyleSheet("""
                QCheckBox {
                    color: #f8f8f2;
                    spacing: 5px;
                }
                QCheckBox::indicator {
                    width: 18px;
                    height: 18px;
                }
                QCheckBox::indicator:unchecked {
                    border: 1px solid #bd93f9;
                    background-color: #282a36;
                    border-radius: 9px;
                }
                QCheckBox::indicator:checked {
                    border: 1px solid #bd93f9;
                    background-color: #bd93f9;
                    border-radius: 9px;
                }
            """)
            checkbox.setChecked(True)  # Marcar por defecto

            # Guardar referencia a los checkboxes estándar para compatibilidad
            if code == 'ar':
                self.shahid_ui.audio_ar_checkbox = checkbox
            elif code == 'en':
                self.shahid_ui.audio_en_checkbox = checkbox
            elif code == 'tr':
                self.shahid_ui.audio_tr_checkbox = checkbox

            self.shahid_ui.audio_group_layout.addWidget(checkbox)

        print(f"[UI] Updated audio tracks for codec {codec}: {[track.get('code', '') for track in valid_audio_tracks]}")

    # ORIGINAL METHODS
    # ///////////////////////////////////////////////////////////////
    def refresh_movies_list(self):
        """Refresh the movies list."""
        movies_list = self.shahid_api.get_movies_list()
        self.shahid_ui.populate_movies_table(movies_list)

    def add_movie(self):
        """Add a new movie to the search."""
        movie_id, ok = QInputDialog.getText(self, "Add Movie", "Enter the movie ID:")
        if ok and movie_id:
            # Search for the movie
            self.search_content(movie_id)

    def refresh_series_list(self):
        """Refresh the series list."""
        # This is a placeholder for now
        pass

    def add_series(self):
        """Add a new series to the search."""
        series_id, ok = QInputDialog.getText(self, "Add Series", "Enter the series ID:")
        if ok and series_id:
            # Search for the series
            self.search_content(series_id)

    # RESIZE EVENTS
    # ///////////////////////////////////////////////////////////////
    def resizeEvent(self, _):
        # Update Size Grips
        UIFunctions.resize_grips(self)

    # MOUSE CLICK EVENTS
    # ///////////////////////////////////////////////////////////////
    def mousePressEvent(self, event):
        # SET DRAG POS WINDOW
        self.dragPos = event.globalPosition().toPoint()

        # PRINT MOUSE EVENTS
        if event.buttons() == Qt.LeftButton:
            print('Mouse click: LEFT CLICK')
        if event.buttons() == Qt.RightButton:
            print('Mouse click: RIGHT CLICK')



    # TAB CHANGE EVENT
    # ///////////////////////////////////////////////////////////////
    def on_tab_changed(self, index):
        """Handle tab change events."""
        print(f"Tab changed to index: {index}")

        # If changing to Seasons & Episodes tab
        if index == 1 and hasattr(self, 'current_content'):
            # Show loading indicator
            progress_dialog = QProgressDialog("Loading seasons and episodes...", "Cancel", 0, 0, self)
            progress_dialog.setWindowTitle("Loading")
            progress_dialog.setWindowModality(Qt.WindowModal)
            progress_dialog.setMinimumDuration(0)
            progress_dialog.setValue(0)
            progress_dialog.show()
            QApplication.processEvents()

            try:
                print(f"[UI] Loading content type: {self.current_content['type']}")

                # If content is a movie, handle differently
                if self.current_content['type'] == "MOVIE":
                    print("[UI] Handling content as MOVIE")
                    # For movies, we'll create a single "season" with a single "episode"
                    self.shahid_ui.seasons_list.clear()
                    movie_id = self.current_content['details']['productModel']['id']
                    movie_title = self.current_content['details']['productModel']['title']
                    print(f"[UI] Movie ID: {movie_id}, Title: {movie_title}")

                    # Add a single item to seasons list - without any prefix
                    item = QListWidgetItem(movie_title)
                    # For movies, set season number to 0
                    item.setData(Qt.UserRole, {'id': movie_id, 'number': 0, 'name': movie_title, 'count': 1})
                    self.shahid_ui.seasons_list.addItem(item)
                    print("[UI] Added movie to seasons list with season number 0")

                    # Select the item
                    self.shahid_ui.seasons_list.setCurrentItem(item)

                    # Add a single episode
                    self.shahid_ui.episodes_list.clear()
                    episode_item = QListWidgetItem(movie_title)
                    # For movies, set episode number to 0
                    episode_item.setData(Qt.UserRole, {'id': movie_id, 'number': 0, 'name': movie_title})
                    self.shahid_ui.episodes_list.addItem(episode_item)
                    episode_item.setSelected(True)
                    print("[UI] Added movie to episodes list with episode number 0")

                    # Enable continue button
                    self.shahid_ui.seasons_continue_button.setEnabled(True)
                else:
                    # For series, load seasons
                    print("[UI] Handling content as SERIES")
                    series_id = self.current_content['details']['productModel']['id']
                    print(f"[UI] Series ID: {series_id}")
                    self.load_seasons(series_id)
            finally:
                # Hide progress dialog
                progress_dialog.close()

    # IDM DOWNLOAD METHODS
    # ///////////////////////////////////////////////////////////////
    def download_with_idm(self):
        """Download selected content using IDM."""
        try:
            # Check if we have current content
            if not hasattr(self, 'current_content') or not self.current_content:
                QMessageBox.warning(self, "No Content", "Please select content first.")
                return

            # Check if token exists
            if not self.token:
                QMessageBox.warning(self, "Token Required", "Please enter your Shahid token first.")
                return

            # Check if IDM is available
            if not self.shahid_downloader.is_idm_available():
                QMessageBox.warning(
                    self,
                    "IDM Not Available",
                    "Internet Download Manager (IDM) is not installed or not found.\n\n"
                    "Please install IDM from: https://www.internetdownloadmanager.com/\n"
                    "Or check if IDM is installed in a custom location."
                )
                return

            # Get selected options
            resolution = self.shahid_ui.get_selected_resolution()
            codec = self.shahid_ui.get_selected_codec()
            audio_tracks = self.shahid_ui.get_selected_audio_tracks()
            subtitle_tracks = self.shahid_ui.get_selected_subtitle_tracks()

            if not resolution:
                QMessageBox.warning(self, "No Resolution", "Please select a video resolution.")
                return

            if not codec:
                QMessageBox.warning(self, "No Codec", "Please select a video codec.")
                return

            # Get content details
            content_details = self.current_content['details']
            content_type = self.current_content['type']
            content_title = content_details['productModel']['title']

            print(f"[IDM] Starting IDM download for: {content_title}")
            print(f"[IDM] Resolution: {resolution}, Codec: {codec}")
            print(f"[IDM] Audio tracks: {audio_tracks}")
            print(f"[IDM] Subtitle tracks: {subtitle_tracks}")

            # Handle different content types
            if content_type == "MOVIE":
                self.download_movie_with_idm(content_details, resolution, codec, audio_tracks, subtitle_tracks)
            else:
                self.download_series_with_idm(content_details, resolution, codec, audio_tracks, subtitle_tracks)

        except Exception as e:
            print(f"[IDM] Error in download_with_idm: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "Error", f"An error occurred while starting IDM download:\n{str(e)}")

    def download_movie_with_idm(self, content_details, resolution, codec, audio_tracks, subtitle_tracks):
        """Download a movie using IDM."""
        try:
            movie_id = content_details['productModel']['id']
            movie_title = content_details['productModel']['title']

            print(f"[IDM] Getting playout data for movie: {movie_title} (ID: {movie_id})")

            # Get playout data
            playout_data = self.shahid_api.get_episode_playout_url(movie_id)
            if not playout_data:
                QMessageBox.warning(self, "Error", "Could not get movie playout data.")
                return

            # Extract MPD URL based on selected codec
            mpd_url = None
            codec_key = 'H264' if codec == 'H.264' else 'H265'

            # Try to get MPD URL from mpd_urls first
            if 'mpd_urls' in playout_data and codec_key in playout_data['mpd_urls']:
                mpd_url = playout_data['mpd_urls'][codec_key]
                print(f"[IDM] Using MPD URL for {codec_key}: {mpd_url}")
            elif 'mpd_urls' in playout_data:
                # Try fallback codec
                fallback_codec_key = 'H265' if codec_key == 'H264' else 'H264'
                if fallback_codec_key in playout_data['mpd_urls']:
                    mpd_url = playout_data['mpd_urls'][fallback_codec_key]
                    print(f"[IDM] Using fallback MPD URL for {fallback_codec_key}: {mpd_url}")
                else:
                    # Use any available MPD URL
                    available_urls = playout_data['mpd_urls']
                    if available_urls:
                        mpd_url = list(available_urls.values())[0]
                        print(f"[IDM] Using first available MPD URL: {mpd_url}")
            else:
                # Fallback to main URL
                mpd_url = playout_data.get('url')
                print(f"[IDM] Using main playout URL: {mpd_url}")

            if not mpd_url:
                QMessageBox.warning(self, "Error", "Could not extract MPD URL from playout data.")
                return

            print(f"[IDM] MPD URL: {mpd_url}")

            # Check if this is an MPD file and inform user about the process
            if mpd_url.endswith('.mpd') or 'manifest' in mpd_url.lower():
                info_msg = (
                    "🔄 IDM Download Process\n\n"
                    "The content will be downloaded using N_m3u8DL-RE (same as the built-in downloader) "
                    "but organized in IDM-style folders and naming.\n\n"
                    "This provides:\n"
                    "• Same high-quality downloads as the built-in downloader\n"
                    "• IDM-style file organization\n"
                    "• Progress tracking in the Downloads tab\n\n"
                    "Continue with IDM-style download?"
                )

                reply = QMessageBox.question(
                    self,
                    "IDM Download Process",
                    info_msg,
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.Yes
                )

                if reply != QMessageBox.Yes:
                    return

            # Get DRM info if available
            drm_info = None
            try:
                # Try to extract DRM info from the movie
                drm_info = self.shahid_drm.process_episode_drm(movie_id, mpd_url)
                if drm_info:
                    print(f"[IDM] Successfully extracted DRM info for movie: {movie_title}")
                    print(f"[IDM] DRM KID: {drm_info.get('kid', 'N/A')}")
                    print(f"[IDM] DRM KEY: {drm_info.get('formatted_key', 'N/A')}")
                else:
                    print(f"[IDM] No DRM info found for movie: {movie_title}")
            except Exception as e:
                print(f"[IDM] Error extracting DRM info for movie: {e}")
                drm_info = None

            # Add movie to downloads table for tracking
            row_position = self.shahid_ui.downloads_table.rowCount()
            self.shahid_ui.downloads_table.insertRow(row_position)

            # Set movie details in table
            title_item = QTableWidgetItem(movie_title)
            title_item.setData(Qt.UserRole, {
                'content_id': movie_id,
                'content_title': movie_title,
                'content_type': 'MOVIE',
                'season_number': 0,
                'episode_number': 0,
                'mpd_url': mpd_url,
                'resolution': resolution,
                'codec': codec,
                'audio_tracks': audio_tracks,
                'subtitle_tracks': subtitle_tracks,
                'drm_info': drm_info
            })
            self.shahid_ui.downloads_table.setItem(row_position, 0, title_item)

            # Set other columns
            self.shahid_ui.downloads_table.setItem(row_position, 1, QTableWidgetItem("Movie"))
            self.shahid_ui.downloads_table.setItem(row_position, 2, QTableWidgetItem("1"))
            self.shahid_ui.downloads_table.setItem(row_position, 3, QTableWidgetItem(resolution))
            self.shahid_ui.downloads_table.setItem(row_position, 4, QTableWidgetItem(codec))

            # Create progress bar
            progress_bar = QProgressBar()
            progress_bar.setRange(0, 100)
            progress_bar.setValue(0)
            self.shahid_ui.downloads_table.setCellWidget(row_position, 5, progress_bar)

            # Set initial status
            status_item = QTableWidgetItem("Starting IDM")
            status_item.setTextAlignment(Qt.AlignCenter)
            self.shahid_ui.downloads_table.setItem(row_position, 6, status_item)

            # Add action buttons
            buttons_widget = QWidget()
            buttons_layout = QHBoxLayout(buttons_widget)
            buttons_layout.setContentsMargins(0, 0, 0, 0)
            buttons_layout.setAlignment(Qt.AlignCenter)

            cancel_button = QPushButton("Cancel")
            cancel_button.setStyleSheet("background-color: #ff5555; color: white; padding: 5px;")
            cancel_button.clicked.connect(lambda: self.cancel_download(row_position))
            buttons_layout.addWidget(cancel_button)

            self.shahid_ui.downloads_table.setCellWidget(row_position, 7, buttons_widget)

            # Start IDM download
            success = self.shahid_downloader.download_content_with_idm(
                row_position, mpd_url, resolution, audio_tracks, subtitle_tracks,
                movie_title, content_type="MOVIE", drm_info=drm_info
            )

            if success:
                QMessageBox.information(
                    self,
                    "IDM Download Started",
                    f"Movie '{movie_title}' has been sent to IDM.\n\n"
                    "Please check IDM to monitor the download progress."
                )
            else:
                QMessageBox.warning(
                    self,
                    "IDM Download Failed",
                    f"Failed to start IDM download for '{movie_title}'.\n\n"
                    "Please check if IDM is running and try again."
                )

        except Exception as e:
            print(f"[IDM] Error in download_movie_with_idm: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "Error", f"An error occurred while downloading movie:\n{str(e)}")

    def download_series_with_idm(self, content_details, resolution, codec, audio_tracks, subtitle_tracks):
        """Download selected episodes using IDM."""
        try:
            series_title = content_details['productModel']['title']

            # Get selected episodes
            selected_episodes = []
            for i in range(self.shahid_ui.episodes_list.count()):
                item = self.shahid_ui.episodes_list.item(i)
                if item.isSelected():
                    episode_data = item.data(Qt.UserRole)
                    selected_episodes.append(episode_data)

            if not selected_episodes:
                QMessageBox.warning(self, "No Episodes", "Please select episodes to download.")
                return

            # Get current season info
            current_season_item = self.shahid_ui.seasons_list.currentItem()
            if not current_season_item:
                QMessageBox.warning(self, "No Season", "Please select a season.")
                return

            season_data = current_season_item.data(Qt.UserRole)
            season_number = season_data['number']

            print(f"[IDM] Starting IDM download for {len(selected_episodes)} episodes from {series_title} Season {season_number}")

            # Ask for confirmation
            confirm_msg = (
                f"Download {len(selected_episodes)} episode(s) from '{series_title}' Season {season_number} using IDM-style process?\n\n"
                "🔄 Process:\n"
                "• Episodes will be downloaded using N_m3u8DL-RE (same quality as built-in downloader)\n"
                "• Files will be organized in IDM-style folders and naming\n"
                "• Progress will be tracked in the Downloads tab\n\n"
                "This provides the same high-quality downloads with IDM-style organization."
            )

            confirm = QMessageBox.question(
                self,
                "Confirm IDM Download",
                confirm_msg,
                QMessageBox.Yes | QMessageBox.No
            )

            if confirm != QMessageBox.Yes:
                return

            # Process each episode
            successful_downloads = 0
            for episode_data in selected_episodes:
                try:
                    episode_id = episode_data['id']
                    episode_number = episode_data['number']
                    episode_name = episode_data['name']

                    print(f"[IDM] Processing episode {episode_number}: {episode_name}")

                    # Get playout data for this episode
                    playout_data = self.shahid_api.get_episode_playout_url(episode_id)
                    if not playout_data:
                        print(f"[IDM] Could not get playout data for episode {episode_number}")
                        continue

                    # Extract MPD URL based on selected codec
                    mpd_url = None
                    codec_key = 'H264' if codec == 'H.264' else 'H265'

                    # Try to get MPD URL from mpd_urls first
                    if 'mpd_urls' in playout_data and codec_key in playout_data['mpd_urls']:
                        mpd_url = playout_data['mpd_urls'][codec_key]
                        print(f"[IDM] Using MPD URL for {codec_key}: {mpd_url}")
                    elif 'mpd_urls' in playout_data:
                        # Try fallback codec
                        fallback_codec_key = 'H265' if codec_key == 'H264' else 'H264'
                        if fallback_codec_key in playout_data['mpd_urls']:
                            mpd_url = playout_data['mpd_urls'][fallback_codec_key]
                            print(f"[IDM] Using fallback MPD URL for {fallback_codec_key}: {mpd_url}")
                        else:
                            # Use any available MPD URL
                            available_urls = playout_data['mpd_urls']
                            if available_urls:
                                mpd_url = list(available_urls.values())[0]
                                print(f"[IDM] Using first available MPD URL: {mpd_url}")
                    else:
                        # Fallback to main URL
                        mpd_url = playout_data.get('url')
                        print(f"[IDM] Using main playout URL: {mpd_url}")

                    if not mpd_url:
                        print(f"[IDM] Could not extract MPD URL for episode {episode_number}")
                        continue

                    # Get DRM info if available
                    drm_info = None
                    try:
                        # Try to extract DRM info from the episode
                        drm_info = self.shahid_drm.process_episode_drm(episode_id, mpd_url)
                        if drm_info:
                            print(f"[IDM] Successfully extracted DRM info for episode {episode_number}")
                            print(f"[IDM] DRM KID: {drm_info.get('kid', 'N/A')}")
                            print(f"[IDM] DRM KEY: {drm_info.get('formatted_key', 'N/A')}")
                        else:
                            print(f"[IDM] No DRM info found for episode {episode_number}")
                    except Exception as e:
                        print(f"[IDM] Error extracting DRM info for episode {episode_number}: {e}")
                        drm_info = None

                    # Add episode to downloads table
                    row_position = self.shahid_ui.downloads_table.rowCount()
                    self.shahid_ui.downloads_table.insertRow(row_position)

                    # Set episode details in table
                    title_item = QTableWidgetItem(series_title)
                    title_item.setData(Qt.UserRole, {
                        'content_id': episode_id,
                        'content_title': series_title,
                        'content_type': 'SERIES',
                        'season_number': season_number,
                        'episode_number': episode_number,
                        'episode_name': episode_name,
                        'mpd_url': mpd_url,
                        'resolution': resolution,
                        'codec': codec,
                        'audio_tracks': audio_tracks,
                        'subtitle_tracks': subtitle_tracks,
                        'drm_info': drm_info
                    })
                    self.shahid_ui.downloads_table.setItem(row_position, 0, title_item)

                    # Set other columns
                    self.shahid_ui.downloads_table.setItem(row_position, 1, QTableWidgetItem(str(season_number)))
                    self.shahid_ui.downloads_table.setItem(row_position, 2, QTableWidgetItem(str(episode_number)))
                    self.shahid_ui.downloads_table.setItem(row_position, 3, QTableWidgetItem(resolution))
                    self.shahid_ui.downloads_table.setItem(row_position, 4, QTableWidgetItem(codec))

                    # Create progress bar
                    progress_bar = QProgressBar()
                    progress_bar.setRange(0, 100)
                    progress_bar.setValue(0)
                    self.shahid_ui.downloads_table.setCellWidget(row_position, 5, progress_bar)

                    # Set initial status
                    status_item = QTableWidgetItem("Starting IDM")
                    status_item.setTextAlignment(Qt.AlignCenter)
                    self.shahid_ui.downloads_table.setItem(row_position, 6, status_item)

                    # Add action buttons
                    buttons_widget = QWidget()
                    buttons_layout = QHBoxLayout(buttons_widget)
                    buttons_layout.setContentsMargins(0, 0, 0, 0)
                    buttons_layout.setAlignment(Qt.AlignCenter)

                    cancel_button = QPushButton("Cancel")
                    cancel_button.setStyleSheet("background-color: #ff5555; color: white; padding: 5px;")
                    cancel_button.clicked.connect(lambda: self.cancel_download(row_position))
                    buttons_layout.addWidget(cancel_button)

                    self.shahid_ui.downloads_table.setCellWidget(row_position, 7, buttons_widget)

                    # Start IDM download
                    success = self.shahid_downloader.download_content_with_idm(
                        row_position, mpd_url, resolution, audio_tracks, subtitle_tracks,
                        series_title, season_number, episode_number, content_type="SERIES", drm_info=drm_info
                    )

                    if success:
                        successful_downloads += 1
                        print(f"[IDM] Successfully started IDM download for episode {episode_number}")
                    else:
                        print(f"[IDM] Failed to start IDM download for episode {episode_number}")

                except Exception as e:
                    print(f"[IDM] Error processing episode {episode_data.get('number', 'unknown')}: {e}")
                    continue

            # Show summary
            if successful_downloads > 0:
                QMessageBox.information(
                    self,
                    "IDM Downloads Started",
                    f"Successfully sent {successful_downloads} out of {len(selected_episodes)} episodes to IDM.\n\n"
                    "Please check IDM to monitor the download progress."
                )
            else:
                QMessageBox.warning(
                    self,
                    "IDM Download Failed",
                    "Failed to start any IDM downloads.\n\n"
                    "Please check if IDM is running and try again."
                )

        except Exception as e:
            print(f"[IDM] Error in download_series_with_idm: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "Error", f"An error occurred while downloading series:\n{str(e)}")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setWindowIcon(QIcon("icon.ico"))
    window = MainWindow()
    sys.exit(app.exec())
