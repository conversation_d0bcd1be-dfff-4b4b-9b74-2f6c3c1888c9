"""
Shahid DRM Module
This module handles DRM-related functionality for Shahid content.
"""

import base64
import hashlib
import hmac
import os
import re
import requests
import sys
from datetime import datetime
from binaries.pywidevine.decrypt.wvdecrypt import WvDecrypt

class ShahidDRM:
    def __init__(self, token, api):
        self.token = token
        self.api = api

        # Get the script directory (parent of the modules directory)
        self.script_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

        # Use paths relative to the script directory
        self.keys_path = os.path.join(self.script_dir, 'KEYS', 'KEYS.txt')

        # Create KEYS directory if it doesn't exist
        os.makedirs(os.path.dirname(self.keys_path), exist_ok=True)
        print(f"DRM keys will be stored at: {self.keys_path}")

    def extract_pssh_from_mpd(self, mpd_url):
        """Extract PSSH from MPD content."""
        headers = {
            'authority': 'api2.shahid.net',
            'accept': 'application/json',
            'accept-language': 'en',
            'content-type': 'application/json',
            'language': 'en',
            'origin': 'https://shahid.mbc.net',
            'referer': 'https://shahid.mbc.net/',
            'token': self.token,
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'uuid': 'web',
        }

        response = requests.get(mpd_url, headers=headers)

        if response.status_code == 200:
            data = response.content.decode('utf-8')

            # Extract PSSH
            match_obj = re.findall(r'pssh.+<', data, re.M | re.I)
            if match_obj and '>' in match_obj[0] and '<' in match_obj[0].split('>')[1]:
                pssh = match_obj[0].split('>')[1].split('<')[0]

                # Extract KID
                kid_match = re.search(r'cenc:default_KID="([0-9a-fA-F-]+)"', data)
                if kid_match:
                    kid = kid_match.group(1).replace('-', '').lower()
                    return pssh, kid

        return None, None


    def fetch_drm_license(self, asset_id, pssh, kid):
        """Fetch DRM license for content and decrypt it using WvDecrypt."""
        # Generate timestamp
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
        current_timestamp = int(datetime.timestamp(datetime.strptime(current_time, '%Y-%m-%d %H:%M:%S.%f')) * 1000)

        # Generate DRM parameters
        drm_params = {'request': '{"assetId":' + str(asset_id) + '}', 'ts': current_timestamp, 'country': 'SA'}
        auth_header = self.api._generate_authorization_header(drm_params)

        # Set DRM headers
        drm_headers = {
            'language': 'EN',
            'os_version': '10',
            'accept-language': 'en',
            'browser_version': '93.0',
            'authorization': auth_header,
            'uuid': 'web',
            'shahid_os': 'WINDOWS',
            'sec-ch-ua-platform': '"Windows"',
            'browser_name': 'CHROME',
            'sec-ch-ua-mobile': '?0',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'content-type': 'application/json',
            'accept': 'application/json',
            'token': self.token,
            'origin': 'https://shahid.mbc.net',
            'referer': 'https://shahid.mbc.net/'
        }

        # Fetch DRM data
        drm_response = requests.get('https://api3.shahid.net/proxy/v2.1/playout/new/drm', headers=drm_headers, params=drm_params)

        if drm_response.status_code == 200:
            drm_data = drm_response.json()

            if 'signature' in drm_data:
                license_url = drm_data['signature']

                try:
                    # Get certificate
                    cert = requests.post(license_url, b'\x08\x04').content

                    # Use WvDecrypt to decrypt the content
                    wvdecrypt = WvDecrypt(pssh)
                    wvdecrypt.set_certificate(base64.b64encode(cert))
                    challenge = wvdecrypt.get_challenge()
                    license_response = requests.post(license_url, challenge).content
                    wvdecrypt.update_license(base64.b64encode(license_response))
                    keys = wvdecrypt.start_process()

                    # Extract content key
                    content_key = None
                    for key in keys:
                        if key.type == "CONTENT":
                            content_key = key.key.hex()
                            # Save the key to file
                            self.save_key(kid, content_key)
                            break

                    return {
                        'license_url': license_url,
                        'pssh': pssh,
                        'kid': kid,
                        'key': content_key,
                        'cert': base64.b64encode(cert).decode('utf-8'),
                        'keys': keys
                    }
                except Exception as e:
                    print(f"Error fetching DRM license: {e}")
                    import traceback
                    traceback.print_exc()

        return None

    def save_key(self, kid, key):
        """Save decryption key to file."""
        with open(self.keys_path, 'w') as f:
            f.write(f"{kid}:{key}")
        return True

    def process_episode_drm(self, episode_id, mpd_url):
        """Process DRM for an episode and return all necessary information."""
        print(f"[DRM] Processing DRM for episode ID: {episode_id}")
        print(f"[DRM] MPD URL: {mpd_url}")

        # Extract PSSH and KID from MPD
        pssh, kid = self.extract_pssh_from_mpd(mpd_url)

        if not pssh or not kid:
            print("[DRM] Failed to extract PSSH or KID from MPD")
            return None

        print(f"[DRM] Extracted PSSH: {pssh}")
        print(f"[DRM] Extracted KID: {kid}")

        # Fetch DRM license and decrypt
        drm_info = self.fetch_drm_license(episode_id, pssh, kid)

        if not drm_info or 'key' not in drm_info:
            print("[DRM] Failed to fetch DRM license or decrypt content")
            return None

        print(f"[DRM] Decryption Key: {kid}:{drm_info['key']}")

        return {
            'pssh': pssh,
            'kid': kid,
            'key': drm_info['key'],
            'formatted_key': f"{kid}:{drm_info['key']}"
        }
