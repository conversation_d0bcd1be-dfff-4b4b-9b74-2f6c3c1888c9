﻿LOG 2025/05/29
Save Path: F:\TEEFA\MBC\shahid_template\binaries\Logs
Task Start: 2025/05/29 03:29:03
Task CommandLine: F:\TEEFA\MBC\shahid_template\binaries\N_m3u8DL-RE.exe https://mbc-aws-ksa.mncdn.com/out/v1/227ce16149174c1ea6e32c4695c33612/22166333d51a4d528478144cc30fc048/65aee16b2775429e8add39c54f7b6995/index.mpd?aws.manifestfilter=video_height:144-1080;video_codec:H264&video_height=144-1080&video_codec=H264 -sv id=4:for=best --select-audio lang=ar:for=all --sub-only=false --tmp-dir F:\TEEFA\MBC\shahid_template\cache\episode_9 --save-dir F:\TEEFA\MBC\shahid_template\cache\episode_9 --save-name "<PERSON><PERSON> ++.S03.E11.468p.SHAHID.VIP.WEB-DL.H264.AAC" --base-url https://mbc-aws-ksa.mncdn.com/out/v1/227ce16149174c1ea6e32c4695c33612/22166333d51a4d528478144cc30fc048/65aee16b2775429e8add39c54f7b6995/ --decryption-binary-path=F:\TEEFA\MBC\shahid_template\binaries\mp4decrypt.exe --key-text-file=F:\TEEFA\MBC\shahid_template\KEYS\KEYS.txt --log-level INFO --binary-merge --thread-count 16 --download-retry-count 3 --sub-format SRT -mt

03:29:03.418 INFO : N_m3u8DL-RE (Beta version) 20230628
03:29:03.428 EXTRA: ffmpeg => F:\TEEFA\MBC\shahid_template\binaries\ffmpeg.exe
03:29:03.428 INFO : Loading URL: https://mbc-aws-ksa.mncdn.com/out/v1/227ce16149174c1ea6e32c4695c33612/22166333d51a4d528478144cc30fc048/65aee16b2775429e8add39c54f7b6995/index.mpd?aws.manifestfilter=video_height:144-1080;video_codec:H264&video_height=144-1080&video_codec=H264
03:29:03.641 INFO : Content Matched: Dynamic Adaptive Streaming over HTTP
03:29:03.641 INFO : Parsing streams...
03:29:03.749 WARN : Writing meta json
03:29:03.770 INFO : Extracted, there are 40 streams, with 10 basic streams, 6 audio streams, 24 subtitle streams
03:29:03.771 INFO : Vid *CENC 1920x1080 | 2680 Kbps | 2 | 25 | avc1.640028 | 506 Segments | ~50m35s
03:29:03.771 INFO : Vid *CENC 1280x720 | 1540 Kbps | 3 | 25 | avc1.4D401F | 506 Segments | ~50m35s
03:29:03.772 INFO : Vid *CENC 1024x576 | 1186 Kbps | 1 | 25 | avc1.4D401F | 506 Segments | ~50m35s
03:29:03.772 INFO : Vid *CENC 832x468 | 727 Kbps | 4 | 25 | avc1.4D401F | 506 Segments | ~50m35s
03:29:03.773 INFO : Vid *CENC 640x360 | 541 Kbps | 5 | 25 | avc1.4D401E | 506 Segments | ~50m35s
03:29:03.774 INFO : Vid *CENC 512x288 | 424 Kbps | 6 | 25 | avc1.4D401E | 506 Segments | ~50m35s
03:29:03.774 INFO : Vid *CENC 448x252 | 372 Kbps | 7 | 25 | avc1.4D401E | 506 Segments | ~50m35s
03:29:03.775 INFO : Vid *CENC 448x252 | 330 Kbps | 8 | 25 | avc1.4D401E | 506 Segments | ~50m35s
03:29:03.775 INFO : Vid *CENC 256x144 | 219 Kbps | 9 | 25 | avc1.4D401E | 506 Segments | ~50m35s
03:29:03.776 INFO : Vid *CENC 256x144 | 149 Kbps | 10 | 25 | avc1.4D401E | 506 Segments | ~50m35s
03:29:03.777 INFO : New version detected! v0.3.0-beta
03:29:03.778 INFO : Aud *CENC 11 | 128 Kbps | mp4a.40.2 | ar | 2CH | 506 Segments | ~50m35s
03:29:03.786 INFO : Aud *CENC 12 | 127 Kbps | mp4a.40.2 | qaa | 2CH | 506 Segments | ~50m35s
03:29:03.786 INFO : Aud *CENC 13 | 127 Kbps | mp4a.40.2 | qab | 2CH | 506 Segments | ~50m35s
03:29:03.787 INFO : Aud *CENC 14 | 127 Kbps | mp4a.40.2 | qac | 2CH | 506 Segments | ~50m35s
03:29:03.787 INFO : Aud *CENC 15 | 127 Kbps | mp4a.40.2 | qad | 2CH | 506 Segments | ~50m35s
03:29:03.788 INFO : Aud *CENC 16 | 127 Kbps | mp4a.40.2 | qae | 2CH | 506 Segments | ~50m35s
03:29:03.789 INFO : Sub 17 | en | stpp | 506 Segments | ~50m35s
03:29:03.790 INFO : Sub 18 | ar | stpp | 506 Segments | ~50m35s
03:29:03.792 INFO : Sub 19 | ar-fn | stpp | 506 Segments | ~50m35s
03:29:03.793 INFO : Sub 20 | en-fn | stpp | 506 Segments | ~50m35s
03:29:03.794 INFO : Sub 21 | fr-fn | stpp | 506 Segments | ~50m35s
03:29:03.794 INFO : Sub 22 | it | stpp | 506 Segments | ~50m35s
03:29:03.795 INFO : Sub 23 | tl | stpp | 506 Segments | ~50m35s
03:29:03.795 INFO : Sub 24 | id | stpp | 506 Segments | ~50m35s
03:29:03.795 INFO : Sub 25 | ko | stpp | 506 Segments | ~50m35s
03:29:03.796 INFO : Sub 26 | ja | stpp | 506 Segments | ~50m35s
03:29:03.796 INFO : Sub 27 | nl | stpp | 506 Segments | ~50m35s
03:29:03.797 INFO : Sub 28 | el | stpp | 506 Segments | ~50m35s
03:29:03.797 INFO : Sub 29 | pt | stpp | 506 Segments | ~50m35s
03:29:03.798 INFO : Sub 30 | pl | stpp | 506 Segments | ~50m35s
03:29:03.799 INFO : Sub 31 | no | stpp | 506 Segments | ~50m35s
03:29:03.799 INFO : Sub 32 | bn | stpp | 506 Segments | ~50m35s
03:29:03.800 INFO : Sub 33 | ru | stpp | 506 Segments | ~50m35s
03:29:03.800 INFO : Sub 34 | de | stpp | 506 Segments | ~50m35s
03:29:03.801 INFO : Sub 35 | ct | stpp | 506 Segments | ~50m35s
03:29:03.801 INFO : Sub 36 | tr | stpp | 506 Segments | ~50m35s
03:29:03.802 INFO : Sub 37 | ms | stpp | 506 Segments | ~50m35s
03:29:03.802 INFO : Sub 38 | ml | stpp | 506 Segments | ~50m35s
03:29:03.803 INFO : Sub 39 | hi | stpp | 506 Segments | ~50m35s
03:29:03.803 INFO : Sub 40 | es | stpp | 506 Segments | ~50m35s
03:29:03.804 EXTRA: VideoFilter => GroupIdReg: 4 For: best
03:29:03.804 EXTRA: AudioFilter => LanguageReg: ar For: all
03:29:03.804 INFO : Parsing streams...
03:29:03.805 INFO : Selected streams:
03:29:03.805 INFO : Vid *CENC 832x468 | 727 Kbps | 4 | 25 | avc1.4D401F | 506 Segments | ~50m35s
03:29:03.806 INFO : Aud *CENC 11 | 128 Kbps | mp4a.40.2 | ar | 2CH | 506 Segments | ~50m35s
03:29:03.806 WARN : Writing meta json
03:29:03.808 INFO : Save Name: Kamel El Adad ++.S03.E11.468p.SHAHID.VIP.WEB-DL.H264.AAC
03:29:03.809 INFO : Start downloading...Vid 832x468 | 727 Kbps | 4 | 25 | avc1.4D401F
03:29:03.809 INFO : Start downloading...Aud 11 | 128 Kbps | mp4a.40.2 | ar | 2CH
03:29:03.884 WARN : Type: cenc
03:29:03.884 WARN : PSSH(WV): CAESEISmX655Okypu07bscE7xmAaBWluc3lzIiQ4NGE2NWZhZS03OTNhLTRjYTktYmI0ZS1kYmIxYzEzYmM2NjAqAlNEMgA=
03:29:03.886 WARN : KID: 84a65fae793a4ca9bb4edbb1c13bc660
03:29:03.886 INFO : Trying to search for KEY from text file...
03:29:03.887 INFO : OK 84a65fae793a4ca9bb4edbb1c13bc660:eadb68d0e8830dfb552247e715305fbd
03:29:03.888 WARN : Reading media info...
03:29:03.910 INFO : [0x1]: Video, h264 (avc1), 832x468
03:29:03.948 WARN : Type: cenc
03:29:03.949 WARN : PSSH(WV): CAESEISmX655Okypu07bscE7xmAaBWluc3lzIiQ4NGE2NWZhZS03OTNhLTRjYTktYmI0ZS1kYmIxYzEzYmM2NjAqAlNEMgA=
03:29:03.950 WARN : KID: 84a65fae793a4ca9bb4edbb1c13bc660
03:29:03.950 INFO : Trying to search for KEY from text file...
03:29:03.951 INFO : OK 84a65fae793a4ca9bb4edbb1c13bc660:eadb68d0e8830dfb552247e715305fbd
03:29:03.951 WARN : Reading media info...
03:29:03.975 INFO : [0x1]: Audio, aac (mp4a)
03:29:33.769 INFO : Binary merging...
03:29:33.912 INFO : Decrypting...
03:30:15.187 INFO : Binary merging...
03:30:15.444 INFO : Decrypting...
03:30:16.520 INFO : Done
