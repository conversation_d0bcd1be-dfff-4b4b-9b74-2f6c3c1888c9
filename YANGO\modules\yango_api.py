#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
YANGO API Module
Handles all API interactions with YANGO PLAY service
Based on the original YANGO PLAY script with complete GraphQL queries
"""

import requests
import json
from PySide6.QtCore import QObject, Signal, QThread

class YangoAPI(QObject):
    # Signals for communication
    movie_found = Signal(dict)
    series_found = Signal(dict)
    seasons_found = Signal(list)
    episodes_found = Signal(list)
    streams_found = Signal(dict)
    error_occurred = Signal(str)

    def __init__(self):
        super().__init__()
        self.base_url = "https://kp-graphql-api.movies.yango.com/graphql/"
        self.selected_quality = "HD"  # Default quality
        self.cookies = self.load_cookies()
        self.setup_headers()

        # Initialize settings for proxy support
        self.settings_manager = None
        self.load_settings()

    def load_cookies(self):
        """Load cookies from cookies.txt file like the original YANGO script"""
        import os
        import sys

        # Get the directory where the executable is located
        if getattr(sys, 'frozen', False):
            # Running as compiled executable
            app_dir = os.path.dirname(sys.executable)
        else:
            # Running as script
            app_dir = os.path.dirname(os.path.abspath(__file__))

        # Try multiple possible locations for cookies.txt (YANGO uses regular cookies.txt)
        possible_paths = [
            "cookies.txt",  # Current directory
            os.path.join(app_dir, "cookies.txt"),  # Executable directory
            os.path.join(os.path.dirname(__file__), "..", "cookies.txt"),  # Parent directory
            os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "cookies.txt"),  # Absolute parent
            os.path.join(os.getcwd(), "cookies.txt"),  # Working directory
        ]

        cookies_file = None
        for path in possible_paths:
            if os.path.exists(path):
                cookies_file = path
                print(f"✅ Found cookies file at: {path}")
                break

        if not cookies_file:
            print(f"❌ Cookies file not found in any of these locations:")
            for path in possible_paths:
                print(f"   - {os.path.abspath(path)}")
            print("Please export your YANGO cookies to cookies.txt")
            return None

        try:
            cookies = []
            with open(cookies_file, "r", encoding='utf-8') as file:
                for line in file:
                    # Skip comments or empty lines
                    if line.startswith("#") or not line.strip():
                        continue
                    parts = line.strip().split("\t")
                    if len(parts) >= 7:
                        # Cookie name and value
                        name = parts[5]
                        value = parts[6]
                        cookies.append(f"{name}={value}")

            cookie_string = "; ".join(cookies)
            if cookie_string:
                print(f"✅ Loaded {len(cookies)} cookies from {cookies_file}")
                return cookie_string
            else:
                print(f"⚠️ No cookies found in {cookies_file}")
                return None

        except Exception as e:
            print(f"❌ Error loading cookies from {cookies_file}: {str(e)}")
            return None

    def setup_headers(self):
        """Setup headers for API requests based on original YANGO script"""
        # Base headers for HD quality (from original script)
        self.headers_HD = {
            'accept': '*/*',
            'accept-language': 'ru,en;q=0.9',
            'cache-control': 'no-cache',
            'content-type': 'application/json',
            'origin': 'https://play.yango.com',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'referer': 'https://play.yango.com/',
            'sec-ch-ua': '"Brave";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-site',
            'sec-gpc': '1',
            'service-id': '100025',
            'traceparent': '00-8757c04476dcdaf12cfd5e87a2f749ba-d9459f44549c048c-00',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'x-device-supported-stream-formats': '[{"HLS":["fairplay","clear"]},{"DASH":["playready","widevine","clear"]}]',
            'x-preferred-language': 'en',
            'x-real-host': '*.yango.com',
            'x-request-id': '1731909965309447-11841338694081714137:7',
        }

        # Headers for 4K quality (from original script)
        self.headers_4K = {
            'accept': '*/*',
            'accept-language': 'ru,en;q=0.9',
            'cache-control': 'no-cache',
            'content-type': 'application/json',
            'origin': 'https://play.yango.com',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'referer': 'https://play.yango.com/',
            'sec-ch-ua': '"Brave";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-site',
            'x-device-os': 'IOS',
            'x-device-os-version': '18',
            'x-device-vendor': 'Apple',
            'x-device-model': 'iPhone16,2',
            'x-device-app-version': '1.33.1',
            'x-device-video-formats': 'SD,UHD,HD',
            'X-Device-Audio-Codecs': 'AAC,DD,EC3',
            'X-Device-Dynamic-Ranges': 'HDR10,DV,HLG',
            'sec-gpc': '1',
            'service-id': '100025',
            'traceparent': '00-8757c04476dcdaf12cfd5e87a2f749ba-d9459f44549c048c-00',
            'user-agent': 'iOS client iPhone (iOS 18.0) Saft/1.34.0 (5389) CFNetwork/1.0 Darwin/24.0.0',
            'x-device-supported-stream-formats': '[{"HLS":["fairplay","clear"]},{"DASH":["playready","widevine","clear"]}]',
            'x-preferred-language': 'en',
            'x-real-host': '*.yango.com',
            'x-request-id': '1731909965309447-11841338694081714137:7',
        }

        # Add cookies to both headers if available
        if self.cookies:
            self.headers_HD['Cookie'] = self.cookies
            self.headers_4K['Cookie'] = self.cookies

        # Default to HD headers
        self.current_headers = self.headers_HD

    def set_quality(self, quality="HD"):
        """Set quality preference (HD or 4K)"""
        self.selected_quality = quality
        if quality == "4K":
            self.current_headers = self.headers_4K
            print(f"✅ Quality set to: {quality} (Using 4K headers with iOS device simulation)")
        else:
            self.current_headers = self.headers_HD
            print(f"✅ Quality set to: {quality} (Using HD headers with standard browser)")

        # Also update the original YANGO script quality if available
        try:
            import sys
            import os
            # Add the parent directory to sys.path to import the original script
            parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            if parent_dir not in sys.path:
                sys.path.insert(0, parent_dir)

            # Try to import and update the original script's quality
            try:
                import importlib.util
                spec = importlib.util.spec_from_file_location("yango_play", os.path.join(parent_dir, "YANGO PLAY.py"))
                if spec and spec.loader:
                    yango_play = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(yango_play)
                    if hasattr(yango_play, 'set_quality'):
                        yango_play.set_quality(quality)
                        print(f"✅ Updated original YANGO script quality to: {quality}")
            except Exception as e:
                print(f"⚠️ Could not update original script quality: {str(e)}")
        except Exception as e:
            print(f"⚠️ Error updating original script: {str(e)}")

    def extract_content_id_from_url(self, url):
        """Extract content ID from YANGO URL"""
        try:
            if not url.startswith('http'):
                # Assume it's already an ID
                return url.strip()

            # Handle different YANGO URL formats
            # Example: https://play.yango.com/en-eg/movie/content-id
            # Example: https://play.yango.com/en-eg/series/content-id

            if '/movie/' in url:
                # Extract movie ID
                parts = url.split('/movie/')
                if len(parts) > 1:
                    content_id = parts[1].split('?')[0].split('/')[0]
                    print(f"🎬 Extracted movie ID: {content_id}")
                    return content_id
            elif '/series/' in url:
                # Extract series ID
                parts = url.split('/series/')
                if len(parts) > 1:
                    content_id = parts[1].split('?')[0].split('/')[0]
                    print(f"📺 Extracted series ID: {content_id}")
                    return content_id
            elif '?rt=' in url:
                # Handle tracking URLs - these are not valid content IDs
                print(f"⚠️ Invalid URL format: {url}")
                print("💡 Please use a direct movie or series URL from YANGO")
                print("   Example: https://play.yango.com/en-eg/movie/content-id")
                return None
            else:
                # Try to extract from the end of URL
                parts = url.rstrip('/').split('/')
                content_id = parts[-1]
                # Check if it looks like a valid content ID
                if '?' in content_id or len(content_id) < 3:
                    print(f"⚠️ Invalid content ID format: {content_id}")
                    return None
                print(f"🔍 Extracted ID from URL end: {content_id}")
                return content_id

        except Exception as e:
            print(f"❌ Error extracting content ID from URL: {str(e)}")
            return None

    def fetch_and_print_movie_card(self, content_id, silent=False):
        """
        Fetch movie details using MovieCard query from original YANGO script
        """
        try:
            if not silent:
                print(f"🔍 Fetching movie card for ID: {content_id}")

            url = f"{self.base_url}?operationName=MovieCard"

            payload = {
                "operationName": "MovieCard",
                "variables": {
                    "contentUuid": content_id,
                    "isAuthorized": True,
                    "withUserData": True,
                },
                "query": "query MovieCard($contentUuid: String!, $isAuthorized: Boolean!, $withUserData: Boolean!) { movieByContentUuid(contentUuid: $contentUuid) { id contentId ...AgeRestriction ...OverviewMeta ...ContentOverview ...ContentDetails ...ContentCard ...SerialStructure ...MainTrailer ...Promo ...OttNextEpisode @include(if: $isAuthorized) ...OttMovieTiming @include(if: $isAuthorized) ...MovieType __typename } } fragment OttPreviewFeatures on OttPreview { features(filter: {layout: OTT_TITLE_CARD, onlyClientSupported: true}) { alias displayedName: displayName group __typename } __typename } fragment AvailabilityViewOption on ViewOption { watchabilityStatus availabilityAnnounce { __typename } __typename } fragment Years on Movie { __typename ... on VideoInterface { productionYear(override: OTT_WHEN_EXISTS) __typename } ... on Series { releaseYears { start end __typename } __typename } } fragment AudioMeta on AudioMeta { forAdult language languageName quality qualityName studio type __typename } fragment SubtitleMeta on SubtitleMeta { forAdult language languageName studio type __typename } fragment Title on Title { localized original __typename } fragment MovieTitle on Movie { title { ...Title __typename } __typename } fragment ImageSize on ImageSize { width height __typename } fragment MovieHorizontalLogo on MovieLogos { horizontal { avatarsUrl origSize { ...ImageSize __typename } __typename } __typename } fragment Image on Image { avatarsUrl fallbackUrl __typename } fragment MovieVerticalPoster on MoviePosters { vertical(override: OTT_WHEN_EXISTS) { ...Image __typename } verticalWithRightholderLogo { ...Image __typename } __typename } fragment SkippableFragment on SkippableFragment { type startTime endTime final __typename } fragment EpisodeBase on Episode { id contentId number __typename } fragment SeasonBase on Season { id contentId number __typename } fragment SerialStructureEpisode on Episode { ...EpisodeBase synopsis(override: OTT_WHEN_EXISTS) title { ...Title __typename } season { ...SeasonBase __typename } cover { avatarsUrl fallbackUrl __typename } ott { duration editorAnnotation plannedAvailabilityDate viewOption { availabilityStatus availabilityEndDate watchabilityStatus __typename } timing @include(if: $withUserData) { current __typename } __typename } __typename } fragment OttEpisodeTiming on Episode { ott { timing { current __typename } __typename } __typename } fragment NextEpisode on Episode { ...EpisodeBase title { ...Title __typename } cover { avatarsUrl fallbackUrl __typename } ott { duration viewOption { availabilityEndDate availabilityStatus __typename } __typename } season { ...SeasonBase __typename } ...OttEpisodeTiming __typename } fragment AgeRestriction on Movie { restriction { age __typename } __typename } fragment OverviewMeta on Movie { contentId __typename ott { preview { ...OttPreviewFeatures ... on OttPreview_AbstractVideo { duration __typename } __typename } __typename } top10 genres { id name __typename } restriction { age __typename } countries { id name __typename } viewOption { ...AvailabilityViewOption __typename } ...Years ... on Series { seasonsAll: seasons(limit: 0) { total __typename } __typename } } fragment ContentOverview on Movie { shortDescription editorAnnotation userData @include(if: $isAuthorized) { watchStatuses { watched { value __typename } __typename } __typename } ott { ... on Ott_AbstractVideo { preview { ... on OttPreview_AbstractVideo { duration __typename } __typename } __typename } __typename } __typename } fragment ContentDetails on Movie { id contentId title { original __typename } ottSynopsis: synopsis(override: OTT_WHEN_EXISTS) actors: members(limit: 10, role: [ACTOR, CAMEO, UNCREDITED]) { items { person { id name originalName __typename } __typename } __typename } directors: members(role: DIRECTOR, limit: 5) { items { person { id name originalName __typename } __typename } __typename } ott { preview { ...OttPreviewFeatures availableMetadata { audioMeta { ...AudioMeta __typename } subtitleMeta { ...SubtitleMeta __typename } __typename } __typename } __typename } viewOption { ...AvailabilityViewOption __typename } __typename } fragment ContentCard on Movie { ...MovieTitle gallery { covers { horizontal { avatarsUrl __typename } __typename } logos { rightholderForCover(filter: {formFactor: M}) { image { avatarsUrl __typename } theme __typename } ...MovieHorizontalLogo rightholderForCoverRecommendedTheme __typename } posters { ...MovieVerticalPoster __typename } __typename } ott { ... on Ott_AbstractVideo { skippableFragments { ...SkippableFragment __typename } __typename } __typename } __typename } fragment SerialStructure on Series { id seasons(limit: 10000, isOnlyOnline: true) { items { id contentId number episodes(limit: 1, isOnlyOnline: true) { items { ...SerialStructureEpisode __typename } total __typename } episodeGroupings(filter: {onlyOnline: true}, capacity: 20) { from to offset __typename } __typename } __typename } __typename } fragment MainTrailer on Movie { contentId ott { mainTrailers: trailers(limit: 2) { items { contentGroupUuid main __typename } __typename } __typename } __typename } fragment Promo on Movie { contentId ott { promos: trailers(limit: 1, onlyPromo: true) { items { contentGroupUuid __typename } __typename } __typename } __typename } fragment OttNextEpisode on Movie { id contentId ott { ... on Ott_AbstractSeries { nextEpisode(fallbackToFirstEpisode: true) { contentId fallback episode { offsetInSeason(filter: {onlyOnline: true}) ...NextEpisode __typename } __typename } __typename } __typename } __typename } fragment OttMovieTiming on Movie { id ott { ... on Ott_AbstractVideo { preview { ... on OttPreview_AbstractVideo { timing { current __typename } __typename } __typename } __typename } __typename } __typename } fragment MovieType on Movie { __typename }"
            }

            if not silent:
                print(f"📤 Sending request to: {url}")
                print(f"📤 Payload: {payload}")
                print(f"📤 Headers: {self.current_headers}")

            # Get proxy configuration
            proxies = self.get_proxy_config()
            response = requests.post(url, json=payload, headers=self.current_headers, proxies=proxies)

            if not silent:
                print(f"📥 Response status code: {response.status_code}")
                print(f"📥 Response headers: {dict(response.headers)}")
                print(f"📥 Response text: {response.text}")

            response.raise_for_status()

            data = response.json()

            if not silent:
                print(f"✅ Movie card fetched successfully")
                print(f"📊 Response data: {data}")

            return data

        except requests.exceptions.RequestException as e:
            if not silent:
                print(f"❌ Network error while fetching movie card: {str(e)}")
                print(f"❌ Response status: {getattr(e.response, 'status_code', 'N/A')}")
                print(f"❌ Response text: {getattr(e.response, 'text', 'N/A')}")
            return None
        except Exception as e:
            if not silent:
                print(f"❌ Error fetching movie card: {str(e)}")
            return None

    def fetch_movie_details(self, content_id):
        """
        Fetch movie details using PlayerBaseInfo query from original YANGO script
        This is the main function for getting streaming details
        """
        try:
            print(f"🎬 Fetching movie details for ID: {content_id}")

            url = f"{self.base_url}?operationName=PlayerBaseInfo"

            payload = {
                "operationName": "PlayerBaseInfo",
                "variables": {
                    "prerollsSupported": True,
                    "isKidSubProfile": False,
                    "contentId": content_id,
                },
                "query": 'query PlayerBaseInfo($contentId: String!, $prerollsSupported: Boolean = false, $isKidSubProfile: Boolean = false) { content(contentId: $contentId) { ... on Catchup { ...CatchupPlayerBaseInfo __typename } ... on VideoInterface { ...FilmPlayerBaseInfo __typename } ... on Episode { ...EpisodePlayerBaseInfo __typename } ... on TvChannel { ...ChannelPlayerBaseInfo __typename } ... on Clip { ...ClipPlayerBaseInfo __typename } __typename } userProfile @include(if: $isKidSubProfile) { ...ChildLockPlayerKidSubProfile __typename } } fragment CatchupTvChannel on Catchup { tvChannel { contentId ageRestriction title gallery { logos { main { avatarsUrl __typename } __typename } __typename } __typename } __typename } fragment MovieStreamAudioMeta on AudioMeta { audioChannelsNumber audioGroupId forAdult language languageName quality studioName title visibleByDefault __typename } fragment MovieStreamDrmConfig on DrmConfig { drmServers { certificateUrl name processSPCPath provisioningUrl url __typename } headers requestParams __typename } fragment MovieStreamSubtitleMeta on SubtitleMeta { forAdult languageName studio type language title url visibleByDefault __typename } fragment MovieStreamTile on Tile { spriteDuration tileDuration uriTemplate tilePixelResolution { height width __typename } spriteMatrixSize { columns rows __typename } spritePixelResolution { height width __typename } __typename } fragment MovieStreamTiles on TilesContainer { highResolutionTiles { ...MovieStreamTile __typename } lowResolutionTiles { ...MovieStreamTile __typename } offset __typename } fragment MovieStreamPrerolls on PrerollMeta { durationMs promotedEntityId __typename } fragment MovieStreamSkippableFragment on SkippableFragment { startTime endTime final type __typename } fragment MovieStreamMeta on StreamMeta { audioMetas { ...MovieStreamAudioMeta __typename } drmConfig { ...MovieStreamDrmConfig __typename } subtitleMetas { ...MovieStreamSubtitleMeta __typename } tilesContainer { ...MovieStreamTiles __typename } trackings videoMetas { bitrate height width __typename } prerollsDuration prerolls { ...MovieStreamPrerolls __typename } skippableFragments { ...MovieStreamSkippableFragment __typename } videoToken cuesUrl __typename } fragment MovieStream on Stream { streamMeta { ...MovieStreamMeta __typename } uri __typename } fragment MovieOnlineStreams on OnlineStreams { licenseStatus drmRequirement streams { ...MovieStream __typename } sessionId concurrencyArbiterConfig { concurrencyArbiterRequestParams server __typename } playerRestrictionConfig { subtitlesButtonEnable __typename } monetizationModel __typename } fragment CatchupStreams on Catchup { onlineStreams { ...MovieOnlineStreams __typename } __typename } fragment CatchupMeta on Catchup { catchupTitle: title duration hasSmokingScenes ageRestriction viewOption { watchabilityExpirationTime __typename } __typename } fragment FilmPlayerOnlineStreams on Ott { ... on Ott_AbstractVideo { onlineStreams(prerollsSupported: $prerollsSupported) { ...MovieOnlineStreams __typename } __typename } __typename } fragment SmokingFilmFlag on Ott { ... on Ott_AbstractVideo { hasSmokingScenes __typename } __typename } fragment MovieTiming on OttTiming { current __typename } fragment FilmOttPreview on Ott_AbstractVideo { preview { ... on OttPreview_AbstractVideo { timing { ...MovieTiming __typename } duration __typename } __typename } __typename } fragment MovieWatchParams on OttUserData { watchParams { audioLanguage subtitleLanguage __typename } __typename } fragment FilmAgeRestriction on VideoInterface { restriction { age __typename } __typename } fragment EpisodeDetails on Episode { episodeContentId: contentId number tvSeries { contentId id __typename } season { contentId number __typename } episodeOtt: ott { viewOption { watchabilityStatus __typename } __typename } __typename } fragment EpisodeAgeRestriction on Episode { tvSeries { contentId restriction { age __typename } __typename } __typename } fragment EpisodePlayerOnlineStreams on OttEpisode { onlineStreams(prerollsSupported: $prerollsSupported) { ...MovieOnlineStreams __typename } __typename } fragment EpisodeSmokingFlag on OttEpisode { hasSmokingScenes __typename } fragment EpisodePlayerNextEpisode on Episode { neighbourhoodInSeries(limit: 1, filter: {onlyOnline: true}) { items { ...EpisodeDetails __typename } __typename } __typename } fragment ChannelMeta on TvChannel { ageRestriction title gallery { logos { main { avatarsUrl fallbackUrl __typename } __typename } covers { horizontal { avatarsUrl fallbackUrl __typename } __typename } __typename } isNeedToHidePrograms isMultiplex __typename } fragment ChannelStreams on TvChannel { onlineStreams { ...MovieOnlineStreams __typename } __typename } fragment TvChannelProgram on TvProgram { id title startTime endTime episodeTitle ageRestriction typeName catchupContentId image { avatarsUrl fallbackUrl __typename } __typename } fragment ChannelPrograms on TvChannel { programs: tvPrograms(duration: "PT4H") { ...TvChannelProgram __typename } __typename } fragment ClipStreamMeta on StreamMeta { audioMetas { ...MovieStreamAudioMeta __typename } drmConfig { ...MovieStreamDrmConfig __typename } subtitleMetas { ...MovieStreamSubtitleMeta __typename } tilesContainer { ...MovieStreamTiles __typename } trackings videoMetas { bitrate height width __typename } skippableFragments { ...MovieStreamSkippableFragment __typename } videoToken cuesUrl __typename } fragment ClipStream on Stream { streamMeta { ...ClipStreamMeta __typename } uri __typename } fragment ClipOnlineStreams on OnlineStreams { licenseStatus drmRequirement streams { ...ClipStream __typename } sessionId concurrencyArbiterConfig { concurrencyArbiterRequestParams server __typename } playerRestrictionConfig { subtitlesButtonEnable __typename } monetizationModel __typename } fragment ClipStreams on Clip { onlineStreams { ...ClipOnlineStreams __typename } __typename } fragment CatchupPlayerBaseInfo on Catchup { catchupContentId: contentId ...CatchupTvChannel ...CatchupStreams ...CatchupMeta __typename } fragment FilmPlayerBaseInfo on VideoInterface { filmContentId: contentId id filmOtt: ott { ...FilmPlayerOnlineStreams ...SmokingFilmFlag ...FilmOttPreview userData { ...MovieWatchParams __typename } __typename } ...FilmAgeRestriction __typename } fragment EpisodePlayerBaseInfo on Episode { id episodeContentId: contentId ...EpisodeDetails ...EpisodeAgeRestriction episodeOtt: ott { ...EpisodePlayerOnlineStreams ...EpisodeSmokingFlag timing { ...MovieTiming __typename } duration __typename } ...EpisodePlayerNextEpisode tvSeries { contentId ott { userData { ...MovieWatchParams __typename } __typename } __typename } __typename } fragment ChannelPlayerBaseInfo on TvChannel { channelContentId: contentId ...ChannelMeta ...ChannelStreams ...ChannelPrograms __typename } fragment ClipPlayerBaseInfo on Clip { clipContentId: contentId ...ClipStreams __typename } fragment ChildLockPlayerKidSubProfile on UserKidSubProfile { id { ottId puid __typename } restrictions { parentalControlVideo { streamUrl id __typename } parentalControlEnabled __typename } __typename }'
            }

            print(f"📤 Sending PlayerBaseInfo request to: {url}")
            print(f"📤 Payload: {payload}")
            print(f"📤 Headers: {self.current_headers}")

            # Get proxy configuration
            proxies = self.get_proxy_config()
            response = requests.post(url, json=payload, headers=self.current_headers, proxies=proxies)

            print(f"📥 PlayerBaseInfo Response status code: {response.status_code}")
            print(f"📥 PlayerBaseInfo Response headers: {dict(response.headers)}")
            print(f"📥 PlayerBaseInfo Response text: {response.text}")

            response.raise_for_status()

            data = response.json()
            print(f"✅ Movie details fetched successfully")
            print(f"📊 PlayerBaseInfo Response data: {data}")

            # Emit signal with streaming data
            self.streams_found.emit(data)
            return data

        except requests.exceptions.RequestException as e:
            print(f"❌ Network error while fetching movie details: {str(e)}")
            print(f"❌ PlayerBaseInfo Response status: {getattr(e.response, 'status_code', 'N/A')}")
            print(f"❌ PlayerBaseInfo Response text: {getattr(e.response, 'text', 'N/A')}")
            self.error_occurred.emit(f"Network error: {str(e)}")
            return None
        except Exception as e:
            print(f"❌ Error fetching movie details: {str(e)}")
            self.error_occurred.emit(f"Error: {str(e)}")
            return None

    def search_movie(self, content_id):
        """Search for a movie by content ID using original YANGO API"""
        try:
            print(f"🔍 Searching for movie with ID: {content_id}")

            # First, try to get basic movie info using MovieCard
            movie_card_response = self.fetch_and_print_movie_card(content_id)

            if movie_card_response and "data" in movie_card_response and "movieByContentUuid" in movie_card_response["data"]:
                movie_data = movie_card_response["data"]["movieByContentUuid"]

                if movie_data:
                    title = movie_data.get('title', {})
                    if isinstance(title, dict):
                        title_text = title.get('localized', title.get('original', 'Unknown'))
                    else:
                        title_text = str(title) if title else 'Unknown'

                    print(f"✅ Movie found: {title_text}")

                    # Add content_id to movie_data for easier access
                    movie_data['content_id'] = content_id

                    # Check content type and emit appropriate signal
                    content_type = movie_data.get('__typename', '')
                    if content_type == 'TvSeries':
                        print(f"📺 Content is a TV Series, emitting series_found signal")
                        self.series_found.emit(movie_data)
                    else:
                        # For movies, get detailed streaming info using PlayerBaseInfo
                        print(f"🎬 Getting streaming details...")
                        streaming_details = self.fetch_movie_details(content_id)

                        if streaming_details:
                            # Combine movie card data with streaming details
                            movie_data['streaming_details'] = streaming_details

                        print(f"🎬 Content is a Movie, emitting movie_found signal")
                        self.movie_found.emit(movie_data)

                    return movie_data
                else:
                    # Try searching as series instead
                    print(f"🔄 Movie not found, trying as series...")
                    return self.search_series(content_id)
            else:
                # Try searching as series instead
                print(f"🔄 Movie search failed, trying as series...")
                return self.search_series(content_id)

        except Exception as e:
            error_msg = f"Error searching movie: {str(e)}"
            print(f"❌ {error_msg}")
            self.error_occurred.emit(error_msg)
            return None

    def fetch_season_details(self, content_id, default_release_year="N/A"):
        """
        Fetch season details using SerialStructureSeason query from original YANGO script
        """
        try:
            print(f"🔍 Fetching season details for ID: {content_id}")

            url = f"{self.base_url}?operationName=SerialStructureSeason"

            payload = {
                "operationName": "SerialStructureSeason",
                "variables": {
                    "contentId": content_id,
                    "limit": 30,
                    "offset": 0,
                    "withUserData": True,
                },
                "query": "query SerialStructureSeason($contentId: String!, $limit: Int!, $offset: Int!, $withUserData: Boolean!) { seasonByContentId(contentId: $contentId) { id contentId number episodes(limit: $limit, offset: $offset, isOnlyOnline: true) { total items { ...SerialStructureEpisode __typename } __typename } __typename } } fragment EpisodeBase on Episode { id contentId number __typename } fragment Title on Title { localized original __typename } fragment SeasonBase on Season { id contentId number __typename } fragment SerialStructureEpisode on Episode { ...EpisodeBase synopsis(override: OTT_WHEN_EXISTS) title { ...Title __typename } season { ...SeasonBase __typename } cover { avatarsUrl fallbackUrl __typename } ott { duration editorAnnotation plannedAvailabilityDate viewOption { availabilityStatus availabilityEndDate watchabilityStatus __typename } timing @include(if: $withUserData) { current __typename } __typename } __typename } "
            }

            print(f"📤 Sending SerialStructureSeason request to: {url}")
            print(f"📤 Payload: {payload}")
            print(f"📤 Headers: {self.current_headers}")

            response = requests.post(url, json=payload, headers=self.current_headers)

            print(f"📥 SerialStructureSeason Response status code: {response.status_code}")
            print(f"📥 SerialStructureSeason Response headers: {dict(response.headers)}")
            print(f"📥 SerialStructureSeason Response text: {response.text}")

            response.raise_for_status()

            data = response.json()

            print(f"✅ Season details fetched successfully")
            print(f"📊 SerialStructureSeason Response data: {data}")
            return data

        except requests.exceptions.RequestException as e:
            print(f"❌ Network error while fetching season details: {str(e)}")
            return None
        except Exception as e:
            print(f"❌ Error fetching season details: {str(e)}")
            return None

    def fetch_season_details_and_display(self, content_id):
        """
        Fetch episodes of a season using its contentId with pagination support
        """
        try:
            print(f"🔍 Fetching season details for ID: {content_id}")

            all_episodes = []
            offset = 0
            limit = 30  # Keep original limit but use pagination
            total_episodes = None

            while True:
                url = f"{self.base_url}?operationName=SerialStructureSeason"

                payload = {
                    "operationName": "SerialStructureSeason",
                    "variables": {
                        "contentId": content_id,
                        "limit": limit,
                        "offset": offset,
                        "withUserData": True,
                    },
                    "query": "query SerialStructureSeason($contentId: String!, $limit: Int!, $offset: Int!, $withUserData: Boolean!) { seasonByContentId(contentId: $contentId) { id contentId number episodes(limit: $limit, offset: $offset, isOnlyOnline: true) { total items { ...SerialStructureEpisode __typename } __typename } __typename } } fragment EpisodeBase on Episode { id contentId number __typename } fragment Title on Title { localized original __typename } fragment SeasonBase on Season { id contentId number __typename } fragment SerialStructureEpisode on Episode { ...EpisodeBase synopsis(override: OTT_WHEN_EXISTS) title { ...Title __typename } season { ...SeasonBase __typename } cover { avatarsUrl fallbackUrl __typename } ott { duration editorAnnotation plannedAvailabilityDate viewOption { availabilityStatus availabilityEndDate watchabilityStatus __typename } timing @include(if: $withUserData) { current __typename } __typename } __typename } "
                }

                print(f"📤 Fetching episodes batch: offset={offset}, limit={limit}")

                # Get proxy configuration
                proxies = self.get_proxy_config()
                response = requests.post(url, json=payload, headers=self.current_headers, proxies=proxies)
                response.raise_for_status()

                data = response.json()
                season_data = data.get("data", {}).get("seasonByContentId", None)

                if not season_data:
                    print(f"❌ No season data found for content ID: {content_id}")
                    break

                episodes_data = season_data.get("episodes", {})

                if not episodes_data:
                    print(f"❌ No episodes data found")
                    break

                # Get total episodes count from first request
                if total_episodes is None:
                    total_episodes = episodes_data.get("total", 0)
                    print(f"📊 Total episodes in season: {total_episodes}")

                current_episodes = episodes_data.get("items", [])

                if not current_episodes:
                    print(f"📋 No more episodes found at offset {offset}")
                    break

                print(f"📋 Found {len(current_episodes)} episodes in this batch")
                all_episodes.extend(current_episodes)

                # Check if we have all episodes
                if len(all_episodes) >= total_episodes or len(current_episodes) < limit:
                    print(f"✅ Fetched all episodes: {len(all_episodes)}/{total_episodes}")
                    break

                # Move to next batch
                offset += limit

            if not all_episodes:
                print(f"❌ No episodes found for season with content ID: {content_id}")
                return []

            # تحديد رقم الموسم
            season_number = season_data.get('number', 1)
            print(f"📺 Season Number: {season_number}")

            # تجهيز الحلقات
            formatted_episodes = []

            for episode in all_episodes:
                # تحديد حالة الحلقة
                availability_status = episode.get("ott", {}).get("viewOption", {}).get("availabilityStatus", None)
                planned_date = episode.get("ott", {}).get("plannedAvailabilityDate", None)

                if availability_status == "PUBLISHED":
                    status = "Available"
                elif planned_date:
                    # استخراج التاريخ بالتنسيق MM/DD
                    formatted_date = "/".join(planned_date.split("T")[0].split("-")[1:])
                    status = formatted_date
                else:
                    status = "Soon"  # عرض قريباً إذا لم يكن هناك تاريخ إصدار

                # تنسيق البيانات
                formatted_episodes.append({
                    "Episode": f"E{str(episode.get('number')).zfill(2)}",
                    "Status/Release Date": status,
                    "Content ID": episode.get("contentId"),  # حفظ Content ID للمعالجة
                    "episode_number": episode.get('number'),
                    "content_id": episode.get("contentId"),
                    "title": episode.get("title", {}).get("localized") or episode.get("title", {}).get("original") or f"Episode {episode.get('number')}",
                    "availability_status": availability_status,
                    "episode_data": episode
                })

            print(f"✅ Season details fetched successfully")
            print(f"📋 Found {len(formatted_episodes)} episodes for Season {season_number}")

            return formatted_episodes

        except requests.exceptions.RequestException as e:
            print(f"❌ Failed to fetch season details: {e}")
            return []
        except Exception as e:
            print(f"❌ An unexpected error occurred: {e}")
            return []

    def get_player_base_info(self, content_id):
        """
        Get player base info using PlayerBaseInfo query from original YANGO script
        """
        try:
            print(f"🔍 Getting player base info for ID: {content_id}")

            url = f"{self.base_url}?operationName=PlayerBaseInfo"

            payload = {
                "operationName": "PlayerBaseInfo",
                "variables": {
                    "prerollsSupported": True,
                    "isKidSubProfile": False,
                    "contentId": content_id,
                },
                "query": "query PlayerBaseInfo($contentId: String!, $prerollsSupported: Boolean = false, $isKidSubProfile: Boolean = false) { content(contentId: $contentId) { ... on Catchup { ...CatchupPlayerBaseInfo __typename } ... on VideoInterface { ...FilmPlayerBaseInfo __typename } ... on Episode { ...EpisodePlayerBaseInfo __typename } ... on TvChannel { ...ChannelPlayerBaseInfo __typename } ... on Clip { ...ClipPlayerBaseInfo __typename } __typename } userProfile @include(if: $isKidSubProfile) { ...ChildLockPlayerKidSubProfile __typename } } fragment CatchupTvChannel on Catchup { tvChannel { contentId ageRestriction title gallery { logos { main { avatarsUrl __typename } __typename } __typename } __typename } __typename } fragment MovieStreamAudioMeta on AudioMeta { audioChannelsNumber audioGroupId forAdult language languageName quality studioName title visibleByDefault __typename } fragment MovieStreamDrmConfig on DrmConfig { drmServers { certificateUrl name processSPCPath provisioningUrl url __typename } headers requestParams __typename } fragment MovieStreamSubtitleMeta on SubtitleMeta { forAdult languageName studio type language title url visibleByDefault __typename } fragment MovieStreamTile on Tile { spriteDuration tileDuration uriTemplate tilePixelResolution { height width __typename } spriteMatrixSize { columns rows __typename } spritePixelResolution { height width __typename } __typename } fragment MovieStreamTiles on TilesContainer { highResolutionTiles { ...MovieStreamTile __typename } lowResolutionTiles { ...MovieStreamTile __typename } offset __typename } fragment MovieStreamPrerolls on PrerollMeta { durationMs promotedEntityId __typename } fragment MovieStreamSkippableFragment on SkippableFragment { startTime endTime final type __typename } fragment MovieStreamMeta on StreamMeta { audioMetas { ...MovieStreamAudioMeta __typename } drmConfig { ...MovieStreamDrmConfig __typename } subtitleMetas { ...MovieStreamSubtitleMeta __typename } tilesContainer { ...MovieStreamTiles __typename } trackings videoMetas { bitrate height width __typename } prerollsDuration prerolls { ...MovieStreamPrerolls __typename } skippableFragments { ...MovieStreamSkippableFragment __typename } videoToken cuesUrl __typename } fragment MovieStream on Stream { streamMeta { ...MovieStreamMeta __typename } uri __typename } fragment MovieOnlineStreams on OnlineStreams { licenseStatus drmRequirement streams { ...MovieStream __typename } sessionId concurrencyArbiterConfig { concurrencyArbiterRequestParams server __typename } playerRestrictionConfig { subtitlesButtonEnable __typename } monetizationModel __typename } fragment CatchupStreams on Catchup { onlineStreams { ...MovieOnlineStreams __typename } __typename } fragment CatchupMeta on Catchup { catchupTitle: title duration hasSmokingScenes ageRestriction viewOption { watchabilityExpirationTime __typename } __typename } fragment FilmPlayerOnlineStreams on Ott { ... on Ott_AbstractVideo { onlineStreams(prerollsSupported: $prerollsSupported) { ...MovieOnlineStreams __typename } __typename } __typename } fragment SmokingFilmFlag on Ott { ... on Ott_AbstractVideo { hasSmokingScenes __typename } __typename } fragment MovieTiming on OttTiming { current __typename } fragment FilmOttPreview on Ott_AbstractVideo { preview { ... on OttPreview_AbstractVideo { timing { ...MovieTiming __typename } duration __typename } __typename } __typename } fragment MovieWatchParams on OttUserData { watchParams { audioLanguage subtitleLanguage __typename } __typename } fragment FilmAgeRestriction on VideoInterface { restriction { age __typename } __typename } fragment EpisodeDetails on Episode { episodeContentId: contentId number tvSeries { contentId id __typename } season { contentId number __typename } episodeOtt: ott { viewOption { watchabilityStatus __typename } __typename } __typename } fragment EpisodeAgeRestriction on Episode { tvSeries { contentId restriction { age __typename } __typename } __typename } fragment EpisodePlayerOnlineStreams on OttEpisode { onlineStreams(prerollsSupported: $prerollsSupported) { ...MovieOnlineStreams __typename } __typename } fragment EpisodeSmokingFlag on OttEpisode { hasSmokingScenes __typename } fragment EpisodePlayerNextEpisode on Episode { neighbourhoodInSeries(limit: 1, filter: {onlyOnline: true}) { items { ...EpisodeDetails __typename } __typename } __typename } fragment ChannelMeta on TvChannel { ageRestriction title gallery { logos { main { avatarsUrl fallbackUrl __typename } __typename } covers { horizontal { avatarsUrl fallbackUrl __typename } __typename } __typename } isNeedToHidePrograms isMultiplex __typename } fragment ChannelStreams on TvChannel { onlineStreams { ...MovieOnlineStreams __typename } __typename } fragment TvChannelProgram on TvProgram { id title startTime endTime episodeTitle ageRestriction typeName catchupContentId image { avatarsUrl fallbackUrl __typename } __typename } fragment ChannelPrograms on TvChannel { programs: tvPrograms(duration: \"PT4H\") { ...TvChannelProgram __typename } __typename } fragment ClipStreamMeta on StreamMeta { audioMetas { ...MovieStreamAudioMeta __typename } drmConfig { ...MovieStreamDrmConfig __typename } subtitleMetas { ...MovieStreamSubtitleMeta __typename } tilesContainer { ...MovieStreamTiles __typename } trackings videoMetas { bitrate height width __typename } skippableFragments { ...MovieStreamSkippableFragment __typename } videoToken cuesUrl __typename } fragment ClipStream on Stream { streamMeta { ...ClipStreamMeta __typename } uri __typename } fragment ClipOnlineStreams on OnlineStreams { licenseStatus drmRequirement streams { ...ClipStream __typename } sessionId concurrencyArbiterConfig { concurrencyArbiterRequestParams server __typename } playerRestrictionConfig { subtitlesButtonEnable __typename } monetizationModel __typename } fragment ClipStreams on Clip { onlineStreams { ...ClipOnlineStreams __typename } __typename } fragment CatchupPlayerBaseInfo on Catchup { catchupContentId: contentId ...CatchupTvChannel ...CatchupStreams ...CatchupMeta __typename } fragment FilmPlayerBaseInfo on VideoInterface { filmContentId: contentId id filmOtt: ott { ...FilmPlayerOnlineStreams ...SmokingFilmFlag ...FilmOttPreview userData { ...MovieWatchParams __typename } __typename } ...FilmAgeRestriction __typename } fragment EpisodePlayerBaseInfo on Episode { id episodeContentId: contentId ...EpisodeDetails ...EpisodeAgeRestriction episodeOtt: ott { ...EpisodePlayerOnlineStreams ...EpisodeSmokingFlag timing { ...MovieTiming __typename } duration __typename } ...EpisodePlayerNextEpisode tvSeries { contentId ott { userData { ...MovieWatchParams __typename } __typename } __typename } __typename } fragment ChannelPlayerBaseInfo on TvChannel { channelContentId: contentId ...ChannelMeta ...ChannelStreams ...ChannelPrograms __typename } fragment ClipPlayerBaseInfo on Clip { clipContentId: contentId ...ClipStreams __typename } fragment ChildLockPlayerKidSubProfile on UserKidSubProfile { id { ottId puid __typename } restrictions { parentalControlVideo { streamUrl id __typename } parentalControlEnabled __typename } __typename } "
            }

            print(f"📤 Sending PlayerBaseInfo request to: {url}")
            print(f"📤 Payload: {payload}")
            print(f"📤 Headers: {self.current_headers}")

            response = requests.post(url, json=payload, headers=self.current_headers)

            print(f"📥 PlayerBaseInfo Response status code: {response.status_code}")
            print(f"📥 PlayerBaseInfo Response headers: {dict(response.headers)}")
            print(f"📥 PlayerBaseInfo Response text: {response.text}")

            response.raise_for_status()

            data = response.json()

            print(f"✅ Player base info fetched successfully")
            print(f"📊 PlayerBaseInfo Response data: {data}")
            return data

        except requests.exceptions.RequestException as e:
            print(f"❌ Network error while getting player base info: {str(e)}")
            print(f"❌ Response status: {getattr(e.response, 'status_code', 'N/A')}")
            print(f"❌ Response text: {getattr(e.response, 'text', 'N/A')}")
            return None
        except Exception as e:
            print(f"❌ Error getting player base info: {str(e)}")
            return None

    def fetch_movies(self):
        """
        Fetch movies from API using Selection query from original YANGO script
        """
        try:
            print("🔍 Fetching movies from YANGO API...")

            url = f"{self.base_url}?operationName=Selection"

            payload = {
                "operationName": "Selection",
                "variables": {
                    "selectionId": "movies",
                    "limit": 100,
                    "offset": 0
                },
                "query": "query Selection($selectionId: String!, $limit: Int!, $offset: Int!) { selection(id: $selectionId) { items(limit: $limit, offset: $offset) { ... on Movie { id contentId title { localized original __typename } gallery { posters { vertical { avatarsUrl __typename } __typename } __typename } __typename } __typename } __typename } }"
            }

            response = requests.post(url, json=payload, headers=self.current_headers)
            response.raise_for_status()

            data = response.json()
            movies = data.get("data", {}).get("selection", {}).get("items", [])

            print(f"✅ Fetched {len(movies)} movies")
            return movies

        except Exception as e:
            error_msg = f"Error fetching movies: {str(e)}"
            print(f"❌ {error_msg}")
            self.error_occurred.emit(error_msg)
            return []

    def fetch_series(self):
        """
        Fetch series from API using Selection query from original YANGO script
        """
        try:
            print("🔍 Fetching series from YANGO API...")

            url = f"{self.base_url}?operationName=Selection"

            payload = {
                "operationName": "Selection",
                "variables": {
                    "selectionId": "series",
                    "limit": 100,
                    "offset": 0
                },
                "query": "query Selection($selectionId: String!, $limit: Int!, $offset: Int!) { selection(id: $selectionId) { items(limit: $limit, offset: $offset) { ... on Series { id contentId title { localized original __typename } gallery { posters { vertical { avatarsUrl __typename } __typename } __typename } __typename } __typename } __typename } }"
            }

            response = requests.post(url, json=payload, headers=self.current_headers)
            response.raise_for_status()

            data = response.json()
            series = data.get("data", {}).get("selection", {}).get("items", [])

            print(f"✅ Fetched {len(series)} series")
            return series

        except Exception as e:
            error_msg = f"Error fetching series: {str(e)}"
            print(f"❌ {error_msg}")
            self.error_occurred.emit(error_msg)
            return []

    def search_series(self, content_id):
        """Search for a series by content ID"""
        try:
            print(f"🔍 Searching for series with ID: {content_id}")

            # Try to fetch as movie first (YANGO uses same endpoint for both)
            movie_card_response = self.fetch_and_print_movie_card(content_id)

            if movie_card_response and "data" in movie_card_response:
                series_data = movie_card_response["data"].get("movieByContentUuid", {})

                if series_data:
                    title = series_data.get('title', {})
                    if isinstance(title, dict):
                        title_text = title.get('localized', title.get('original', 'Unknown'))
                    else:
                        title_text = str(title) if title else 'Unknown'

                    print(f"✅ Series found: {title_text}")

                    # Add content_id to series_data for easier access
                    series_data['content_id'] = content_id

                    # Try to fetch season details for series
                    season_details = self.fetch_season_details(content_id)
                    if season_details:
                        series_data['season_details'] = season_details

                    self.series_found.emit(series_data)
                    return series_data
                else:
                    self.error_occurred.emit(f"Content not found with ID: {content_id}")
                    return None
            else:
                self.error_occurred.emit(f"Failed to fetch content data for ID: {content_id}")
                return None

        except Exception as e:
            error_msg = f"Error searching series: {str(e)}"
            print(f"❌ {error_msg}")
            self.error_occurred.emit(error_msg)
            return None

    def load_settings(self):
        """Load settings manager for proxy support"""
        try:
            from .yango_settings import YangoSettings
            self.settings_manager = YangoSettings()
            print("✅ Settings loaded in API")
        except Exception as e:
            print(f"❌ Error loading settings in API: {e}")
            self.settings_manager = None

    def get_proxy_config(self):
        """Get proxy configuration from settings"""
        try:
            if self.settings_manager:
                proxy_config = self.settings_manager.get_proxy_config()
                if proxy_config:
                    print(f"🌐 Using proxy configuration: {list(proxy_config.keys())}")
                return proxy_config
            return None
        except Exception as e:
            print(f"❌ Error getting proxy config: {e}")
            return None