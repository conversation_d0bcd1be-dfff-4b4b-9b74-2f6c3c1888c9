import subprocess
import sys
import threading
import time

# قائمة بجميع المكتبات المطلوبة
required_libraries = [
    "argparse", "base64", "os", "tabulate", "requests",
    "json", "urllib.parse", "xmltodict",
    "colorama", "subprocess", "shutil", "prettytable"
]

# تثبيت المكتبات المطلوبة
for library in required_libraries:
    try:
        __import__(library.split('.')[0])  # محاولة استيراد المكتبة
    except ImportError:
        subprocess.check_call([sys.executable, "-m", "pip", "install", library])

# تابع تنفيذ السكربت
import argparse
from base64 import b64encode
import base64
import os
from tabulate import tabulate
import requests
import json
from urllib.parse import unquote
import xmltodict
from pywidevine.cdm import cdm, deviceconfig
from pywidevine.utils import *
from pywidevine.decrypt.wvdecrypt import WvDecrypt
from colorama import Fore, Style, init
from xml.etree import ElementTree as ET
from tabulate import tabulate
from colorama import Fore, Style, init
import subprocess
import shutil
from prettytable import PrettyTable


currentFile = __file__
realPath = os.path.realpath(currentFile)
dirPath = os.path.dirname(realPath)
dirName = os.path.basename(dirPath)

################# BINARIES ################
cache_dir = os.path.join(dirPath, "cache")
download_dir = os.path.join(dirPath, "downloads")
KEYS_PATH = os.path.join(dirPath, "KEYS", "OSNPLUS_KEYS.txt")
mkvmerge_path = os.path.join(dirPath, "bin", "mkvmerge.exe")
n_m3u8dl_path = os.path.join(dirPath, "bin", "N_m3u8DL-RE.exe")
mp4decrypt_path = os.path.join(dirPath, "bin", "mp4decrypt.exe")
mediainfo_path = os.path.join(dirPath, "bin", "mediainfo.exe")
ffmpeg_path = os.path.join(dirPath, "bin", "ffmpeg.exe")

# تطهير المسار الخاص بـ downloads للتأكد من أنه لا يحتوي على أحرف غير صالحة
# تأكد من أن المسارات تحتوي على الفواصل الصحيحة

# التأكد من إنشاء المجلدات الأساسية
if not os.path.exists(download_dir):
    os.makedirs(download_dir)


def sanitize_filename(filename):
    return filename.translate(str.maketrans('', '', ':?*<>|\\/'))

# دالة لتنظيف المسار بأكمله
def sanitize_path(path):
    # إزالة الأحرف غير الصالحة في المسار
    path = sanitize_filename(path)

    # التأكد من أن المسار لا يتجاوز الحد المسموح به في Windows
    if len(path) > 260:
        path = r'\\?\{}'.format(path)  # دعم المسارات الطويلة

    # إعادة المسار المعدل
    return path


arguments = argparse.ArgumentParser()
arguments.add_argument("-g", dest="group", help="group release", default='NAIM2007')
arguments.add_argument("-keys", dest="keys", action='store_true', help="show keys only")
args = arguments.parse_args()


os.system('')
GREEN = '\033[32m'
MAGENTA = '\033[35m'
YELLOW = '\033[33m'
RED = '\033[31m'
CYAN = '\033[36m'
RESET = '\033[0m'

init(autoreset=True)

# إعداد الألوان
GREEN = Fore.GREEN
MAGENTA = Fore.MAGENTA
YELLOW = Fore.YELLOW
RED = Fore.RED
CYAN = Fore.CYAN
RESET = Fore.RESET

# Define colors
BLUE = Fore.BLUE + Style.BRIGHT
RESET = Style.RESET_ALL


import threading
import time
import requests
import json
from urllib.parse import unquote

# تخزين التوكن الحالي
saved_access_token = None

def load_refresh_token_from_cookies(file_path="cookies.txt"):
    """تحميل refreshToken من الكوكيز"""
    try:
        with open(file_path, "r") as file:
            lines = file.readlines()
            for line in lines:
                if "auth" in line:
                    auth_cookie = line.split("\t")[-1].strip()
                    decoded_auth = unquote(auth_cookie)
                    try:
                        auth_data = json.loads(decoded_auth)
                    except json.JSONDecodeError:
                        decoded_auth = unquote(decoded_auth)
                        auth_data = json.loads(decoded_auth)
                    return auth_data.get("refreshToken")
    except (FileNotFoundError, json.JSONDecodeError):
        return None
    return None

def refresh_access_token(silent_mode=False):
    """تحديث accessToken باستخدام refreshToken"""
    global saved_access_token
    refresh_token_value = load_refresh_token_from_cookies()
    if not refresh_token_value:
        if not silent_mode:
            print("❌ No refreshToken found.")
        return None

    url = "https://koussa-osn.anghami.com/osn/auth/v1/refresh-token"
    headers = {
        'accept': '*/*',
        'accept-language': 'en-US,en;q=0.9',
        'priority': 'u=1, i',
        'referer': 'https://osnplus.com/en-ae/',
        'rsc': '1',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36 Edg/124.0.0.0',
        'Content-Type': 'application/json',
    }

    payload = {"refreshToken": refresh_token_value}
    response = requests.post(url, headers=headers, json=payload)

    if response.status_code == 200:
        data = response.json()
        access_token = data.get("token")
        if access_token:
            save_access_token(access_token)
            saved_access_token = access_token  # تحديث التوكن الحالي
            if not silent_mode:
                print("✅ Token refreshed successfully!")
            return access_token
    if not silent_mode:
        print("❌ Failed to refresh token. Status code:", response.status_code)
    return None

def save_access_token(access_token, file_path="refresh_token.txt"):
    """حفظ التوكن في ملف"""
    with open(file_path, "w") as file:
        file.write(f"accessToken: {access_token}")

def get_access_token():
    """إرجاع التوكن الحالي، وتحديثه إذا كان غير موجود"""
    global saved_access_token
    if not saved_access_token:
        saved_access_token = refresh_access_token(silent_mode=True)
    return saved_access_token

def auto_refresh_token(interval=1800):  # تحديث التوكن كل 30 دقيقة
    while True:
        refresh_access_token(silent_mode=True)
        time.sleep(interval)

# تشغيل التحديث التلقائي في Thread منفصل
refresh_thread = threading.Thread(target=auto_refresh_token, daemon=True)
refresh_thread.start()

def get_movie_details(movie_link, access_token):
    """Function to get movie details from the API using the movie link."""
    url = "https://api.osnplus.com/osn/media/v1/get-watch-content"

    headers = {
        'accept': '*/*',
        'accept-language': 'en-US,en;q=0.9',
        'access_token': access_token,
        'client_platform': 'web-osn',
        'client_version': '1.1.1',
        'content-type': 'application/json',
        'device_unique_identifier': '35b4ffb9-654f-4fca-a132-8ed1011d1927',
        'language': 'en',
        'origin': 'https://osnplus.com',
        'referer': 'https://osnplus.com/',
    }

    # Extract the movie ID from the link
    movie_id = movie_link.split("-")[-1]

    # Prepare payload with the correct structure
    payload = {
        "contentIds": [movie_id]
    }

    # Send the request as a POST request with JSON payload
    response = requests.post(url, headers=headers, json=payload)

    if response.status_code == 200:
        data = response.json()

        # Extract movie details
        try:
            movie_data = data["watchContents"][movie_id]["movie"]["movie"]
            show_titles = movie_data["title"].get("en", "Title not found")
            release_year = movie_data.get("year", "Year not found")
            sanitized_title = f"{show_titles} - {release_year}".replace(":", " -").replace("?", "").replace("*", "").replace("<", "").replace(">", "").replace("|", "").replace("\\", "").replace("/", "")

            # Use unified folder and file naming format
            download_path = os.path.join("downloads", sanitized_title)
            os.makedirs(download_path, exist_ok=True)

            # إعداد العناوين الملونة
            headers = [f"{Fore.YELLOW}Field{Style.RESET_ALL}", f"{Fore.YELLOW}Value{Style.RESET_ALL}"]

            # Display basic information in one row
            table_data = [
                ["Title & Release Year", f"{show_titles} ({release_year})"]
            ]

            # Extract IMDb rating if available
            imdb_rating = movie_data.get("imdbRating", {}).get("rating", "N/A")
            table_data.append(["IMDb Rating", imdb_rating])

            print(tabulate(table_data, headers=headers, tablefmt="fancy_grid", colalign=("center", "center")))

            # Download and save the poster
            poster_url = movie_data.get("images", {}).get("longImageWithTitleUrl") or movie_data.get("images", {}).get("wideImageWithTitleUrl")
            if (poster_url):
                download_and_save_poster(poster_url, sanitized_title, download_path)
            else:
                print("\nPoster URL not found.")

            # Display available streams
            streams = movie_data.get("streams", [])
            if streams:
                print("\n[+] Available Streams:")
                stream_table = []
                for index, stream in enumerate(streams, start=1):
                    if stream.get("manifestType") == "MANIFEST_TYPE_DASH":
                        stream_info = [
                            index,
                            stream.get("streamId", "N/A"),
                            stream.get("highestImageResolution", "N/A"),
                            # نترك فقط الأعمدة المطلوبة
                        ]
                        stream_table.append(stream_info)

                # إعداد العناوين للجدول
                headers = [
                    f"{Fore.YELLOW}Index{Style.RESET_ALL}",
                    f"{Fore.YELLOW}Stream ID{Style.RESET_ALL}",
                    f"{Fore.YELLOW}Resolution{Style.RESET_ALL}",
                ]

                print(tabulate(stream_table, headers=headers, tablefmt="fancy_grid", colalign=("center",) * len(headers)))

                # اختيار البث بواسطة المؤشر
                stream_choice = int(input("\nPlease select a stream by Index (number): "))
                selected_stream = next((s for i, s in enumerate(streams) if i + 1 == stream_choice), None)

                if selected_stream:
                    content_id = movie_id
                    stream_id = selected_stream['streamId']
                    mpd_url, lic_url, drmToken, cdnToken, pssh, kid = get_stream_details(content_id, stream_id, access_token)

                    if mpd_url and lic_url and drmToken and pssh and kid:
                        process_license(show_titles, mpd_url, lic_url, pssh, kid, drmToken,args)
                        parse_mpd(mpd_url, show_titles, release_year)
                    else:
                        print(f"{Fore.RED}Error: Could not retrieve all necessary details for license processing.{Style.RESET_ALL}")
                else:
                    print(f"{Fore.RED}Invalid Index selected.{Style.RESET_ALL}")
            else:
                print(f"{Fore.RED}\nNo streams available for this movie.{Style.RESET_ALL}")


        except KeyError:
            print(f"{Fore.RED}Error: Movie details not found in response.{Style.RESET_ALL}")
    else:
        print(f"{Fore.RED}Failed to retrieve movie details. Status code: {response.status_code}{Style.RESET_ALL}")
        print("Response:", response.text)


def download_and_save_poster(poster_url, sanitized_title, download_path):
    """Downloads and saves the movie poster inside the specified download path."""
    try:
        response = requests.get(poster_url, stream=True)
        response.raise_for_status()

        filename = f"{sanitized_title}_poster.jpg"
        file_path = os.path.join(download_path, filename)

        with open(file_path, "wb") as file:
            file.write(response.content)

        #print(f"\nPoster downloaded and saved as {file_path}.")

    except requests.exceptions.RequestException as e:
        print("Failed to download poster:", e)


def get_stream_details(content_id, stream_id, access_token):
    """Function to get stream details using contentId and streamId."""
    url = "https://api.osnplus.com/osn/media/v1/stream"

    headers = {
        'accept': '*/*',
        'accept-language': 'en-US,en;q=0.9',
        'access_token': access_token,
        'client_platform': 'web-osn',
        'client_version': '1.1.1',
        'content-type': 'application/json',
        'device_unique_identifier': '35b4ffb9-654f-4fca-a132-8ed1011d1927',
        'language': 'en',
        'origin': 'https://osnplus.com',
        'referer': 'https://osnplus.com/',
    }

    # Prepare payload with contentId and streamId
    payload = {
        "contentId": content_id,
        "streamId": stream_id
    }

    # Send the request as a POST request with JSON payload
    response = requests.post(url, headers=headers, json=payload)

    if response.status_code == 200:
        data = response.json()

        # Extract required information
        mpd_url = data['manifestUrl']
        lic_url = data['drmLicenseServers']['widevineLicenseUrl']
        drmToken = data['drmToken']
        cdnToken = data['cdnToken']

        # Construct the final mpd_url with token
        mpd_url_with_token = f"{mpd_url}?token={cdnToken}"
        # Fetch and extract pssh and KID from the MPD file
        pssh = get_pssh(mpd_url_with_token)
        kid = get_kid_mpd(mpd_url_with_token)

        return mpd_url_with_token, lic_url, drmToken, cdnToken, pssh, kid  # Return the extracted values
    else:
        print("Failed to retrieve stream details. Status code:", response.status_code)
        print("Response:", response.text)
        return None, None, None, None, None, None  # Return None in case of failure

def parse_mpd(mpd_url, show_titles, release_year):
    """Function to parse MPD file and extract available qualities, audio tracks, and subtitles."""
    try:
        # تنزيل ملف MPD
        response = requests.get(mpd_url)
        response.raise_for_status()  # التحقق من حالة الاستجابة
        mpd_content = response.content

        # تحليل ملف MPD باستخدام XML
        root = ET.fromstring(mpd_content)
        namespaces = {'mpd': 'urn:mpeg:dash:schema:mpd:2011'}  # إضافة مساحة اسم للـ MPD

        # إعداد قوائم لتخزين المعلومات
        quality_data = []
        audio_languages = set()
        subtitle_languages = set()

        # البحث عن AdaptationSet لمقاطع الفيديو، الصوت والترجمات
        for adaptation_set in root.findall('.//mpd:Period/mpd:AdaptationSet', namespaces):
            mime_type = adaptation_set.get('mimeType', 'unknown')
            if mime_type == 'video/mp4':
                for representation in adaptation_set.findall('mpd:Representation', namespaces):
                    resolution = representation.get('height') + "p"
                    bandwidth = representation.get('bandwidth')
                    uuid = representation.get('id')  # الحصول على UUID للفيديو
                    quality_data.append({
                        "No.": None,  # سيتم ملء الرقم لاحقاً
                        "Resolution": resolution,
                        "Bandwidth": f"{int(bandwidth)} bps",
                        "UUID": uuid,
                        "Audios": "",  # سيتم ملؤها لاحقاً
                        "Subtitles": ""
                    })
            elif mime_type == 'audio/mp4':
                audio_language = adaptation_set.get('lang', 'unknown')
                audio_languages.add(audio_language)
            elif mime_type == 'text/vtt':
                subtitle_language = adaptation_set.get('lang', 'unknown')
                subtitle_languages.add(subtitle_language)

        # تحويل مجموعات اللغات إلى سلاسل نصية
        audio_languages_str = " | ".join(sorted(audio_languages))
        subtitle_languages_str = " | ".join(sorted(subtitle_languages))

        # إضافة اللغات إلى كل صف جودة
        for row in quality_data:
            row["Audios"] = audio_languages_str
            row["Subtitles"] = subtitle_languages_str

        # فرز الجودة بناءً على الدقة، وفي حال تساوي الدقة، يتم الفرز حسب عرض النطاق الترددي
        quality_data.sort(key=lambda x: (int(x["Resolution"].replace("p", "")), int(x["Bandwidth"].replace(" bps", ""))))

        # إضافة الأرقام الترتيبية
        for i, row in enumerate(quality_data, start=1):
            row["No."] = i

        # إعداد العناوين الملونة
        headers = [
            f"{Fore.YELLOW}No.{Style.RESET_ALL}",
            f"{Fore.YELLOW}Resolution{Style.RESET_ALL}",
            f"{Fore.YELLOW}Bandwidth{Style.RESET_ALL}",
            f"{Fore.YELLOW}Audios{Style.RESET_ALL}",
            f"{Fore.YELLOW}Subtitles{Style.RESET_ALL}"
        ]

        # تحويل البيانات إلى قائمة من القوائم مع استبعاد عمود UUID والتأكد من الترقيم
        table_data = [[i+1, row["Resolution"], row["Bandwidth"], row["Audios"], row["Subtitles"]] for i, row in enumerate(quality_data)]

        # عرض الجدول
        print("\n[+] Available Qualities:")
        print(tabulate(table_data, headers=headers, tablefmt="fancy_grid", colalign=("center", "center", "center", "center", "center")))

        # طلب اختيار الجودة من المستخدم
        while True:
            try:
                selected_quality_index = int(input("\nPlease enter the number of the quality you want to download: ")) - 1
                if 0 <= selected_quality_index < len(quality_data):
                    # الحصول على الجودة المختارة وتمريرها لدالة التحميل
                    selected_row = quality_data[selected_quality_index]
                    resolution = selected_row["Resolution"]
                    bandwidth = selected_row["Bandwidth"]
                    uuid = selected_row["UUID"]  # UUID للجودة المختارة

                    print(f"Selected quality: Resolution={resolution}, Bandwidth={bandwidth}, UUID={uuid}")

                    # استدعاء دالة التحميل وتمرير UUID بدلاً من الدقة أو عرض النطاق الترددي
                    download_movie(mpd_url, resolution, bandwidth, uuid, show_titles, release_year)
                    print_banner()  # طباعة العنوان العلوي
                    main_menu()
                    break
                else:
                    print(f"{Fore.RED}Invalid selection. Please enter a valid number.{Style.RESET_ALL}")
            except ValueError:
                print(f"{Fore.RED}Invalid input. Please enter a number.{Style.RESET_ALL}")

    except requests.exceptions.RequestException as e:
        print(f"{Fore.RED}Failed to fetch MPD file: {e}{Style.RESET_ALL}")
    except ET.ParseError as e:
        print(f"{Fore.RED}Failed to parse MPD file: {e}{Style.RESET_ALL}")


resolution_map = {
    "256x144": "144p", "426x240": "240p", "426x252": "252p", "512x288": "288p",
    "640x360": "360p", "832x468": "468p", "854x480": "480p", "1024x576": "576p",
    "1280x720": "720p", "1920x1080": "1080p", "2560x1440": "1440p", "3840x2160": "2160p"
}

def download_movie(mpd_url, resolution, bandwidth, uuid, show_titles, release_year):
    """Download the movie with specified UUID and merge files if it doesn't already exist."""

    sanitized_title = f"{show_titles} - {release_year}".replace(":", " -").replace("?", "").replace("*", "").replace("<", "").replace(">", "").replace("|", "").replace("\\", "").replace("/", "")
    actual_resolution = resolution_map.get(resolution, resolution)
    download_path = os.path.join(download_dir, sanitized_title)
    output_file = os.path.join(download_path, f"{sanitized_title}.{actual_resolution}.OSN+.WEB-DL.H264.AAC.mkv")

    # Check if movie already exists
    # التحقق إذا كان الفيلم موجودًا
    if os.path.exists(output_file):
        print(f"The movie '{sanitized_title}' already exists at: {output_file}. Skipping download.")
        time.sleep(5)  # تأخير لمدة 5 ثوانٍ للسماح للمستخدم برؤية الرسالة
        return

    os.makedirs(cache_dir, exist_ok=True)
    os.makedirs(download_path, exist_ok=True)

    audio_option = f'--select-audio "lang=ar|en|tr:for=best3"'
    subtitle_option = f'--select-subtitle "lang=ar|en:for=best2"'

    download_name = f"{sanitized_title}.{actual_resolution}.OSN+.VIP.WEB-DL.H264.AAC"

    download_command = (
        f'"{n_m3u8dl_path}" "{mpd_url}" -mt '
        f'--select-video "id={uuid}" '
        f'{audio_option} '
        f'{subtitle_option} '
        f'--tmp-dir "{cache_dir}" '
        f'--save-dir "{cache_dir}" '
        f'--save-name "{download_name}" '
        f'--decryption-binary-path="{mp4decrypt_path}" '
        f'--key-text-file="{KEYS_PATH}" '
        f'--log-level "OFF"'
    )

    # Execute the download command
    subprocess.call(download_command, shell=True)

    # Collect downloaded files
    video_file = os.path.join(cache_dir, f"{download_name}.mp4")
    audio_files = [f for f in os.listdir(cache_dir) if f.endswith(".m4a")]
    subtitle_files = {
        "ar": next((f for f in os.listdir(cache_dir) if "ar" in f and f.endswith(".srt")), None),
        "en": next((f for f in os.listdir(cache_dir) if "en" in f and f.endswith(".srt")), None),
    }

    if os.path.exists(video_file) and audio_files:
        print("Downloaded files found, proceeding to merge...")

        audio_paths = [os.path.join(cache_dir, f) for f in audio_files]
        merge_movie_with_mkvmerge(video_file, audio_paths, output_file)

        # Los subtítulos ya están integrados en el archivo MKV, no es necesario moverlos como archivos separados
        print("Subtitles have been integrated into the MKV file")

        # Cleanup cache directory
        try:
            shutil.rmtree(cache_dir)
            print(f"Cache directory {cache_dir} has been deleted.")
        except Exception as e:
            print(f"An error occurred while deleting the cache directory: {e}")
    else:
        print("Download failed or required files not found. Nothing to merge.")

def merge_movie_with_mkvmerge(video_file, audio_files, output_file):
    """Merge video and audio files into MKV format using mkvmerge, including subtitles."""
    mkvmerge_command = [mkvmerge_path, "-o", output_file]

    # Add video file
    mkvmerge_command.append(video_file)

    # Add audio files with labels
    audio_map = {
        ".ar.m4a": ("ara", "Arabic"),
        ".tr.m4a": ("tur", "Turkish"),
        ".en-atmos.m4a": ("eng", "English Atmos"),
        ".en-ddp.m4a": ("eng", "English Dolby 5.1"),
        ".en-2CH.m4a": ("eng", "English Stereo"),
        ".en.m4a": ("eng", "English")  # Default if no specific label is found
    }

    for audio_file in audio_files:
        track_language = "und"
        track_label = "Unknown"
        for key, (language_code, label) in audio_map.items():
            if key in audio_file:
                track_language = language_code
                track_label = label
                break

        # Add the audio file to the mkvmerge command
        mkvmerge_command.extend([
            "--language", f"0:{track_language}",
            "--track-name", f"0:{track_label}",
            audio_file
        ])

    # Add subtitle files if they exist
    subtitle_files = {
        "ar": next((f for f in os.listdir(cache_dir) if "ar" in f and f.endswith(".srt")), None),
        "en": next((f for f in os.listdir(cache_dir) if "en" in f and f.endswith(".srt")), None),
    }

    subtitle_map = {
        "ar": ("ara", "Arabic"),
        "en": ("eng", "English")
    }

    for lang, subtitle_file in subtitle_files.items():
        if subtitle_file:
            language_code, label = subtitle_map.get(lang, ("und", "Unknown"))
            subtitle_path = os.path.join(cache_dir, subtitle_file)

            # Add the subtitle file to the mkvmerge command
            mkvmerge_command.extend([
                "--language", f"0:{language_code}",
                "--track-name", f"0:{label}",
                subtitle_path
            ])
            print(f"Adding {label} subtitle to the MKV file")

    # Print and run the command
    print("Running mkvmerge command:", " ".join(mkvmerge_command))
    subprocess.run(mkvmerge_command, check=True)

def get_pssh(mpd_url):
    """Function to extract PSSH from the MPD file."""
    r = requests.get(url=mpd_url)
    r.raise_for_status()
    xml = xmltodict.parse(r.text)
    tracks = xml['MPD']['Period']['AdaptationSet']
    for video_tracks in tracks:
        if video_tracks['@mimeType'] == 'video/mp4':
            for t in video_tracks['Representation'][0]['ContentProtection']:
                if t.get('@schemeIdUri', '').lower() == "urn:uuid:edef8ba9-79d6-4ace-a3c8-27dcd51d21ed":
                    pssh = t.get("cenc:pssh", {}).get('#text', '')
                    return pssh
    return None  # Return None if not found

def get_kid_mpd(mpd_url):
    """Function to extract KID from the MPD file."""
    r = requests.get(url=mpd_url)
    r.raise_for_status()
    xml = xmltodict.parse(r.text)
    tracks = xml['MPD']['Period']['AdaptationSet']
    for video_tracks in tracks:
        if video_tracks['@mimeType'] == 'video/mp4':
            for t in video_tracks['Representation'][0]['ContentProtection']:
                if t.get('@schemeIdUri', '').lower() == "urn:mpeg:dash:mp4protection:2011":
                    kid = t.get('@cenc:default_KID', '')
                    return kid
    return None  # Return None if not found

def divider():
    print("=" * 40)

def process_license(show_title, mpd_url, lic_url, pssh, kid, drmToken, args):
    """Function to extract and display DRM keys from license URL."""
    show_titles = show_title.replace(".", " ").strip()
    print("")
    print(f"{YELLOW}[+] TITLE:{RESET}{CYAN} {show_titles}{RESET}")
    print("")
    print(f"{YELLOW}[+] MPD:{RESET}{CYAN} {mpd_url}{RESET}")
    print(f"{YELLOW}\n[+] LICENSE URL:{RESET}{CYAN} {lic_url}{RESET}")
    print(f"{YELLOW}\n[+] PSSH:{RESET}{CYAN} {pssh}{RESET}")
    print(f"{YELLOW}\n[+] KID:{RESET}{CYAN}{kid}{RESET}")

    # إعداد الترويسة لطلب الترخيص
    headers_license = {
        'x-dt-custom-data': drmToken,
        'User-Agent': 'Dalvik/2.1.0 (Linux; Android 11; SHIELD Android TV Build/RQ1A.210105.003; wv)',
    }

    try:
        # تتبع: التأكد من أن PSSH موجودة
        if not pssh:
            print(f"{RED}Error: PSSH data is missing.{RESET}")
            return

        # تتبع: التأكد من أن URL الترخيص موجود
        if not lic_url:
            print(f"{RED}Error: License URL is missing.{RESET}")
            return

        # تتبع: عملية استخراج الشفرة باستخدام مكتبة WvDecrypt
        #print(f"{CYAN}Sending request to license server...{RESET}")
        wvdecrypt = WvDecrypt(pssh)
        raw_challenge = wvdecrypt.get_challenge()

        response = requests.post(url=lic_url, headers=headers_license, data=raw_challenge)

        # تتبع: التأكد من حالة الاستجابة
        if response.status_code != 200:
            print(f"{RED}Error: Failed to get license. Status code: {response.status_code}{RESET}")
            print(f"{RED}Response content: {response.content}{RESET}")
            return

        license_b64 = b64encode(response.content)
        wvdecrypt.update_license(license_b64)
        keys = wvdecrypt.start_process()

        # عرض المفاتيح إذا كانت متاحة
        if args.keys:
            print("")
            print(f"{GREEN}OBTAINED KEYS: ")
            for key in keys:
                print(f'{YELLOW}--key {key}{RESET}')
            return

        # حفظ المفاتيح في ملف
        print("")
        path = 'KEYS/'
        os.makedirs(path, exist_ok=True)
        save_filename = 'OSNPLUS_KEYS.txt'
        save_path = path + save_filename
        existing_keys = read_keys_file(save_path)
        new_keys = [key for key in keys if key not in existing_keys]

        if new_keys:
            print(f"{GREEN}OBTAINED KEYS: ")
            for key in keys:
                print(f'{YELLOW}--key {key}{RESET}')

            with open(save_path, 'a') as f:
                f.write('\n\n' + show_title)
                for key in new_keys:
                    f.write('\n' + key)

            print(f"{GREEN}\n[+] Success Saving KEYS{RESET}")
        else:
            print(f"{RED}[-] KEYS Already Saved!{RESET}")

    except Exception as e:
        print(f"{RED}An error occurred: {e}{RESET}")

#################################### SERIES ########################################
def extract_series_id(series_link):
    try:
        series_id = series_link.split("-")[-1]
        if series_id.isdigit():
            return series_id
        else:
            #print("لم يتم استخراج معرف مسلسل صالح.")
            return None
    except Exception as e:
        #print("خطأ في استخراج معرف المسلسل:", e)
        return None

# Display table in fancy grid format
def display_table(data, headers):
    if not data:
        print("لا توجد بيانات لعرضها في الجدول.")
        return

    # تلوين العناوين باللون الأصفر
    colored_headers = [f"{Fore.YELLOW}{header}{Style.RESET_ALL}" for header in headers]

    # طباعة الجدول باستخدام tabulate
    print(tabulate(data, headers=colored_headers, tablefmt="fancy_grid", colalign=("center",) * len(colored_headers)))

# دالة لجلب تفاصيل المسلسل واستخراج رابط البوستر (وتأخير تحميل البوستر حتى يبدأ التحميل)
def get_series_details(series_link, access_token):
    url = "https://koussa-osn.anghami.com/osn/media/v1/get-series-page"
    headers = {
        'accept': '*/*',
        'accept-language': 'en-US,en;q=0.9',
        'access_token': access_token,
        'client_platform': 'web-osn',
        'client_version': '1.1.1',
        'content-type': 'application/json',
        'device_unique_identifier': '35b4ffb9-654f-4fca-a132-8ed1011d1927',
        'language': 'en',
        'origin': 'https://osnplus.com',
        'referer': 'https://osnplus.com/',
    }

    # استخراج معرف المسلسل من الرابط
    series_id = extract_series_id(series_link)
    if not series_id:
        print("معرف المسلسل غير صالح. يرجى التحقق من الرابط.")
        return

    data = {'contentId': series_id}
    response = requests.post(url, headers=headers, json=data)

    if response.status_code == 200:
        data = response.json()
        #print("Full Response:", data)  # Print the full response for debugging

        if not data.get('notFound', True):
            series_data = data['series']
            series_title = series_data.get("title", {}).get("en", "العنوان غير متوفر")

            # استخراج رابط البوستر فقط، بدون تحميله الآن
            poster_url = series_data.get("images", {}).get("longImageWithTitleUrl")

            # استخراج تفاصيل المواسم والحلقات
            seasons = series_data.get("seasons", [])
            table_data = []
            season_ids = []
            for season in seasons:
                season_number = season.get("seasonNumber", "غير متوفر")
                episodes_count = season.get("episodesCount", "غير متوفر")
                season_id = season.get("contentId", "غير متوفر")
                season_year = season.get("year", "السنة غير متوفرة")

                # Extract IMDb rating if available
                imdb_rating = series_data.get("imdbRating", {}).get("rating", "N/A")
                table_data.append([f"{series_title} ({season_year})", season_number, episodes_count, season_id, imdb_rating])
                season_ids.append(season_id)

            #print("Series Details and Available Seasons:")
            display_table(table_data, headers=["Title & Year", "Season", "Episodes", "Season ID", "IMDb Rating"])

            while True:
                try:
                    selected_season = int(input("\nEnter The Season Number : ")) - 1
                    if 0 <= selected_season < len(season_ids):
                        season_id = season_ids[selected_season]

                        # تأجيل تحميل البوستر حتى يتم البدء في التحميل الفعلي
                        poster_path = download_poster_to_cache(poster_url) if poster_url else None

                        # تمرير المسار النهائي لمجلد المسلسل ومسار البوستر في الكاش
                        series_folder_path = os.path.join("download_path", f"{series_title} S{str(selected_season + 1).zfill(2)}")

                        # تمرير رقم الموسم المختار (selected_season + 1) كـ `season_number` للدالة التالية
                        get_episodes_by_season(season_id, access_token, series_title, selected_season + 1, series_folder_path, poster_url)
                        break
                    else:
                        print("Please enter a valid number.")
                except ValueError:
                    print("Please enter a valid number.")
        else:
            print("No data found for this series.")
    else:
        print("Failed to fetch series details. Status code:", response.status_code)

# تعريف المتغيرات العالمية
saved_stream_id = None
saved_resolution = None
saved_uuid = None
download_count = 0  # عداد تحميل الحلقات
MAX_EPISODES_BEFORE_REFRESH = 10  # 👈 عدد الحلقات قبل تحديث التوكن

def get_episodes_by_season(season_id, access_token, series_title, season_number, series_folder_path=None, poster_url=None):
    global saved_stream_id, saved_resolution, saved_uuid, saved_access_token, download_count

    url = "https://api.osnplus.com/osn/media/v1/get-episodes-by-season"

    headers = {
        'accept': '*/*',
        'access_token': saved_access_token if saved_access_token else access_token,  # استخدام أحدث توكن متوفر
        'client_platform': 'web-osn',
        'content-type': 'application/json',
    }

    payload = {"seasonContentIds": [season_id]}
    response = requests.post(url, headers=headers, json=payload)

    # 🔄 إعادة المحاولة إذا كان هناك خطأ 401 بسبب انتهاء صلاحية التوكن
    if response.status_code == 401:
        print("⚠️ Token expired while fetching episodes. Refreshing...")
        new_token = refresh_access_token()
        if new_token:
            headers['access_token'] = new_token  # تحديث التوكن الجديد
            response = requests.post(url, headers=headers, json=payload)  # إعادة المحاولة
        else:
            print("❌ Failed to refresh token. Cannot fetch episodes.")
            return

    if response.status_code == 200:
        data = response.json()
        try:
            season_data = data["episodesBySeason"].get(season_id, {})
            if season_data and "episodes" in season_data:
                table_data = []
                episode_titles = []

                # التأكد من رقم الموسم الصحيح من بيانات الموسم مباشرة
                actual_season_number = season_data.get("seasonNumber", season_number)

                for index, episode in enumerate(season_data["episodes"], start=1):
                    episode_title = episode["title"].get("en", "Title not found")
                    table_data.append([index, episode_title, actual_season_number])
                    episode_titles.append(episode_title)

                print(f"\n{Fore.GREEN}[+] Episodes List:")
                display_table(table_data, headers=["Number", "Title", "Season"])

                print(f"{Fore.CYAN}[+] Options:{Fore.RESET}")
                print(f"{Fore.WHITE}[1] Download all episodes{Fore.RESET}")
                print(f"{Fore.WHITE}[2] Download a range of episodes{Fore.RESET}")
                print(f"{Fore.WHITE}[3] Select a specific episode{Fore.RESET}")
                print()

                while True:
                    download_choice = input("Please select an option: ")

                    if download_choice == '1':
                        # تحميل جميع الحلقات
                        selected_episodes = season_data["episodes"]
                        break

                    elif download_choice == '2':
                        # تحميل نطاق من الحلقات
                        while True:
                            start_episode = input("Enter the start episode number: ")
                            end_episode = input("Enter the end episode number: ")

                            if start_episode.isdigit() and end_episode.isdigit() and 1 <= int(start_episode) <= len(season_data["episodes"]) and 1 <= int(end_episode) <= len(season_data["episodes"]) and int(start_episode) <= int(end_episode):
                                selected_episodes = season_data["episodes"][int(start_episode) - 1:int(end_episode)]
                                break
                            else:
                                print("Invalid range. Please enter valid episode numbers.")

                        break

                    elif download_choice == '3':
                        # تحميل حلقة معينة
                        while True:
                            episode_number = input(f"Enter the episode number (1 to {len(season_data['episodes'])}): ")

                            if episode_number.isdigit() and 1 <= int(episode_number) <= len(season_data["episodes"]):
                                selected_episodes = [season_data["episodes"][int(episode_number) - 1]]
                                break
                            else:
                                print(f"Invalid episode number. Please enter a number between 1 and {len(season_data['episodes'])}.")

                        break

                    else:
                        print("Invalid option. Please select a valid option (1, 2, or 3).")

                print(f"Selected episodes: {[ep['title'].get('en', 'No Title') for ep in selected_episodes]}")

                # بعد اختيار الحلقات، يتم تحميلها واحدة تلو الأخرى
                for selected_episode in selected_episodes:
                    episode_title = selected_episode["title"].get("en", "No Title")
                    episode_number = selected_episode["episodeNumber"]
                    if "streams" in selected_episode:
                        get_streams_table(
                            selected_episode["streams"],
                            selected_episode["contentId"],
                            headers['access_token'],  # تمرير التوكن المحدث
                            series_title,
                            episode_title,
                            actual_season_number,
                            episode_number,
                            poster_url
                        )

                        # 🔄 تحديث التوكن بعد عدد معين من الحلقات
                        download_count += 1
                        if download_count >= MAX_EPISODES_BEFORE_REFRESH:
                            print(f"🔄 Refreshing token after {MAX_EPISODES_BEFORE_REFRESH} episodes...")
                            refresh_access_token()
                            download_count = 0  # إعادة تعيين العداد

                    else:
                        print(f"No streams available for {episode_title}.")
            else:
                print("No episodes found.")
        except KeyError as e:
            print(f"Error extracting episode details: {e}")
    else:
        print("Failed to retrieve episode details. Status code:", response.status_code)
# المتغيرات لتخزين الاختيارات
selected_stream_id = None  # لتخزين Stream ID المختار
selected_stream_quality = None  # لتخزين الجودة الخاصة بالاستريم (مثل HD أو 4K)
has_shown_quality_table = False  # متغير لضمان إظهار جدول الجودة مرة واحدة فقط

# متغيرات لتخزين الحالة
has_shown_stream_table = False
saved_stream_id = None  # لتخزين Stream ID المختار مسبقًا

def get_streams_table(episode_streams, content_id, access_token, series_title, episode_title, season_number, episode_number, poster_url):
    """
    عرض قائمة الـ Streams لحلقة معينة، مع تحديث التوكن وإعادة المحاولة عند الحاجة.
    """
    global has_shown_stream_table, saved_stream_id, saved_access_token

    table_data = []

    # تجهيز بيانات الـ Streams
    for index, stream in enumerate(episode_streams, start=1):
        manifest_type = stream.get("manifestType", "N/A")

        # تجاهل "MANIFEST_TYPE_HLS"
        if (manifest_type == "MANIFEST_TYPE_HLS"):
            continue

        stream_id = stream.get("streamId", "N/A")
        resolution = stream.get("highestImageResolution", "N/A")
        hdr = "Yes" if stream.get("isHdr", False) else "No"

        table_data.append([index, stream_id, resolution, hdr, manifest_type])

    if not has_shown_stream_table:  # عرض الجدول للمستخدم فقط في المرة الأولى
        if table_data:
            print(f"\n{Fore.GREEN}[+] Available Streams for the Selected Episode:")
            display_table(table_data, headers=["Index", "Stream ID", "Resolution", "HDR", "Manifest Type"])

            while True:
                try:
                    selected_stream_index = int(input("\nPlease enter the stream index you want to use: "))

                    selected_stream = next((row for row in table_data if row[0] == selected_stream_index), None)

                    if selected_stream:
                        saved_stream_id = selected_stream[1]
                        print(f"[+] Selected Stream ID: {saved_stream_id}, Quality: {selected_stream[2]}")
                        has_shown_stream_table = True
                        break
                    else:
                        print("Invalid stream index. Please enter a number from the table.")
                except ValueError:
                    print("Invalid input. Please enter a valid number for the stream index.")
        else:
            print("No streams available.")
            return
    else:
        # اختيار أول Stream تلقائيًا للحلقات التالية
        if table_data:
            saved_stream_id = table_data[0][1]
            print(f"[+] Automatically using Stream ID: {saved_stream_id} for this episode")
        else:
            print("No streams available.")
            return

    # استدعاء دالة `get_stream_series` مع معالجة التوكن
    get_stream_series(content_id, saved_stream_id, saved_access_token, series_title, episode_title, season_number, episode_number, poster_url)

def get_stream_series(content_id, stream_id, access_token, series_title, episode_title, season_number, episode_number, poster_url):
    """Function to get stream details using contentId and streamId, including PSSH and KID extraction."""
    url = "https://api.osnplus.com/osn/media/v1/stream"
    headers = {
        'accept': '*/*',
        'accept-language': 'en-US,en;q=0.9',
        'access_token': access_token,
        'client_platform': 'web-osn',
        'client_version': '1.1.1',
        'content-type': 'application/json',
        'device_unique_identifier': '35b4ffb9-654f-4fca-a132-8ed1011d1927',
        'language': 'en',
        'origin': 'https://osnplus.com',
        'referer': 'https://osnplus.com/',
    }

    payload = {
        "contentId": content_id,
        "streamId": stream_id
    }

    response = requests.post(url, headers=headers, json=payload)

    if response.status_code == 200:
        data = response.json()

        # Check if drmLicenseServers is present and contains the necessary information
        if 'drmLicenseServers' in data and data['drmLicenseServers'] is not None:
            lic_url = data['drmLicenseServers'].get('widevineLicenseUrl')
            drmToken = data.get('drmToken')
            cdnToken = data.get('cdnToken')

            # Verify that lic_url, drmToken, and cdnToken are available
            if not lic_url or not drmToken or not cdnToken:
                print("Error: Missing license URL, DRM token, or CDN token in the stream details.")
                return

            # Construct MPD URL with the token
            mpd_url = data.get('manifestUrl')
            if mpd_url:
                mpd_url_with_token = f"{mpd_url}?token={cdnToken}"
            else:
                print("Error: MPD URL is missing.")
                return

            # Retrieve PSSH and KID
            pssh = get_pssh(mpd_url_with_token)
            kid = get_kid_mpd(mpd_url_with_token)

            # Call the function to process the license and extract DRM keys
            process_series_license(series_title, mpd_url_with_token, lic_url, pssh, kid, drmToken, episode_title, season_number, episode_number, poster_url)
        else:
            print("Error: DRM license server information is missing.")
    else:
        print("Failed to retrieve stream details. Status code:", response.status_code)
        print("Response:", response.text)

selected_mpd_quality = None  # المتغير لتخزين الجودة المختارة

def parse_mpd_series(mpd_url, series_title, season_number, episode_number, episode_title, poster_url):
    global selected_mpd_quality  # استخدام المتغير العالمي

    try:
        response = requests.get(mpd_url)
        response.raise_for_status()
        mpd_content = response.content

        root = ET.fromstring(mpd_content)
        namespaces = {'mpd': 'urn:mpeg:dash:schema:mpd:2011'}

        quality_data = []
        audio_languages = set()
        subtitle_languages = set()

        for adaptation_set in root.findall('.//mpd:Period/mpd:AdaptationSet', namespaces):
            mime_type = adaptation_set.get('mimeType', 'unknown')
            if mime_type == 'video/mp4':
                for representation in adaptation_set.findall('mpd:Representation', namespaces):
                    resolution = representation.get('height') + "p"
                    bandwidth = representation.get('bandwidth')
                    uuid = representation.get('id')
                    quality_data.append({
                        "No.": None,
                        "Resolution": resolution,
                        "Bandwidth": f"{int(bandwidth)} bps",
                        "UUID": uuid,
                        "Audios": "",
                        "Subtitles": ""
                    })
            elif mime_type == 'audio/mp4':
                audio_language = adaptation_set.get('lang', 'unknown')
                audio_languages.add(audio_language)
            elif mime_type == 'text/vtt':
                subtitle_language = adaptation_set.get('lang', 'unknown')
                subtitle_languages.add(subtitle_language)

        audio_languages_str = " | ".join(sorted(audio_languages))
        subtitle_languages_str = " | ".join(sorted(subtitle_languages))

        for row in quality_data:
            row["Audios"] = audio_languages_str
            row["Subtitles"] = subtitle_languages_str

        quality_data.sort(key=lambda x: (int(x["Resolution"].replace("p", "")), int(x["Bandwidth"].replace(" bps", ""))))

        for i, row in enumerate(quality_data, start=1):
            row["No."] = i

        if quality_data:
            # طباعة الجدول باستخدام tabulate
            table_output = tabulate([{k: v for k, v in row.items() if k != "UUID"} for row in quality_data], headers="keys", tablefmt="fancy_grid", colalign=("center", "center", "center", "center", "center"))

            # عرض الجدول واختيار الجودة
            print("\nAvailable Qualities:")
            print(table_output)

            if selected_mpd_quality is None:
                try:
                    selected_quality_index = int(input("\nPlease enter the number of the quality you want to download: ")) - 1
                    if 0 <= selected_quality_index < len(quality_data):
                        selected_row = quality_data[selected_quality_index]
                        selected_mpd_quality = selected_row["Resolution"]
                        print(f"[+] Selected Quality: {selected_mpd_quality}")

                        # استدعاء الدالة لتحميل الحلقة
                        download_series_episode(
                            mpd_url,
                            selected_row["Resolution"],
                            selected_row["Bandwidth"],
                            selected_row["UUID"],
                            series_title,
                            season_number,
                            episode_number,
                            poster_url
                        )
                    else:
                        print("Invalid selection.")
                except ValueError:
                    print("Invalid input.")
            else:
                # استخدام الجودة المختارة مسبقًا
                resolution = selected_mpd_quality
                bandwidth = next((row["Bandwidth"] for row in quality_data if row["Resolution"] == resolution), None)
                uuid = next((row["UUID"] for row in quality_data if row["Resolution"] == resolution), None)
                print(f"[+] Using previously selected quality: {selected_mpd_quality}")
                download_series_episode(mpd_url, resolution, bandwidth, uuid, series_title, season_number, episode_number, poster_url)
    except requests.exceptions.RequestException as e:
        print(f"Failed to fetch MPD file: {e}")
    except ET.ParseError as e:
        print(f"Failed to parse MPD file: {e}")

# Extract PSSH from MPD file
def get_pssh(mpd_url):
    response = requests.get(mpd_url)
    if (response.status_code == 200):
        root = ET.fromstring(response.content)
        for elem in root.iter():
            if "pssh" in elem.tag:
                return elem.text
    print("PSSH not found.")
    return None

# Extract KID from MPD file
def get_kid_mpd(mpd_url):
    r = requests.get(url=mpd_url)
    r.raise_for_status()
    xml = xmltodict.parse(r.text)
    tracks = xml['MPD']['Period']['AdaptationSet']
    for video_tracks in tracks:
        if video_tracks['@mimeType'] == 'video/mp4':
            for t in video_tracks['Representation'][0]['ContentProtection']:
                if t.get('@schemeIdUri', '').lower() == "urn:mpeg:dash:mp4protection:2011":
                    kid = t.get('@cenc:default_KID', '')
                    return kid
    return None

# Process DRM license for keys
def process_series_license(series_title, mpd_url, lic_url, pssh, kid, drmToken, episode_title, season_number, episode_number, poster_url):
    episode_title_formatted = f"{series_title.replace('.', ' ').strip()} S{str(season_number).zfill(2)} E{str(episode_number).zfill(2)}"
    print("")
    print(f"{YELLOW}[+] TITLE:{RESET}{CYAN} {episode_title_formatted}{RESET}")
    print("")
    print(f"{YELLOW}[+] MPD:{RESET}{CYAN} {mpd_url}{RESET}")
    print(f"{YELLOW}\n[+] LICENSE URL:{RESET}{CYAN} {lic_url}{RESET}")
    print(f"{YELLOW}\n[+] PSSH:{RESET}{CYAN} {pssh}{RESET}")
    print(f"{YELLOW}\n[+] KID:{RESET}{CYAN}{kid}{RESET}")

    # إعداد الترويسة لطلب الترخيص
    headers_license = {
        'x-dt-custom-data': drmToken,
        'User-Agent': 'Dalvik/2.1.0 (Linux; Android 11; SHIELD Android TV Build/RQ1A.210105.003; wv)',
    }

    try:
        # التحقق من وجود PSSH وURL الترخيص
        if not pssh:
            print(f"{RED}Error: PSSH data is missing.{RESET}")
            return
        if not lic_url:
            print(f"{RED}Error: License URL is missing.{RESET}")
            return

        # عملية استخراج الشفرة باستخدام مكتبة WvDecrypt
        wvdecrypt = WvDecrypt(pssh)
        raw_challenge = wvdecrypt.get_challenge()

        response = requests.post(url=lic_url, headers=headers_license, data=raw_challenge)

        if response.status_code != 200:
            print(f"{RED}Error: Failed to get license. Status code: {response.status_code}{RESET}")
            print(f"{RED}Response content: {response.content}{RESET}")
            return

        license_b64 = b64encode(response.content)
        wvdecrypt.update_license(license_b64)
        keys = wvdecrypt.start_process()

        # عرض المفاتيح
        print("")
        if args.keys:
            print("")
            print(f"{GREEN}OBTAINED KEYS: ")
            for key in keys:
                print(f'{YELLOW}--key {key}{RESET}')
            return

        # حفظ المفاتيح في ملف
        path = 'KEYS/'
        os.makedirs(path, exist_ok=True)
        save_filename = 'OSNPLUS_KEYS.txt'
        save_path = path + save_filename
        existing_keys = read_keys_file(save_path)
        new_keys = [key for key in keys if key not in existing_keys]

        if new_keys:
            print(f"{GREEN}OBTAINED KEYS: ")
            for key in keys:
                print(f'{YELLOW}--key {key}{RESET}')

            with open(save_path, 'a') as f:
                f.write('\n\n' + episode_title)
                for key in new_keys:
                    f.write('\n' + key)

            print(f"{GREEN}\n[+] Success Saving KEYS{RESET}")
        else:
            print(f"{RED}[-] KEYS Already Saved!{RESET}")

        #print(f"[+] Processing License for Episode: {episode_title}")
        parse_mpd_series(mpd_url, series_title, season_number, episode_number, episode_title,poster_url)
    except Exception as e:
        print(f"{RED}An error occurred: {e}{RESET}")

def sanitize_title(title):
    # إزالة أو استبدال الرموز غير الصالحة
    title = title.replace(":", " -").replace("?", "").replace("*", "").replace("<", "").replace(">", "").replace("|", "").replace("\\", "").replace("/", "")
    # استبدال أي مسافة متتالية أو غير ضرورية
    title = re.sub(r'\s+', ' ', title).strip()
    return title

def download_series_episode(mpd_url, resolution, bandwidth, uuid, series_title, season_number, episode_number, poster_url):
    global selected_quality  # استخدام المتغير العالمي لتخزين الجودة المختارة

    # تنظيف العنوان
    sanitized_series_title = sanitize_title(f"{series_title.replace('.', ' ')} S{str(season_number).zfill(2)}")
    sanitized_title = f"{sanitized_series_title}E{str(episode_number).zfill(2)}"

    # إذا لم تكن الجودة محددة، نستخدم الجودة المختارة مسبقًا
    actual_resolution = resolution if resolution else selected_quality

    # المسار النهائي للمجلد
    season_folder_path = os.path.join(download_dir, sanitized_series_title)
    season_folder_path = os.path.normpath(season_folder_path)  # ضمان أن المسار صحيح ومنظم
    os.makedirs(season_folder_path, exist_ok=True)

    # اسم التنزيل
    download_name = f"{sanitized_title}.{actual_resolution}.OSN+.WEB-DL.H264.AAC"
    output_file = os.path.join(season_folder_path, f"{download_name}.mkv")

    # التحقق إذا كان الملف موجودًا مسبقًا
    if os.path.exists(output_file):
        print(f"The episode '{sanitized_title}' already exists at: {output_file}. Skipping download.")
        return

    # تحميل البوستر
    cache_poster_path = download_poster_to_cache(poster_url)

    # الخيارات الخاصة بالصوت والترجمة
    audio_option = '--select-audio "lang=ar|en|tr:for=best3"'
    subtitle_option = '--select-subtitle "lang=ar|en:for=best2"'

    # بناء أمر التنزيل
    download_command = (
        f'"{n_m3u8dl_path}" "{mpd_url}" -mt '
        f'--select-video "id={uuid}" '
        f'{audio_option} '
        f'{subtitle_option} '
        f'--tmp-dir "{cache_dir}" '
        f'--save-dir "{cache_dir}" '
        f'--save-name "{download_name}" '
        f'--decryption-binary-path="{mp4decrypt_path}" '
        f'--key-text-file="{KEYS_PATH}" '
        f'--log-level "OFF"'
    )

    # تنفيذ أمر التنزيل
    subprocess.call(download_command, shell=True)

    # تحميل الفيديو والصوت والترجمة
    video_file = os.path.join(cache_dir, f"{download_name}.mp4")
    audio_files = [f for f in os.listdir(cache_dir) if f.endswith(".m4a")]
    subtitle_files = {
        "ar": next((f for f in os.listdir(cache_dir) if "ar" in f and f.endswith(".srt")), None),
        "en": next((f for f in os.listdir(cache_dir) if "en" in f and f.endswith(".srt")), None),
    }

    if os.path.exists(video_file) and audio_files:
        print("Downloaded files found, proceeding to merge...")

        audio_paths = [os.path.join(cache_dir, f) for f in audio_files]
        merge_episode_with_mkvmerge(video_file, audio_paths, output_file)

        # الترجمات تم دمجها بالفعل في ملف MKV، لا حاجة لنقلها كملفات منفصلة
        print("Subtitles have been integrated into the MKV file")


        move_poster_to_final_folder(season_folder_path)

        # حذف مجلد الكاش
        try:
            shutil.rmtree(cache_dir)
            print(f"Cache directory {cache_dir} has been deleted.")
        except Exception as e:
            print(f"An error occurred while deleting the cache directory: {e}")

    else:
        print("Download failed or required files not found. Nothing to merge.")


def merge_episode_with_mkvmerge(video_file, audio_files, output_file):
    """دمج ملفات الفيديو والصوت والترجمات إلى MKV باستخدام mkvmerge مع تسمية دقيقة للمسارات."""
    mkvmerge_command = [mkvmerge_path, "-o", output_file]

    # إضافة ملف الفيديو
    mkvmerge_command.append(video_file)

    # تعيين التسمية لكل مسار صوتي بناءً على اسم الملف
    audio_map = {
        ".ar.m4a": ("ara", "Arabic"),
        ".tr.m4a": ("tur", "Turkish"),
        ".en.m4a": ("eng", "English"),
        ".en-ddp.m4a": ("eng", "English Dolby 5.1")
    }

    for audio_file in audio_files:
        track_language = "und"
        track_label = "Unknown"
        for key, (language_code, label) in audio_map.items():
            if key in audio_file:
                track_language = language_code
                track_label = label
                break

        # إضافة ملف الصوت مع تعيين اللغة والتسمية
        mkvmerge_command.extend([
            "--language", f"0:{track_language}",
            "--track-name", f"0:{track_label}",
            audio_file
        ])

    # إضافة ملفات الترجمة إذا كانت موجودة
    subtitle_files = {
        "ar": next((f for f in os.listdir(cache_dir) if "ar" in f and f.endswith(".srt")), None),
        "en": next((f for f in os.listdir(cache_dir) if "en" in f and f.endswith(".srt")), None),
    }

    subtitle_map = {
        "ar": ("ara", "Arabic"),
        "en": ("eng", "English")
    }

    for lang, subtitle_file in subtitle_files.items():
        if subtitle_file:
            language_code, label = subtitle_map.get(lang, ("und", "Unknown"))
            subtitle_path = os.path.join(cache_dir, subtitle_file)

            # إضافة ملف الترجمة إلى أمر mkvmerge
            mkvmerge_command.extend([
                "--language", f"0:{language_code}",
                "--track-name", f"0:{label}",
                subtitle_path
            ])
            print(f"Adding {label} subtitle to the MKV file")

    # طباعة الأمر النهائي للتأكيد
    print("Running mkvmerge command:", " ".join(mkvmerge_command))

    # تنفيذ الأمر
    subprocess.run(mkvmerge_command, check=True)


def download_poster_to_cache(poster_url):
    try:
        os.makedirs(cache_dir, exist_ok=True)
        response = requests.get(poster_url, stream=True)
        response.raise_for_status()
        cache_poster_path = os.path.join(cache_dir, "poster.jpg")
        with open(cache_poster_path, "wb") as file:
            file.write(response.content)
        print(f"\nPoster downloaded and saved as {cache_poster_path}.")
        return cache_poster_path
    except requests.exceptions.RequestException as e:
        print("Failed to download poster:", e)
        return None

def move_poster_to_final_folder(series_folder_path):
    try:
        cache_poster_path = os.path.join(cache_dir, "poster.jpg")
        final_poster_path = os.path.join(series_folder_path, "poster.jpg")
        if os.path.exists(cache_poster_path):
            shutil.move(cache_poster_path, final_poster_path)
            print(f"Poster moved to {final_poster_path}.")
        else:
            print("Poster not found in cache to move.")
    except Exception as e:
        print("Error while moving poster:", e)

#################################### main_menu ########################################
def clear_screen():
    os.system('cls' if os.name == 'nt' else 'clear')

def print_banner():
    clear_screen()  # مسح الشاشة عند الدخول إلى القائمة
    banner = f"""
    {Fore.BLUE}╔════════════════════════════════════════════════════╗
    ║                                                    ║
    ║     {Fore.YELLOW}WELCOME TO OSN PLUS DOWNLOADER V1.0{Style.RESET_ALL}         ║
    ║     {Fore.GREEN}BY MOUSTAFA KAMEL © 2024{Style.RESET_ALL}                      ║
    ║                                                    ║
    ║     {Fore.GREEN}{Style.RESET_ALL}     ║
    ║                                                    ║
    ║     {Fore.GREEN}CONTACT: {Style.RESET_ALL}@MoustafaKamel95                   ║
    ║                                                    ║
    ╚════════════════════════════════════════════════════╝{Style.RESET_ALL}
    """
    print(banner)

# طباعة القائمة الرئيسية
def main_menu():

    menu = f"""
    {Fore.GREEN}[+] PLEASE CHOOSE AN OPTION:{Style.RESET_ALL}

    {Fore.YELLOW}1.{Style.RESET_ALL} {Fore.CYAN}DOWNLOAD MOVIE{Style.RESET_ALL}
    {Fore.YELLOW}2.{Style.RESET_ALL} {Fore.CYAN}DOWNLOAD SERIES{Style.RESET_ALL}
    {Fore.YELLOW}0.{Style.RESET_ALL} {Fore.CYAN}EXIT{Style.RESET_ALL}
    """
    print(menu)

    while True:
        choice = input(f"{Fore.WHITE}[+] Please select an option (1, 2, or 0 to exit): ")

        if choice == "1":
            access_token = refresh_access_token()
            while True:
                print()
                movie_link = input(f"{Fore.WHITE}[+] Please Enter The Movie Link (or type 'back' to return): ")

                if movie_link.lower() == 'back':
                    break
                access_token = refresh_access_token()
                if access_token:
                    get_movie_details(movie_link, access_token)
                    break  # العودة إلى القائمة الرئيسية بعد التنزيل

        elif choice == "2":
            access_token = refresh_access_token()
            while True:
                print()
                series_link = input(f"{Fore.WHITE}[+] Please Enter The Series Link (or type 'back' to return): ")
                if series_link.lower() == 'back':
                    break
                if access_token:
                    print()
                    series_id = extract_series_id(series_link)
                    if series_id:
                        get_series_details(series_link, access_token)
                        break  # العودة إلى القائمة الرئيسية بعد التنزيل
                    else:
                        print(f"{Fore.WHITE}Invalid series link. Please enter a valid series link.{Style.RESET_ALL}")

        elif choice == "0":
            print("Exiting the program. Goodbye!")
            exit(0)
        else:
            print(f"{Fore.RED}Invalid option. Please enter a valid option.{Style.RESET_ALL}")

# تشغيل البرنامج
def main():
    clear_screen()  # مسح الشاشة
    print_banner()  # طباعة العنوان العلوي
    main_menu()     # تشغيل القائمة الرئيسية
# تشغيل التحديث التلقائي في Thread منفصل
if __name__ == "__main__":
    main()
