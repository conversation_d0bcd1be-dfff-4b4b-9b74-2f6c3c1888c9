Successfully loaded sidebar logo from: images/images/shahid_logo.png
✅ Connected search_button
✅ Found url_input
✅ Connected recent_combo
✅ Connected clear_button
✅ UI connections setup completed
📂 Loaded 3 recent URLs from C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\modules\..\config\recent_urls.json
✅ Using existing content_tabs from UI
🔄 Cleared existing tabs
✅ All tabs setup completed (hidden until search)
C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\main.py:317: DeprecationWarning: 'exec_' will be removed in the future. Use 'exec' instead.
  sys.exit(app.exec_())
🔍 Selected by index 0: 53962 - The Wild Robot (2024)
✅ Selected from recent: 53962
🧹 Content data cleared and UI reset
🔍 Input is ID: 53962
🔍 Searching for unknown with ID: 53962
🔄 ID-only input, checking content type for ID: 53962
🔍 Checking content type for ID: 53962
🔍 Quick check for content type of ID: 53962
✅ ID 53962 is a MOVIE
✅ Confirmed as movie, searching...
🎬 Searching for movie with ID: 53962
🧹 Content data cleared and UI reset
🔍 Movie search - ID only: True, Silent fallback: True

====================================================================================================
📤 SENDING REQUEST TO get-watch-content (MOVIE):
====================================================================================================
URL: https://api.osnplus.com/osn/media/v1/get-watch-content
Headers: {'accept': '*/*', 'accept-language': 'en-US,en;q=0.9', 'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJhbmdoYW1pIiwic3ViIjoiMTAzMDI2MDY5IiwiZXhwIjoxNzQ5NDMxMjgxLCJuYmYiOjE3NDk0Mjc2ODEsImlhdCI6MTc0OTQyNzY4MSwianRpIjoiM2ZjZjMxNTItNDExYS00Mjk5LWE1YjctNzhlNmMzM2JiODkyIiwidXNlcl9pZCI6IjEwMzAyNjA2OSIsImRldmljZV91bmlxdWVfaWRlbnRpZmllciI6IiIsInRva2VuX3R5cGUiOiJhY2Nlc3NfdG9rZW4iLCJwbGFuX2lkcyI6WyIxOTYxIl0sInNvdXJjZSI6Im9zbisifQ.TuiYrcRMOZBAywcKOqjFHZXH7BhsmDfdymKC7w7Aa3Q', 'client_platform': 'web-osn', 'client_version': '1.1.1', 'content-type': 'application/json', 'device_unique_identifier': '35b4ffb9-654f-4fca-a132-8ed1011d1927', 'language': 'en', 'origin': 'https://osnplus.com', 'referer': 'https://osnplus.com/'}
Payload: {'contentIds': ['53962']}
====================================================================================================

====================================================================================================
🔍 FULL API RESPONSE FROM get-watch-content (MOVIE):
====================================================================================================
{
  "watchContents": {
    "53962": {
      "movie": {
        "movie": {
          "contentId": "53962",
          "title": {
            "ar": "ذا وايلد روبوت",
            "en": "The Wild Robot"
          },
          "description": {
            "ar": "بعد أن تحطمت سفينتها على جزيرة مهجورة، يجب على الروبوت روز أن تتعلم التكيّف مع بيئتها الجديدة.",
            "en": "Shipwrecked on a deserted island, a robot named Roz must learn to adapt to its new surroundings."
          },
          "year": "2024",
          "ageRating": "AGE_RATING_FAMILY",
          "isDownloadable": true,
          "isExclusive": true,
          "studio": "Universal Studios Limited",
          "images": {
            "logoImageUrl": "https://osn-artwork.anghcdn.co/logo_tt_PR670964/MV017138_TT.png",
            "longImageWithTitleUrl": "https://osn-artwork.anghcdn.co/portrait_tt_PR670964/MV017138_PTT.jpg",
            "wideImageWithTitleUrl": "https://osn-artwork.anghcdn.co/landscape_tt_PR670964/MV017138_LTT.jpg",
            "wideImageWithoutTitleUrl": "https://osn-artwork.anghcdn.co/landscape_cl_PR670964/MV017138_LC062119387.jpg",
            "wideImageWithoutTitleResizedUrl": ""
          },
          "genres": [
            {
              "genreId": "5",
              "name": {
                "ar": "رسوم متحركة",
                "en": "Animation"
              },
              "genrePageId": "animation-1"
            },
            {
              "genreId": "36",
              "name": {
                "ar": "خيال علمي",
                "en": "Sci-Fi"
              },
              "genrePageId": "fantasy-sci-fi"
            }
          ],
          "credits": {
            "actors": [
              {
                "crewId": "6512",
                "role": "ROLE_ACTOR",
                "firstName": {
                  "ar": "لوبيتا",
                  "en": "Lupita"
                },
                "lastName": {
                  "ar": "نيونغو",
                  "en": "Nyong'o"
                },
                "fullName": {
                  "ar": "لوبيتا نيونغو",
                  "en": "Lupita Nyong'o"
                },
                "sortableName": {
                  "ar": "نيونغو,لوبيتا",
                  "en": "Nyong'o,Lupita"
                },
                "imageUrl": "https://osn-artwork.anghcdn.co/crew_profile_6512/crew_artwork_y40Wu1T742kynOqtwXASc5Qgm49.jpg"
              },
              {
                "crewId": "996",
                "role": "ROLE_ACTOR",
                "firstName": {
                  "ar": "بيدرو",
                  "en": "Pedro"
                },
                "lastName": {
                  "ar": "باسكال",
                  "en": "Pascal"
                },
                "fullName": {
                  "ar": "بيدرو باسكال",
                  "en": "Pedro Pascal"
                },
                "sortableName": {
                  "ar": "باسكال,بيدرو",
                  "en": "Pascal,Pedro"
                },
                "imageUrl": "https://osn-artwork.anghcdn.co/crew_profile_996/crew_artwork_9VYK7oxcqhjd5LAH6ZFJ3XzOlID.jpg"
              },
              {
                "crewId": "16229",
                "role": "ROLE_ACTOR",
                "firstName": {
                  "ar": "كيت",
                  "en": "Kit"
                },
                "lastName": {
                  "ar": "كونور",
                  "en": "Connor"
                },
                "fullName": {
                  "ar": "كيت كونور",
                  "en": "Kit Connor"
                },
                "sortableName": {
                  "ar": "كونور,كيت",
                  "en": "Connor,Kit"
                },
                "imageUrl": "https://osn-artwork.anghcdn.co/crew_profile_16229/crew_artwork_6BDubD35VfSa0uyPr0UuYznuiM4.jpg"
              },
              {
                "crewId": "9135",
                "role": "ROLE_ACTOR",
                "firstName": {
                  "ar": "بيل",
                  "en": "Bill"
                },
                "lastName": {
                  "ar": "نيجي",
                  "en": "Nighy"
                },
                "fullName": {
                  "ar": "بيل نيجي",
                  "en": "Bill Nighy"
                },
                "sortableName": {
                  "ar": "نيجي,بيل",
                  "en": "Nighy,Bill"
                },
                "imageUrl": "https://osn-artwork.anghcdn.co/crew_profile_9135/crew_artwork_acbigDOU1L1vMWAL3Wf0r8h8qLA.jpg"
              },
              {
                "crewId": "8329",
                "role": "ROLE_ACTOR",
                "firstName": {
                  "ar": "ستيفاني",
                  "en": "Stephanie"
                },
                "lastName": {
                  "ar": "شو",
                  "en": "Hsu"
                },
                "fullName": {
                  "ar": "ستيفاني شو",
                  "en": "Stephanie Hsu"
                },
                "sortableName": {
                  "ar": "شو,ستيفاني",
                  "en": "Hsu,Stephanie"
                },
                "imageUrl": "https://osn-artwork.anghcdn.co/crew_profile_8329/crew_artwork_8gb3lfIHKQAGOQyeC4ynQPsCiHr.jpg"
              },
              {
                "crewId": "13782",
                "role": "ROLE_ACTOR",
                "firstName": {
                  "ar": "مات",
                  "en": "Matt"
                },
                "lastName": {
                  "ar": "بيري",
                  "en": "Berry"
                },
                "fullName": {
                  "ar": "مات بيري",
                  "en": "Matt Berry"
                },
                "sortableName": {
                  "ar": "بيري,مات",
                  "en": "Berry,Matt"
                },
                "imageUrl": "https://osn-artwork.anghcdn.co/crew_profile_13782/crew_artwork_7a1sWg1W7ZmNF8bLSnyAlJgQQGD.jpg"
              },
              {
                "crewId": "8185",
                "role": "ROLE_ACTOR",
                "firstName": {
                  "ar": "فينج",
                  "en": "Ving"
                },
                "lastName": {
                  "ar": "راميز",
                  "en": "Rhames"
                },
                "fullName": {
                  "ar": "فينج راميز",
                  "en": "Ving Rhames"
                },
                "sortableName": {
                  "ar": "راميز,فينج",
                  "en": "Rhames,Ving"
                },
                "imageUrl": "https://osn-artwork.anghcdn.co/crew_profile_8185/crew_artwork_4gpLVNKPZlVucc4fT2fSZ7DksTK.jpg"
              },
              {
                "crewId": "4904",
                "role": "ROLE_ACTOR",
                "firstName": {
                  "ar": "مارك",
                  "en": "Mark"
                },
                "lastName": {
                  "ar": "هاميل",
                  "en": "Hamill"
                },
                "fullName": {
                  "ar": "مارك هاميل",
                  "en": "Mark Hamill"
                },
                "sortableName": {
                  "ar": "هاميل,مارك",
                  "en": "Hamill,Mark"
                },
                "imageUrl": "https://osn-artwork.anghcdn.co/crew_profile_4904/crew_artwork_2ZulC2Ccq1yv3pemusks6Zlfy2s.jpg"
              },
              {
                "crewId": "891",
                "role": "ROLE_ACTOR",
                "firstName": {
                  "ar": "كاثرين",
                  "en": "Catherine"
                },
                "lastName": {
                  "ar": "أوهارا",
                  "en": "O'Hara"
                },
                "fullName": {
                  "ar": "كاثرين أوهارا",
                  "en": "Catherine O'Hara"
                },
                "sortableName": {
                  "ar": "أوهارا,كاثرين",
                  "en": "O'Hara,Catherine"
                },
                "imageUrl": "https://osn-artwork.anghcdn.co/crew_profile_891/crew_artwork_gI2RyymLJ9ZrhEyJSD5EqSvFpCX.jpg"
              }
            ],
            "directors": [
              {
                "crewId": "6708",
                "role": "ROLE_DIRECTOR",
                "firstName": {
                  "ar": "كريس",
                  "en": "Chris"
                },
                "lastName": {
                  "ar": "ساندرز",
                  "en": "Sanders"
                },
                "fullName": {
                  "ar": "كريس ساندرز",
                  "en": "Chris Sanders"
                },
                "sortableName": {
                  "ar": "ساندرز,كريس",
                  "en": "Sanders,Chris"
                },
                "imageUrl": "https://osn-artwork.anghcdn.co/crew_profile_6708/crew_artwork_6CtrIOCxggJ5eIAWeFQqd4Hs9FP.jpg"
              }
            ],
            "creators": []
          },
          "runTimeMs": 6099093,
          "hasHdr": true,
          "audioTypes": [
            "AUDIO_TYPE_STEREO",
            "AUDIO_TYPE_DOLBY_DIGITAL",
            "AUDIO_TYPE_DOLBY_ATMOS",
            "AUDIO_TYPE_UNSPECIFIED"
          ],
          "highestImageResolution": "IMAGE_RESOLUTION_4K",
          "autoPreviews": [
            {
              "contentId": "57149",
              "streams": [
                {
                  "streamId": "127431",
                  "runTimeMs": 93000,
                  "audioTracks": [
                    {
                      "language": "en",
                      "audioType": "AUDIO_TYPE_STEREO"
                    }
                  ],
                  "subtitlesLanguages": [],
                  "highestImageResolution": "IMAGE_RESOLUTION_HD",
                  "isHdr": false,
                  "manifestType": "MANIFEST_TYPE_DASH",
                  "isDolbyVision": false
                },
                {
                  "streamId": "127432",
                  "runTimeMs": 93000,
                  "audioTracks": [
                    {
                      "language": "en",
                      "audioType": "AUDIO_TYPE_STEREO"
                    }
                  ],
                  "subtitlesLanguages": [],
                  "highestImageResolution": "IMAGE_RESOLUTION_HD",
                  "isHdr": false,
                  "manifestType": "MANIFEST_TYPE_HLS",
                  "isDolbyVision": false
                }
              ]
            }
          ],
          "trailers": [
            {
              "contentId": "53963",
              "title": {
                "ar": "المقطع الترويجي · ذا وايلد روبوت",
                "en": "Trailer · The Wild Robot"
              },
              "streams": [
                {
                  "streamId": "121137",
                  "runTimeMs": 170000,
                  "audioTracks": [
                    {
                      "language": "en",
                      "audioType": "AUDIO_TYPE_STEREO"
                    },
                    {
                      "language": "en",
                      "audioType": "AUDIO_TYPE_DOLBY_DIGITAL"
                    }
                  ],
                  "subtitlesLanguages": [],
                  "highestImageResolution": "IMAGE_RESOLUTION_HD",
                  "isHdr": false,
                  "manifestType": "MANIFEST_TYPE_DASH",
                  "isDolbyVision": false
                },
                {
                  "streamId": "121138",
                  "runTimeMs": 170000,
                  "audioTracks": [
                    {
                      "language": "en",
                      "audioType": "AUDIO_TYPE_STEREO"
                    },
                    {
                      "language": "en",
                      "audioType": "AUDIO_TYPE_DOLBY_DIGITAL"
                    }
                  ],
                  "subtitlesLanguages": [],
                  "highestImageResolution": "IMAGE_RESOLUTION_HD",
                  "isHdr": false,
                  "manifestType": "MANIFEST_TYPE_HLS",
                  "isDolbyVision": false
                }
              ],
              "wideImageWithoutTitleUrl": "https://osn-artwork.anghcdn.co/landscape_cl_PR670964/MV017138_LC062119387.jpg"
            }
          ],
          "streams": [
            {
              "streamId": "121141",
              "runTimeMs": 5848000,
              "audioTracks": [
                {
                  "language": "en",
                  "audioType": "AUDIO_TYPE_STEREO"
                },
                {
                  "language": "en",
                  "audioType": "AUDIO_TYPE_DOLBY_DIGITAL"
                },
                {
                  "language": "fr",
                  "audioType": "AUDIO_TYPE_STEREO"
                },
                {
                  "language": "en",
                  "audioType": "AUDIO_TYPE_UNSPECIFIED"
                }
              ],
              "subtitlesLanguages": [
                "en",
                "ar"
              ],
              "highestImageResolution": "IMAGE_RESOLUTION_HD",
              "isHdr": false,
              "manifestType": "MANIFEST_TYPE_DASH",
              "isDolbyVision": false
            },
            {
              "streamId": "121142",
              "runTimeMs": 5848000,
              "audioTracks": [
                {
                  "language": "en",
                  "audioType": "AUDIO_TYPE_STEREO"
                },
                {
                  "language": "en",
                  "audioType": "AUDIO_TYPE_DOLBY_DIGITAL"
                },
                {
                  "language": "fr",
                  "audioType": "AUDIO_TYPE_STEREO"
                },
                {
                  "language": "en",
                  "audioType": "AUDIO_TYPE_UNSPECIFIED"
                }
              ],
              "subtitlesLanguages": [
                "en",
                "ar"
              ],
              "highestImageResolution": "IMAGE_RESOLUTION_HD",
              "isHdr": false,
              "manifestType": "MANIFEST_TYPE_HLS",
              "isDolbyVision": false
            },
            {
              "streamId": "124099",
              "runTimeMs": 6099093,
              "audioTracks": [
                {
                  "language": "en",
                  "audioType": "AUDIO_TYPE_STEREO"
                },
                {
                  "language": "en",
                  "audioType": "AUDIO_TYPE_DOLBY_DIGITAL"
                },
                {
                  "language": "fr",
                  "audioType": "AUDIO_TYPE_STEREO"
                },
                {
                  "language": "en",
                  "audioType": "AUDIO_TYPE_UNSPECIFIED"
                },
                {
                  "language": "en",
                  "audioType": "AUDIO_TYPE_DOLBY_ATMOS"
                }
              ],
              "subtitlesLanguages": [
                "en",
                "ar"
              ],
              "highestImageResolution": "IMAGE_RESOLUTION_4K",
              "isHdr": true,
              "manifestType": "MANIFEST_TYPE_DASH",
              "isDolbyVision": false
            },
            {
              "streamId": "124100",
              "runTimeMs": 6099093,
              "audioTracks": [
                {
                  "language": "en",
                  "audioType": "AUDIO_TYPE_STEREO"
                },
                {
                  "language": "en",
                  "audioType": "AUDIO_TYPE_DOLBY_DIGITAL"
                },
                {
                  "language": "fr",
                  "audioType": "AUDIO_TYPE_STEREO"
                },
                {
                  "language": "en",
                  "audioType": "AUDIO_TYPE_UNSPECIFIED"
                },
                {
                  "language": "en",
                  "audioType": "AUDIO_TYPE_DOLBY_ATMOS"
                }
              ],
              "subtitlesLanguages": [
                "en",
                "ar"
              ],
              "highestImageResolution": "IMAGE_RESOLUTION_4K",
              "isHdr": true,
              "manifestType": "MANIFEST_TYPE_HLS",
              "isDolbyVision": false
            }
          ],
          "maxQuality": "IMAGE_RESOLUTION_4K",
          "uriId": "PR670964",
          "freeToStreamText": "",
          "isFreeToStream": false,
          "hasDolbyVision": false,
          "imdbRating": {
            "hasRating": false,
            "rating": 0
          }
        }
      }
    }
  }
}
====================================================================================================
✅ Movie found with 4 streams
✅ Movie found successfully!
🔄 Loading poster from: https://osn-artwork.anghcdn.co/portrait_tt_PR670964/MV017138_PTT.jpg
✅ Poster loaded successfully
✅ Content tabs are now visible
✅ Added movie 'The Wild Robot' to seasons list
✅ Added to recent: 53962 - The Wild Robot (2024)
🔄 Loading poster from: https://osn-artwork.anghcdn.co/portrait_tt_PR670964/MV017138_PTT.jpg
✅ Poster loaded successfully
✅ Content tabs are now visible
✅ Added movie 'The Wild Robot' to seasons list
✅ Added to recent: 53962 - The Wild Robot (2024)

🎬 Loading movie streams...
🎬 Displaying 4 streams for movie: The Wild Robot
🧹 Streams container cleared
🎯 Keeping stream: IMAGE_RESOLUTION_4K HDR:True DV:False Type:MANIFEST_TYPE_DASH
⏭️ Skipping duplicate: IMAGE_RESOLUTION_4K HDR:True DV:False Type:MANIFEST_TYPE_HLS
🎯 Keeping stream: IMAGE_RESOLUTION_HD HDR:False DV:False Type:MANIFEST_TYPE_DASH
⏭️ Skipping duplicate: IMAGE_RESOLUTION_HD HDR:False DV:False Type:MANIFEST_TYPE_HLS
🔧 Filtered from 4 to 2 unique streams
Unknown property box-shadow
Unknown property box-shadow
Unknown property box-shadow
Unknown property box-shadow
Unknown property box-shadow
Unknown property box-shadow
Unknown property box-shadow
Unknown property box-shadow
Unknown property box-shadow
Unknown property box-shadow
Unknown property box-shadow
Unknown property box-shadow
Unknown property box-shadow
Unknown property box-shadow
Unknown property box-shadow
Unknown property box-shadow
Unknown property box-shadow
Unknown property box-shadow
Unknown property box-shadow
Unknown property box-shadow
✅ Displayed 2 stream cards for movie
Unknown property box-shadow
Unknown property box-shadow
Unknown property box-shadow
Unknown property box-shadow
Unknown property box-shadow
Unknown property box-shadow
Unknown property box-shadow
Unknown property box-shadow
✅ Switched to Available Streams tab for movie
Unknown property box-shadow
✅ Selected Stream: ID=121141, Quality=IMAGE_RESOLUTION_HD
🔄 Fetching detailed stream information...
📡 Fetching stream details for Movie - Content ID: 53962, Stream ID: 121141
🔄 STREAM DETAILS REQUEST:
🔄 URL: https://api.osnplus.com/osn/media/v1/stream
🔄 Payload: {'contentId': '53962', 'streamId': '121141'}
📋 FULL STREAM DETAILS RESPONSE:
📋 Status Code: 200
📋 Response Keys: ['contentId', 'streamId', 'drmToken', 'cdnToken', 'manifestUrl', 'drmLicenseServers', 'startCreditsInMs', 'startCreditsOutMs', 'recapInMs', 'recapOutMs', 'endCreditsInMs', 'endCreditsOutMs', 'hasStartCredits', 'hasRecap', 'hasEndCredits', 'maxConcurrentStreamsReached', 'thumbnailSpriteVttUrl', 'thumbnailSpriteImagesUrls', 'error', 'headerCommunicationButton', 'maxStreamingDevicesCommunicationButton', 'bandwidthUpgradeTarget', 'bandwidthDowngradeTarget', 'bufferingGoal', 'switchInterval', 'useNetworkInformation', 'preroll', 'hasAds', 'streamError', 'primaryCdnStream', 'fallbackCdnStream']
📋 contentId: 53962
📋 streamId: 121141
📋 drmToken: eyJ1c2VySWQiOiIxMDMwMjYwNjkiLCJtZXJjaGFudCI6ImFuZ2hhbWkiLCJzZXNzaW9uSWQiOiJ7XCJjb250ZW50X2lkXCI6XCI1Mzk2MlwiLFwiand0X3Rva2VuXCI6XCJleUpoYkdjaU9pSklVekkxTmlJc0luUjVjQ0k2SWtwWFZDSjkuZXlKcGMzTWlPaUpoYm1kb1lXMXBJaXdpYzNWaUlqb2lNVEF6TURJMk1EWTVJaXdpWlhod0lqb3hOelE1TmpBd05EZzVMQ0p1WW1ZaU9qRTNORGswTWpjMk9Ea3NJbWxoZENJNk1UYzBPVFF5TnpZNE9Td2lhblJwSWpvaU5qWTRaamxqTkRVdFpHTmpaQzAwTXpCakxXSmtNREl0TWpBME5XTTRObVF5WVRFeElpd2lkWE5sY2w5cFpDSTZJakV3TXpBeU5qQTJPU0lzSW1SbGRtbGpaVjlwWkNJNklqTTFZalJtWm1JNUxUWTFOR1l0TkdaallTMWhNVE15TFRobFpERXdNVEZrTVRreU55SXNJbVJsZG1salpWOXBibVp2SWpvaUlpd2lhWE5mWkc5M2JteHZZV1JmY21WeGRXVnpkQ0k2Wm1Gc2MyVXNJbU52Ym5SbGJuUmZhV1FpT2lJMU16azJNaUlzSW1OdmJuUmxiblJmY0dGeVpXNTBZV3hmY21GMGFXNW5Jam9pSWl3aWMzUnlaV0Z0WDJsa0lqb2lNVEl4TVRReElpd2lkSFpmWkdWMmFXTmxYMjF2WkdWc0lqb2lJbjAuRElzdnFtX0ZYTzl5ZUlhcVgwMEMweG5OcGl4UU1INkZyZXNBNzlsTzVMNFwifSJ9
📋 cdnToken: ************************************************************************************************************************************************************************************************************************************************
📋 manifestUrl: https://osn-video.anghcdn.co/public/139352-53962-PR670964-BX-MV017138-276047-c03e9f0767ec0f61ec4c4bb5329f107b-**********/stream.mpd
📋 drmLicenseServers: [Large object - dict with 4 items]
📋   Dict keys: ['widevineLicenseUrl', 'playreadyLicenseUrl', 'fairplayLicenseUrl', 'fairplayCertificateUrl']
📋 startCreditsInMs: 0
📋 startCreditsOutMs: 0
📋 recapInMs: 0
📋 recapOutMs: 0
📋 endCreditsInMs: 5365000
📋 endCreditsOutMs: 5848000
📋 hasStartCredits: False
📋 hasRecap: False
📋 hasEndCredits: True
📋 maxConcurrentStreamsReached: False
📋 thumbnailSpriteVttUrl: https://osn-video.anghcdn.co/public/139352-53962-PR670964-BX-MV017138-276047-c03e9f0767ec0f61ec4c4bb5329f107b-**********/sprite/sprite.vtt
📋 thumbnailSpriteImagesUrls: []
📋 error: None
📋 headerCommunicationButton: {'id': '3', 'text': 'Watch in 4K UHD', 'textColor': '#FFFFFF', 'backgroundColor': '#FF00D4', 'deeplinkUrl': 'https://osnplus.com/en-ae/manage-subscriptions', 'isSet': True, 'imageUrl': ''}
📋 maxStreamingDevicesCommunicationButton: None
📋 bandwidthUpgradeTarget: 0.85
📋 bandwidthDowngradeTarget: 0.95
📋 bufferingGoal: 30
📋 switchInterval: 5
📋 useNetworkInformation: False
📋 preroll: [Large object - dict with 7 items]
📋   Dict keys: ['contentId', 'hasPreroll', 'hlsManifestUrl', 'dashManifestUrl', 'mp4Url', 'cdnToken', 'prerollReferencedContent']
📋 hasAds: False
📋 streamError: None
📋 primaryCdnStream: [Large object - dict with 2 items]
📋   Dict keys: ['manifestUrl', 'thumbnailSpriteUrl']
📋 fallbackCdnStream: [Large object - dict with 2 items]
📋   Dict keys: ['manifestUrl', 'thumbnailSpriteUrl']
✅ Stream details fetched successfully!
📺 MPD URL: https://osn-video.anghcdn.co/public/139352-53962-PR670964-BX-MV017138-276047-c03e9f0767ec0f61ec4c4bb5329f107b-**********/stream.mpd?token=************************************************************************************************************************************************************************************************************************************************
🔐 License URL: https://lic.drmtoday.com/license-proxy-widevine/cenc/?specConform=true
🎫 DRM Token: eyJ1c2VySWQiOiIxMDMwMjYwNjkiLCJtZXJjaGFudCI6ImFuZ2hhbWkiLCJzZXNzaW9uSWQiOiJ7XCJjb250ZW50X2lkXCI6XCI1Mzk2MlwiLFwiand0X3Rva2VuXCI6XCJleUpoYkdjaU9pSklVekkxTmlJc0luUjVjQ0k2SWtwWFZDSjkuZXlKcGMzTWlPaUpoYm1kb1lXMXBJaXdpYzNWaUlqb2lNVEF6TURJMk1EWTVJaXdpWlhod0lqb3hOelE1TmpBd05EZzVMQ0p1WW1ZaU9qRTNORGswTWpjMk9Ea3NJbWxoZENJNk1UYzBPVFF5TnpZNE9Td2lhblJwSWpvaU5qWTRaamxqTkRVdFpHTmpaQzAwTXpCakxXSmtNREl0TWpBME5XTTRObVF5WVRFeElpd2lkWE5sY2w5cFpDSTZJakV3TXpBeU5qQTJPU0lzSW1SbGRtbGpaVjlwWkNJNklqTTFZalJtWm1JNUxUWTFOR1l0TkdaallTMWhNVE15TFRobFpERXdNVEZrTVRreU55SXNJbVJsZG1salpWOXBibVp2SWpvaUlpd2lhWE5mWkc5M2JteHZZV1JmY21WeGRXVnpkQ0k2Wm1Gc2MyVXNJbU52Ym5SbGJuUmZhV1FpT2lJMU16azJNaUlzSW1OdmJuUmxiblJmY0dGeVpXNTBZV3hmY21GMGFXNW5Jam9pSWl3aWMzUnlaV0Z0WDJsa0lqb2lNVEl4TVRReElpd2lkSFpmWkdWMmFXTmxYMjF2WkdWc0lqb2lJbjAuRElzdnFtX0ZYTzl5ZUlhcVgwMEMweG5OcGl4UU1INkZyZXNBNzlsTzVMNFwifSJ9
🔍 Parsing MPD file and extracting DRM info...
🔍 Downloading MPD file: https://osn-video.anghcdn.co/public/139352-53962-PR670964-BX-MV017138-276047-c03e9f0767ec0f61ec4c4bb5329f107b-**********/stream.mpd?token=************************************************************************************************************************************************************************************************************************************************
📄 MPD file downloaded successfully, searching for PSSH...
🔍 Found ContentProtection with scheme: urn:mpeg:dash:mp4protection:2011
🔍 Found ContentProtection with scheme: urn:uuid:edef8ba9-79d6-4ace-a3c8-27dcd51d21ed
✅ Found PSSH via Method 1: AAAAu3Bzc2gAAAAA7e+LqXnWSs6jyCfc1R0h7QAAAJsIARIQqY...
🔑 PSSH: AAAAu3Bzc2gAAAAA7e+LqXnWSs6jyCfc1R0h7QAAAJsIARIQqY3RJO4/qwQ+IqBxgeYZjiKEAWV5SmhjM05sZEVsa0lqb2lNVE01TXpVeUxUVXpPVFl5TFZCU05qY3dPVFkwTFVKWUxVMVdNREUzTVRNNExUSTNOakEwTnkxak1ETmxPV1l3TnpZM1pXTXdaall4WldNMFl6UmlZalV6TWpsbU1UQTNZaTB4TnpRMU5EQXdNREkySW4wPQ==
🆔 KID: a98dd124ee3fab043e22a07181e6198e
🔐 Extracting DRM keys...
✅ Successfully extracted 4 DRM keys
🔑 Key: f1cb8ce7935cf90516aa8c6c55946ecd:3ed72154eb94b60e5c9f7820fd22a04b
🔑 Key: a98dd124ee3fab043e22a07181e6198e:d9929bb8dd56110975f9b8c47fc89d9a
🔑 Key: 013696ad0ebc5b70216b38b033487edc:f428e985c0c9717cb7041bc0829d9fc9
🔑 Key: 121566caeff02711aacd2d1c69e3d23f:94e336f4c56a3c86ea2a06a12e96524a
🔓 Extracted 4 DRM keys
📄 Parsing MPD file: https://osn-video.anghcdn.co/public/139352-53962-PR670964-BX-MV017138-276047-c03e9f0767ec0f61ec4c4bb5329f107b-**********/stream.mpd?token=************************************************************************************************************************************************************************************************************************************************
🎬 Found video representation: 640x360 - 236000 bps - UUID: 0b0bb92d-f19e-471f-89fe-d10c407453f2 - Codecs: avc1.4D401F
🎬 Creating quality: 360p (640x360) - UUID: 0b0bb92d-f19e-471f-89fe-d10c407453f2 - Bandwidth: 236000
🎬 Found video representation: 1920x1080 - 6000000 bps - UUID: bed2e0c6-ba47-4dd5-b0d3-422aa2cf2cdd - Codecs: avc1.640028
🎬 Creating quality: 1080p (1920x1080) - UUID: bed2e0c6-ba47-4dd5-b0d3-422aa2cf2cdd - Bandwidth: 6000000
🎬 Found video representation: 1920x1080 - 3499005 bps - UUID: 5a46ab83-6284-4ece-9e13-d7d2d67ca035 - Codecs: avc1.640028
🎬 Creating quality: 1080p (1920x1080) - UUID: 5a46ab83-6284-4ece-9e13-d7d2d67ca035 - Bandwidth: 3499005
🎬 Found video representation: 1280x720 - 2040507 bps - UUID: 45e38a90-6337-4173-a754-cc817239b794 - Codecs: avc1.4D401F
🎬 Creating quality: 720p (1280x720) - UUID: 45e38a90-6337-4173-a754-cc817239b794 - Bandwidth: 2040507
🎬 Found video representation: 1920x1080 - 4581924 bps - UUID: 02a656bc-adc1-4ed7-b441-40c21d1c2908 - Codecs: avc1.640028
🎬 Creating quality: 1080p (1920x1080) - UUID: 02a656bc-adc1-4ed7-b441-40c21d1c2908 - Bandwidth: 4581924
🎬 Found video representation: 960x540 - 693945 bps - UUID: 088308c1-dad9-47d2-a488-4c58e63a41e8 - Codecs: avc1.4D401F
🎬 Creating quality: 480p (960x540) - UUID: 088308c1-dad9-47d2-a488-4c58e63a41e8 - Bandwidth: 693945
🎵 Keeping audio track: English - AAC (ID: cd5145a1-c381-40d3-bbe2-2d9dbe5e9c37)
🎵 Keeping audio track: English - Dolby Digital Plus (ID: 84f425a0-6dbe-45b1-a269-b4c87d475d99)
🎵 Keeping audio track: French - AAC (ID: 875b90cc-9591-4b75-aa97-acc20977653e)
🎵 Keeping audio track: English - Audio Description (ID: ae7cff73-f59a-41bd-a843-48b688b883bb)
📊 Found 6 video qualities, 4 audio tracks, 2 subtitle tracks
📋 DETAILED QUALITY LIST:
📋   Quality 1: 1080p (1920x1080) - UUID: bed2e0c6-ba47-4dd5-b0d3-422aa2cf2cdd - Bandwidth: 6000000
📋   Quality 2: 1080p (1920x1080) - UUID: 02a656bc-adc1-4ed7-b441-40c21d1c2908 - Bandwidth: 4581924
📋   Quality 3: 1080p (1920x1080) - UUID: 5a46ab83-6284-4ece-9e13-d7d2d67ca035 - Bandwidth: 3499005
📋   Quality 4: 720p (1280x720) - UUID: 45e38a90-6337-4173-a754-cc817239b794 - Bandwidth: 2040507
📋   Quality 5: 480p (960x540) - UUID: 088308c1-dad9-47d2-a488-4c58e63a41e8 - Bandwidth: 693945
📋   Quality 6: 360p (640x360) - UUID: 0b0bb92d-f19e-471f-89fe-d10c407453f2 - Bandwidth: 236000
✅ Download options displayed successfully!
🎵 Audio selected: English - Dolby Digital Plus
🎵 Audio selected: French - AAC
🎵 Audio selected: English - Audio Description
🚀 Starting download with selections...
📺 Quality: 360p
🔊 Audio tracks: 4
📝 Subtitles: 2
✅ Added download to table: The Wild Robot (2024)
🎬 Movie added to downloads table: The Wild Robot (2024)
⏳ Click 'Start Downloads' button to begin download
🚀 Single download detected
🎬 Starting single movie download
🎬 Starting movie download from stored item...
🎬 Starting movie download from table...
🔑 KEYS Already Saved!
🔍 Comprehensive file check for: The Wild Robot - 2024 (360p)
🔍 Checking for existing files: The Wild Robot - 2024 (360p)
📋 File check result: 🆕 No existing files found, proceeding with download
🚀 🆕 No existing files found, proceeding with download
📸 Found movie poster URL: https://osn-artwork.anghcdn.co/portrait_tt_PR670964/MV017138_PTT.jpg
📸 Downloading movie poster from: https://osn-artwork.anghcdn.co/portrait_tt_PR670964/MV017138_PTT.jpg
✅ Movie poster downloaded and saved as C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\downloads\The Wild Robot - 2024\The Wild Robot - 2024_poster.jpg
🚀 Starting movie download: The Wild Robot - 2024
🎵 Selected audio tracks: [{'language': 'en', 'codecs': 'mp4a.40.2', 'bandwidth': '128000', 'id': 'cd5145a1-c381-40d3-bbe2-2d9dbe5e9c37'}, {'language': 'en-ddp', 'codecs': 'ec-3', 'bandwidth': '384000', 'id': '84f425a0-6dbe-45b1-a269-b4c87d475d99'}, {'language': 'fr', 'codecs': 'mp4a.40.2', 'bandwidth': '128000', 'id': '875b90cc-9591-4b75-aa97-acc20977653e'}, {'language': 'en-da', 'codecs': 'mp4a.40.2', 'bandwidth': '128000', 'id': 'ae7cff73-f59a-41bd-a843-48b688b883bb'}]
📝 Selected subtitle tracks: [{'language': 'en', 'mime_type': 'text/vtt'}, {'language': 'ar', 'mime_type': 'text/vtt'}]
🔧 Building selection options...
🎵 Input audio_tracks: [{'language': 'en', 'codecs': 'mp4a.40.2', 'bandwidth': '128000', 'id': 'cd5145a1-c381-40d3-bbe2-2d9dbe5e9c37'}, {'language': 'en-ddp', 'codecs': 'ec-3', 'bandwidth': '384000', 'id': '84f425a0-6dbe-45b1-a269-b4c87d475d99'}, {'language': 'fr', 'codecs': 'mp4a.40.2', 'bandwidth': '128000', 'id': '875b90cc-9591-4b75-aa97-acc20977653e'}, {'language': 'en-da', 'codecs': 'mp4a.40.2', 'bandwidth': '128000', 'id': 'ae7cff73-f59a-41bd-a843-48b688b883bb'}]
📝 Input subtitle_tracks: [{'language': 'en', 'mime_type': 'text/vtt'}, {'language': 'ar', 'mime_type': 'text/vtt'}]
🎯 Using track ID selector: id=cd5145a1-c381-40d3-bbe2-2d9dbe5e9c37
🎵 Created audio selector: id=cd5145a1-c381-40d3-bbe2-2d9dbe5e9c37 (from en, mp4a.40.2)
🎯 Using track ID selector: id=84f425a0-6dbe-45b1-a269-b4c87d475d99
🎵 Created audio selector: id=84f425a0-6dbe-45b1-a269-b4c87d475d99 (from en-ddp, ec-3)
🎯 Using track ID selector: id=875b90cc-9591-4b75-aa97-acc20977653e
🎵 Created audio selector: id=875b90cc-9591-4b75-aa97-acc20977653e (from fr, mp4a.40.2)
🎯 Using track ID selector: id=ae7cff73-f59a-41bd-a843-48b688b883bb
🎵 Created audio selector: id=ae7cff73-f59a-41bd-a843-48b688b883bb (from en-da, mp4a.40.2)
🎵 User audio selection (multiple IDs): --select-audio "id=cd5145a1-c381-40d3-bbe2-2d9dbe5e9c37|84f425a0-6dbe-45b1-a269-b4c87d475d99|875b90cc-9591-4b75-aa97-acc20977653e|ae7cff73-f59a-41bd-a843-48b688b883bb:for=all"
📝 User subtitle selection (multiple): --select-subtitle "lang=en|ar:for=all"
✅ Final audio option: --select-audio "id=cd5145a1-c381-40d3-bbe2-2d9dbe5e9c37|84f425a0-6dbe-45b1-a269-b4c87d475d99|875b90cc-9591-4b75-aa97-acc20977653e|ae7cff73-f59a-41bd-a843-48b688b883bb:for=all"
✅ Final subtitle option: --select-subtitle "lang=en|ar:for=all"
🔧 Movie download command: "C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\binaries\N_m3u8DL-RE.exe" "https://osn-video.anghcdn.co/public/139352-53962-PR670964-BX-MV017138-276047-c03e9f0767ec0f61ec4c4bb5329f107b-**********/stream.mpd?token=************************************************************************************************************************************************************************************************************************************************" -mt --select-video "id=0b0bb92d-f19e-471f-89fe-d10c407453f2" --select-audio "id=cd5145a1-c381-40d3-bbe2-2d9dbe5e9c37|84f425a0-6dbe-45b1-a269-b4c87d475d99|875b90cc-9591-4b75-aa97-acc20977653e|ae7cff73-f59a-41bd-a843-48b688b883bb:for=all" --select-subtitle "lang=en|ar:for=all" --tmp-dir "C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\cache" --save-dir "C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\cache" --save-name "The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC" --decryption-binary-path="C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\binaries\mp4decrypt.exe" --key-text-file="C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\KEYS\OSNPLUS_KEYS.txt" --log-level "OFF"
🔄 Dynamic progress system reset
🔄 Started continuous progress monitor
🎯 Dynamic Progress: 0% - 🚀 Starting movie download: The Wild Robot - 2024
🎯 MAIN: Received download_progress signal: 0% - 🚀 Starting movie download: The Wild Robot - 2024
📊 Component status: Video=1%🔄, Audio=0%⏸️, Subtitle=0%⏸️, Merge=0%⏸️
🎯 Updated downloads table progress for row 0: 0%
🎯 UI Progress Updated: 0% - 🚀 Starting movie download: The Wild Robot - 2024
🔍 Starting complete download monitoring for: The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC
📊 Expected tracks: Video=1, Audio=4, Subtitles=2
📁 Starting comprehensive file monitoring in: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\cache
📊 Gradual Progress: -4% - 🚀 Initializing: The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC
🎯 MAIN: Received download_progress signal: -4% - 🚀 Initializing: The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC
🎯 Updated downloads table progress for row 0: -4%
🎯 UI Progress Updated: -4% - 🚀 Initializing: The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC
📊 Gradual Progress: -3% - 🚀 Initializing: The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC
🎯 MAIN: Received download_progress signal: -3% - 🚀 Initializing: The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC
🎯 Updated downloads table progress for row 0: -3%
🎯 UI Progress Updated: -3% - 🚀 Initializing: The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC
📊 Gradual Progress: -2% - 🚀 Initializing: The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC
🎯 MAIN: Received download_progress signal: -2% - 🚀 Initializing: The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC
🎯 Updated downloads table progress for row 0: -2%
🎯 UI Progress Updated: -2% - 🚀 Initializing: The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC
📊 Gradual Progress: -1% - 🚀 Initializing: The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC
🎯 MAIN: Received download_progress signal: -1% - 🚀 Initializing: The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC
🎯 Updated downloads table progress for row 0: -1%
🎯 UI Progress Updated: -1% - 🚀 Initializing: The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC
📊 Gradual Progress: 0% - 🚀 Initializing: The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC
🎯 MAIN: Received download_progress signal: 0% - 🚀 Initializing: The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC
🎯 Updated downloads table progress for row 0: 0%
🎯 UI Progress Updated: 0% - 🚀 Initializing: The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC
📊 Gradual Progress: 1% - 🚀 Initializing: The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC
🎯 MAIN: Received download_progress signal: 1% - 🚀 Initializing: The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC
🎯 Updated downloads table progress for row 0: 1%
🎯 UI Progress Updated: 1% - 🚀 Initializing: The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC
📥 N_m3u8DL-RE: Vid 640x360 | 236 Kbps: 0%
📥 N_m3u8DL-RE: Aud 384 Kbps | en-ddp | F801CH: 0%
📥 N_m3u8DL-RE: Aud 128 Kbps | en | 2CH: 0%
📥 N_m3u8DL-RE: Aud 128 Kbps | fr | 2CH: 0%
📥 N_m3u8DL-RE: Aud 128 Kbps | en-da | 2CH: 0%
📥 N_m3u8DL-RE: Sub ar: 0%
🎯 Dynamic Progress: 0% - 📹 Video: 1.1% (Total: 0%)
🎯 MAIN: Received download_progress signal: 0% - 📹 Video: 1.1% (Total: 0%)
📊 Component status: Video=1.1%🔄, Audio=0%⏸️, Subtitle=0%⏸️, Merge=0%⏸️
🎯 Updated downloads table progress for row 0: 0%
🎯 UI Progress Updated: 0% - 📹 Video: 1.1% (Total: 0%)
📊 File Progress: 28.6% - Video:0/1, Audio:2/4, Subs:0/2
🎯 Dynamic Progress: 0% - 📹 Video: 1.2000000000000002% (Total: 0%)
📊 Component status: Video=1.2000000000000002%🔄, Audio=0%⏸️, Subtitle=0%⏸️, Merge=0%⏸️
🎯 MAIN: Received download_progress signal: 0% - 📹 Video: 1.2000000000000002% (Total: 0%)
🎯 Updated downloads table progress for row 0: 0%
🎯 UI Progress Updated: 0% - 📹 Video: 1.2000000000000002% (Total: 0%)
🎯 Dynamic Progress: 0% - 📹 Video: 1.3000000000000003% (Total: 0%)
📊 Component status: Video=1.3000000000000003%🔄, Audio=0%⏸️, Subtitle=0%⏸️, Merge=0%⏸️
🎯 MAIN: Received download_progress signal: 0% - 📹 Video: 1.3000000000000003% (Total: 0%)
🎯 Updated downloads table progress for row 0: 0%
🎯 UI Progress Updated: 0% - 📹 Video: 1.3000000000000003% (Total: 0%)
📊 File Progress: 28.6% - Video:0/1, Audio:2/4, Subs:0/2
🎯 Dynamic Progress: 0% - 📹 Video: 1.4000000000000004% (Total: 0%)
🎯 MAIN: Received download_progress signal: 0% - 📹 Video: 1.4000000000000004% (Total: 0%)
📊 Component status: Video=1.4000000000000004%🔄, Audio=0%⏸️, Subtitle=0%⏸️, Merge=0%⏸️
🎯 Updated downloads table progress for row 0: 0%
🎯 UI Progress Updated: 0% - 📹 Video: 1.4000000000000004% (Total: 0%)
🎯 Dynamic Progress: 1% - 📹 Video: 1.5000000000000004% (Total: 1%)
🎯 MAIN: Received download_progress signal: 1% - 📹 Video: 1.5000000000000004% (Total: 1%)
📊 Component status: Video=1.5000000000000004%🔄, Audio=0%⏸️, Subtitle=0%⏸️, Merge=0%⏸️
🎯 Updated downloads table progress for row 0: 1%
🎯 UI Progress Updated: 1% - 📹 Video: 1.5000000000000004% (Total: 1%)
📊 File Progress: 28.6% - Video:0/1, Audio:2/4, Subs:0/2
🎯 Dynamic Progress: 1% - 📹 Video: 1.6000000000000005% (Total: 1%)
🎯 MAIN: Received download_progress signal: 1% - 📹 Video: 1.6000000000000005% (Total: 1%)
📊 Component status: Video=1.6000000000000005%🔄, Audio=0%⏸️, Subtitle=0%⏸️, Merge=0%⏸️
🎯 Updated downloads table progress for row 0: 1%
🎯 UI Progress Updated: 1% - 📹 Video: 1.6000000000000005% (Total: 1%)
🎯 Dynamic Progress: 1% - 📹 Video: 1.7000000000000006% (Total: 1%)
🎯 MAIN: Received download_progress signal: 1% - 📹 Video: 1.7000000000000006% (Total: 1%)
📊 Component status: Video=1.7000000000000006%🔄, Audio=0%⏸️, Subtitle=0%⏸️, Merge=0%⏸️
🎯 Updated downloads table progress for row 0: 1%
🎯 UI Progress Updated: 1% - 📹 Video: 1.7000000000000006% (Total: 1%)
📊 File Progress: 28.6% - Video:0/1, Audio:2/4, Subs:0/2
🎯 Dynamic Progress: 1% - 📹 Video: 1.8000000000000007% (Total: 1%)
📊 Component status: Video=1.8000000000000007%🔄, Audio=0%⏸️, Subtitle=0%⏸️, Merge=0%⏸️
🎯 MAIN: Received download_progress signal: 1% - 📹 Video: 1.8000000000000007% (Total: 1%)
🎯 Updated downloads table progress for row 0: 1%
🎯 UI Progress Updated: 1% - 📹 Video: 1.8000000000000007% (Total: 1%)
🎯 Dynamic Progress: 1% - 📹 Video: 1.9000000000000008% (Total: 1%)
📊 Component status: Video=1.9000000000000008%🔄, Audio=0%⏸️, Subtitle=0%⏸️, Merge=0%⏸️
🎯 MAIN: Received download_progress signal: 1% - 📹 Video: 1.9000000000000008% (Total: 1%)
🎯 Updated downloads table progress for row 0: 1%
🎯 UI Progress Updated: 1% - 📹 Video: 1.9000000000000008% (Total: 1%)
📊 File Progress: 28.6% - Video:0/1, Audio:2/4, Subs:0/2
🎯 Dynamic Progress: 1% - 📹 Video: 2.000000000000001% (Total: 1%)
📊 Component status: Video=2.000000000000001%🔄, Audio=0%⏸️, Subtitle=0%⏸️, Merge=0%⏸️
🎯 MAIN: Received download_progress signal: 1% - 📹 Video: 2.000000000000001% (Total: 1%)
🎯 Updated downloads table progress for row 0: 1%
🎯 UI Progress Updated: 1% - 📹 Video: 2.000000000000001% (Total: 1%)
🎯 Dynamic Progress: 1% - 📹 Video: 2.100000000000001% (Total: 1%)
🎯 MAIN: Received download_progress signal: 1% - 📹 Video: 2.100000000000001% (Total: 1%)
📊 Component status: Video=2.100000000000001%🔄, Audio=0%⏸️, Subtitle=0%⏸️, Merge=0%⏸️
🎯 Updated downloads table progress for row 0: 1%
🎯 UI Progress Updated: 1% - 📹 Video: 2.100000000000001% (Total: 1%)
📊 File Progress: 28.6% - Video:0/1, Audio:2/4, Subs:0/2
🎯 Dynamic Progress: 1% - 📹 Video: 2.200000000000001% (Total: 1%)
📊 Component status: Video=2.200000000000001%🔄, Audio=0%⏸️, Subtitle=0%⏸️, Merge=0%⏸️
🎯 MAIN: Received download_progress signal: 1% - 📹 Video: 2.200000000000001% (Total: 1%)
🎯 Updated downloads table progress for row 0: 1%
🎯 UI Progress Updated: 1% - 📹 Video: 2.200000000000001% (Total: 1%)
🎯 Dynamic Progress: 1% - 📹 Video: 2.300000000000001% (Total: 1%)
📊 Component status: Video=2.300000000000001%🔄, Audio=0%⏸️, Subtitle=0%⏸️, Merge=0%⏸️
🎯 MAIN: Received download_progress signal: 1% - 📹 Video: 2.300000000000001% (Total: 1%)
🎯 Updated downloads table progress for row 0: 1%
🎯 UI Progress Updated: 1% - 📹 Video: 2.300000000000001% (Total: 1%)
📊 File Progress: 28.6% - Video:0/1, Audio:2/4, Subs:0/2
🎯 Dynamic Progress: 1% - 📹 Video: 2.4000000000000012% (Total: 1%)
🎯 MAIN: Received download_progress signal: 1% - 📹 Video: 2.4000000000000012% (Total: 1%)
📊 Component status: Video=2.4000000000000012%🔄, Audio=0%⏸️, Subtitle=0%⏸️, Merge=0%⏸️
🎯 Updated downloads table progress for row 0: 1%
🎯 UI Progress Updated: 1% - 📹 Video: 2.4000000000000012% (Total: 1%)
🎯 Dynamic Progress: 1% - 📹 Video: 2.5000000000000013% (Total: 1%)
🎯 MAIN: Received download_progress signal: 1% - 📹 Video: 2.5000000000000013% (Total: 1%)
📊 Component status: Video=2.5000000000000013%🔄, Audio=0%⏸️, Subtitle=0%⏸️, Merge=0%⏸️
🎯 Updated downloads table progress for row 0: 1%
🎯 UI Progress Updated: 1% - 📹 Video: 2.5000000000000013% (Total: 1%)
📥 N_m3u8DL-RE: Vid 640x360 | 236 Kbps: 11%
📥 N_m3u8DL-RE: Aud 384 Kbps | en-ddp | F801CH: 9%
📊 Gradual Progress: 0% - 🚀 Initializing: The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC
🎯 MAIN: Received download_progress signal: 0% - 🚀 Initializing: The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC
📥 N_m3u8DL-RE: Aud 128 Kbps | en | 2CH: 19%
🎯 Updated downloads table progress for row 0: 0%
📥 N_m3u8DL-RE: Aud 128 Kbps | fr | 2CH: 18%
🎯 UI Progress Updated: 0% - 🚀 Initializing: The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC
📥 N_m3u8DL-RE: Aud 128 Kbps | en-da | 2CH: 17%
📥 N_m3u8DL-RE: Sub en: 100%
🎯 Download completion detected in line: Sub en: 100%
⏹️ Stopped continuous progress monitor
🔧 Forcing immediate merge...
🚀 IMMEDIATE MERGE: Download completed, starting merge NOW!
⏹️ Stopped continuous progress monitor
📊 Gradual Progress: 1% - 🚀 Initializing: The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC
🎯 MAIN: Received download_progress signal: 1% - 🚀 Initializing: The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC
🎯 Updated downloads table progress for row 0: 1%
🎯 UI Progress Updated: 1% - 🚀 Initializing: The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC
📊 File Progress: 28.6% - Video:0/1, Audio:2/4, Subs:0/2
🔍 Scanning cache directory: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\cache
📁 Found 3 total files:
   - The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC (4096 bytes)
   - The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC.ar.srt (104768 bytes)
   - The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC.en.srt (79226 bytes)
📹 Video files: 0
🔊 Audio files: 0
❌ No video files found for immediate merge
🎯 MAIN: Received download_error signal: No video files found for immediate merge
📊 File Progress: 28.6% - Video:0/1, Audio:2/4, Subs:0/2
📊 File Progress: 28.6% - Video:0/1, Audio:2/4, Subs:0/2
📊 File Progress: 28.6% - Video:0/1, Audio:2/4, Subs:0/2
📊 File Progress: 28.6% - Video:0/1, Audio:2/4, Subs:0/2
📊 File Progress: 28.6% - Video:0/1, Audio:2/4, Subs:0/2
📊 File Progress: 28.6% - Video:0/1, Audio:2/4, Subs:0/2
📊 File Progress: 28.6% - Video:0/1, Audio:2/4, Subs:0/2
📊 File Progress: 28.6% - Video:0/1, Audio:2/4, Subs:0/2
📊 File Progress: 28.6% - Video:0/1, Audio:2/4, Subs:0/2
📊 File Progress: 28.6% - Video:0/1, Audio:2/4, Subs:0/2
📊 File Progress: 28.6% - Video:0/1, Audio:2/4, Subs:0/2
📊 File Progress: 28.6% - Video:0/1, Audio:2/4, Subs:0/2
📊 File Progress: 28.6% - Video:0/1, Audio:2/4, Subs:0/2
📊 File Progress: 28.6% - Video:0/1, Audio:2/4, Subs:0/2
📊 File Progress: 28.6% - Video:0/1, Audio:2/4, Subs:0/2
📊 File Progress: 28.6% - Video:0/1, Audio:2/4, Subs:0/2
📊 File Progress: 28.6% - Video:0/1, Audio:2/4, Subs:0/2
📊 File Progress: 28.6% - Video:0/1, Audio:2/4, Subs:0/2
📊 File Progress: 28.6% - Video:0/1, Audio:2/4, Subs:0/2
📊 File Progress: 28.6% - Video:0/1, Audio:2/4, Subs:0/2
📊 File Progress: 28.6% - Video:0/1, Audio:2/4, Subs:0/2
📊 File Progress: 28.6% - Video:0/1, Audio:2/4, Subs:0/2
📊 File Progress: 28.6% - Video:0/1, Audio:2/4, Subs:0/2
📊 File Progress: 28.6% - Video:0/1, Audio:2/4, Subs:0/2
📊 File Progress: 28.6% - Video:0/1, Audio:2/4, Subs:0/2
📊 File Progress: 28.6% - Video:0/1, Audio:2/4, Subs:0/2
📊 File Progress: 28.6% - Video:0/1, Audio:2/4, Subs:0/2
📊 File Progress: 28.6% - Video:0/1, Audio:2/4, Subs:0/2
📊 File Progress: 28.6% - Video:0/1, Audio:2/4, Subs:0/2
📊 File Progress: 28.6% - Video:0/1, Audio:2/4, Subs:0/2
📊 File Progress: 57.1% - Video:0/1, Audio:4/4, Subs:0/2
📊 File Progress: 57.1% - Video:0/1, Audio:4/4, Subs:0/2
📊 File Progress: 71.4% - Video:0/1, Audio:5/4, Subs:0/2
📊 File Progress: 71.4% - Video:0/1, Audio:5/4, Subs:0/2
📊 File Progress: 71.4% - Video:0/1, Audio:5/4, Subs:0/2
📊 File Progress: 71.4% - Video:0/1, Audio:5/4, Subs:0/2
📊 File Progress: 71.4% - Video:0/1, Audio:5/4, Subs:0/2
📊 File Progress: 71.4% - Video:0/1, Audio:5/4, Subs:0/2
📊 File Progress: 71.4% - Video:0/1, Audio:5/4, Subs:0/2
📊 File Progress: 71.4% - Video:0/1, Audio:5/4, Subs:0/2
📊 File Progress: 71.4% - Video:0/1, Audio:5/4, Subs:0/2
📊 File Progress: 85.7% - Video:1/1, Audio:5/4, Subs:0/2
📊 File Progress: 85.7% - Video:1/1, Audio:5/4, Subs:0/2
C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\main.py:293: DeprecationWarning: Function: 'QMouseEvent.globalPos() const' is marked as deprecated, please check the documentation for more information.
  self.dragPos = event.globalPos()
Mouse click: LEFT CLICK
📊 File Progress: 85.7% - Video:1/1, Audio:5/4, Subs:0/2
📊 File Progress: 85.7% - Video:1/1, Audio:5/4, Subs:0/2
📊 File Progress: 85.7% - Video:1/1, Audio:5/4, Subs:0/2
📊 File Progress: 85.7% - Video:1/1, Audio:5/4, Subs:0/2
📊 Gradual Progress: 1% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 1% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 1%
🎯 UI Progress Updated: 1% - 📥 Download completed, verifying files...
📊 Gradual Progress: 2% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 2% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 2%
🎯 UI Progress Updated: 2% - 📥 Download completed, verifying files...
📊 Gradual Progress: 3% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 3% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 3%
🎯 UI Progress Updated: 3% - 📥 Download completed, verifying files...
📊 Gradual Progress: 4% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 4% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 4%
🎯 UI Progress Updated: 4% - 📥 Download completed, verifying files...
📊 Gradual Progress: 5% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 5% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 5%
🎯 UI Progress Updated: 5% - 📥 Download completed, verifying files...
📊 Gradual Progress: 6% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 6% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 6%
🎯 UI Progress Updated: 6% - 📥 Download completed, verifying files...
📊 Gradual Progress: 7% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 7% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 7%
🎯 UI Progress Updated: 7% - 📥 Download completed, verifying files...
📊 Gradual Progress: 8% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 8% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 8%
🎯 UI Progress Updated: 8% - 📥 Download completed, verifying files...
📊 File verification: Video=1/1, Audio=7/4, Subs=2/2
📊 Gradual Progress: 85% - ✅ All files verified, preparing merge...
🎯 MAIN: Received download_progress signal: 85% - ✅ All files verified, preparing merge...
🎯 Updated downloads table progress for row 0: 85%
🎯 UI Progress Updated: 85% - ✅ All files verified, preparing merge...
📊 Gradual Progress: 9% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 9% - 📥 Download completed, verifying files...
📊 File Progress: 100.0% - Video:1/1, Audio:6/4, Subs:0/2
🎯 Updated downloads table progress for row 0: 9%
🎯 UI Progress Updated: 9% - 📥 Download completed, verifying files...
📊 Gradual Progress: 86% - ✅ All files verified, preparing merge...
🎯 MAIN: Received download_progress signal: 86% - ✅ All files verified, preparing merge...
🎯 Updated downloads table progress for row 0: 86%
🎯 UI Progress Updated: 86% - ✅ All files verified, preparing merge...
📊 Gradual Progress: 10% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 10% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 10%
🎯 UI Progress Updated: 10% - 📥 Download completed, verifying files...
📊 Gradual Progress: 87% - ✅ All files verified, preparing merge...
🎯 MAIN: Received download_progress signal: 87% - ✅ All files verified, preparing merge...
🎯 Updated downloads table progress for row 0: 87%
🎯 UI Progress Updated: 87% - ✅ All files verified, preparing merge...
📊 Gradual Progress: 11% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 11% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 11%
🎯 UI Progress Updated: 11% - 📥 Download completed, verifying files...
📊 Gradual Progress: 88% - ✅ All files verified, preparing merge...
🎯 MAIN: Received download_progress signal: 88% - ✅ All files verified, preparing merge...
🎯 Updated downloads table progress for row 0: 88%
🎯 UI Progress Updated: 88% - ✅ All files verified, preparing merge...
📊 Gradual Progress: 12% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 12% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 12%
🎯 UI Progress Updated: 12% - 📥 Download completed, verifying files...
📊 Gradual Progress: 89% - ✅ All files verified, preparing merge...
🎯 MAIN: Received download_progress signal: 89% - ✅ All files verified, preparing merge...
🎯 Updated downloads table progress for row 0: 89%
🎯 UI Progress Updated: 89% - ✅ All files verified, preparing merge...
📊 Gradual Progress: 13% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 13% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 13%
🎯 UI Progress Updated: 13% - 📥 Download completed, verifying files...
🔧 Force starting merge process...
📊 Gradual Progress: 90% - 🔧 Starting merge process...
🎯 MAIN: Received download_progress signal: 90% - 🔧 Starting merge process...
🎯 Updated downloads table progress for row 0: 90%
🎯 UI Progress Updated: 90% - 🔧 Starting merge process...
📊 Gradual Progress: 90% - ✅ All files verified, preparing merge...
🎯 MAIN: Received download_progress signal: 90% - ✅ All files verified, preparing merge...
🎯 Updated downloads table progress for row 0: 90%
🎯 UI Progress Updated: 90% - ✅ All files verified, preparing merge...
📊 Gradual Progress: 14% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 14% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 14%
🎯 UI Progress Updated: 14% - 📥 Download completed, verifying files...
📊 Gradual Progress: 91% - 🔧 Starting merge process...
🎯 MAIN: Received download_progress signal: 91% - 🔧 Starting merge process...
🎯 Updated downloads table progress for row 0: 91%
🎯 UI Progress Updated: 91% - 🔧 Starting merge process...
📊 Gradual Progress: 15% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 15% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 15%
🎯 UI Progress Updated: 15% - 📥 Download completed, verifying files...
📊 Gradual Progress: 92% - 🔧 Starting merge process...
🎯 MAIN: Received download_progress signal: 92% - 🔧 Starting merge process...
🎯 Updated downloads table progress for row 0: 92%
🎯 UI Progress Updated: 92% - 🔧 Starting merge process...
📊 Gradual Progress: 16% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 16% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 16%
🎯 UI Progress Updated: 16% - 📥 Download completed, verifying files...
📊 Gradual Progress: 93% - 🔧 Starting merge process...
🎯 MAIN: Received download_progress signal: 93% - 🔧 Starting merge process...
🎯 Updated downloads table progress for row 0: 93%
🎯 UI Progress Updated: 93% - 🔧 Starting merge process...
📊 Gradual Progress: 17% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 17% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 17%
📊 Gradual Progress: 94% - 🔧 Starting merge process...
🎯 UI Progress Updated: 17% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 94% - 🔧 Starting merge process...
🎯 Updated downloads table progress for row 0: 94%
🎯 UI Progress Updated: 94% - 🔧 Starting merge process...
📊 Gradual Progress: 18% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 18% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 18%
🎯 UI Progress Updated: 18% - 📥 Download completed, verifying files...
📊 Gradual Progress: 95% - 🔧 Starting merge process...
🎯 MAIN: Received download_progress signal: 95% - 🔧 Starting merge process...
🎯 Updated downloads table progress for row 0: 95%
🎯 UI Progress Updated: 95% - 🔧 Starting merge process...
📊 Gradual Progress: 19% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 19% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 19%
🎯 UI Progress Updated: 19% - 📥 Download completed, verifying files...
📊 Gradual Progress: 20% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 20% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 20%
🎯 UI Progress Updated: 20% - 📥 Download completed, verifying files...
📊 Gradual Progress: 21% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 21% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 21%
🎯 UI Progress Updated: 21% - 📥 Download completed, verifying files...
📊 Gradual Progress: 22% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 22% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 22%
🎯 UI Progress Updated: 22% - 📥 Download completed, verifying files...
🔍 Found 1 video files and 4 audio files
📹 Using video file: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\cache\The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC.mp4
🔧 Starting movie merge with mkvmerge...
🎯 Dynamic Progress: 6% - 🔧 Merging movie files...
📊 Component status: Video=2.5000000000000013%🔄, Audio=0%⏸️, Subtitle=0%⏸️, Merge=95%🔄
✅ mkvmerge found at: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\binaries\mkvmerge.exe
🔊 Adding English AAC audio to the movie MKV file
🔊 Adding English Dolby Digital Plus audio to the movie MKV file
🔊 Adding French AAC audio to the movie MKV file
🔊 Adding English Audio Description audio to the movie MKV file
📝 Adding Arabic subtitle to the movie MKV file
📝 Adding English subtitle to the movie MKV file
Running mkvmerge command: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\binaries\mkvmerge.exe -o C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\downloads\The Wild Robot - 2024\The Wild Robot - 2024.360p.OSN+.WEB-DL.H264.AAC.mkv C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\cache\The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC.mp4 --language 0:eng --track-name 0:English AAC C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\cache\The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC.en-da.m4a --language 0:eng --track-name 0:English Dolby Digital Plus C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\cache\The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC.en-ddp.m4a --language 0:fre --track-name 0:French AAC C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\cache\The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC.en.m4a --language 0:eng --track-name 0:English Audio Description C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\cache\The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC.fr.m4a --language 0:ara --track-name 0:Arabic C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\cache\The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC.ar.srt --language 0:eng --track-name 0:English C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\cache\The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC.en.srt
🎯 MAIN: Received download_progress signal: 6% - 🔧 Merging movie files...
🎯 Updated downloads table progress for row 0: 6%
🎯 UI Progress Updated: 6% - 🔧 Merging movie files...
mkvmerge v57.0.0 ('Till The End') 64-bit
'C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\cache\The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC.mp4': Using the demultiplexer for the format 'QuickTime/MP4'.
📊 Gradual Progress: 23% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 23% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 23%
🎯 UI Progress Updated: 23% - 📥 Download completed, verifying files...
📊 Gradual Progress: 24% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 24% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 24%
🎯 UI Progress Updated: 24% - 📥 Download completed, verifying files...
'C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\cache\The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC.en-da.m4a': Using the demultiplexer for the format 'QuickTime/MP4'.
📊 Gradual Progress: 25% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 25% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 25%
🎯 UI Progress Updated: 25% - 📥 Download completed, verifying files...
📊 Gradual Progress: 26% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 26% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 26%
🎯 UI Progress Updated: 26% - 📥 Download completed, verifying files...
📊 Gradual Progress: 27% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 27% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 27%
🎯 UI Progress Updated: 27% - 📥 Download completed, verifying files...
📊 Gradual Progress: 28% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 28% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 28%
🎯 UI Progress Updated: 28% - 📥 Download completed, verifying files...
📊 Gradual Progress: 29% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 29% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 29%
🎯 UI Progress Updated: 29% - 📥 Download completed, verifying files...
📊 Gradual Progress: 30% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 30% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 30%
🎯 UI Progress Updated: 30% - 📥 Download completed, verifying files...
'C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\cache\The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC.en-ddp.m4a': Using the demultiplexer for the format 'QuickTime/MP4'.
📊 Gradual Progress: 31% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 31% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 31%
🎯 UI Progress Updated: 31% - 📥 Download completed, verifying files...
📊 Gradual Progress: 32% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 32% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 32%
🎯 UI Progress Updated: 32% - 📥 Download completed, verifying files...
📊 Gradual Progress: 33% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 33% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 33%
🎯 UI Progress Updated: 33% - 📥 Download completed, verifying files...
'C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\cache\The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC.en.m4a': Using the demultiplexer for the format 'QuickTime/MP4'.
📊 Gradual Progress: 34% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 34% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 34%
🎯 UI Progress Updated: 34% - 📥 Download completed, verifying files...
📊 Gradual Progress: 35% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 35% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 35%
🎯 UI Progress Updated: 35% - 📥 Download completed, verifying files...
📊 Gradual Progress: 36% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 36% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 36%
🎯 UI Progress Updated: 36% - 📥 Download completed, verifying files...
📊 Gradual Progress: 37% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 37% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 37%
🎯 UI Progress Updated: 37% - 📥 Download completed, verifying files...
📊 Gradual Progress: 38% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 38% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 38%
🎯 UI Progress Updated: 38% - 📥 Download completed, verifying files...
📊 Gradual Progress: 39% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 39% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 39%
🎯 UI Progress Updated: 39% - 📥 Download completed, verifying files...
'C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\cache\The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC.fr.m4a': Using the demultiplexer for the format 'QuickTime/MP4'.
📊 Gradual Progress: 40% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 40% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 40%
🎯 UI Progress Updated: 40% - 📥 Download completed, verifying files...
📊 Gradual Progress: 41% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 41% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 41%
🎯 UI Progress Updated: 41% - 📥 Download completed, verifying files...
📊 Gradual Progress: 42% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 42% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 42%
🎯 UI Progress Updated: 42% - 📥 Download completed, verifying files...
📊 Gradual Progress: 43% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 43% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 43%
🎯 UI Progress Updated: 43% - 📥 Download completed, verifying files...
📊 Gradual Progress: 44% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 44% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 44%
🎯 UI Progress Updated: 44% - 📥 Download completed, verifying files...
📊 Gradual Progress: 45% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 45% - 📥 Download completed, verifying files...
'C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\cache\The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC.ar.srt': Using the demultiplexer for the format 'SRT subtitles'.
'C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\cache\The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC.en.srt': Using the demultiplexer for the format 'SRT subtitles'.
🎯 Updated downloads table progress for row 0: 45%
🎯 UI Progress Updated: 45% - 📥 Download completed, verifying files...
'C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\cache\The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC.mp4' track 0: Using the output module for the format 'AVC/H.264'.
'C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\cache\The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC.en-da.m4a' track 0: Using the output module for the format 'AAC'.
'C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\cache\The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC.en-ddp.m4a' track 0: Using the output module for the format 'AC-3'.
'C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\cache\The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC.en.m4a' track 0: Using the output module for the format 'AAC'.
'C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\cache\The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC.fr.m4a' track 0: Using the output module for the format 'AAC'.
'C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\cache\The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC.ar.srt' track 0: Using the output module for the format 'text subtitles'.
'C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\cache\The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC.en.srt' track 0: Using the output module for the format 'text subtitles'.
The file 'C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\downloads\The Wild Robot - 2024\The Wild Robot - 2024.360p.OSN+.WEB-DL.H264.AAC.mkv' has been opened for writing.
'C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\cache\The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC.mp4' track 0: Extracted the aspect ratio information from the MPEG-4 layer 10 (AVC) video data and set the display dimensions to 640/360.
📊 File Progress: 100.0% - Video:1/1, Audio:6/4, Subs:0/2
📊 Gradual Progress: 46% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 46% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 46%
🎯 UI Progress Updated: 46% - 📥 Download completed, verifying files...
📊 Gradual Progress: 47% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 47% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 47%
🎯 UI Progress Updated: 47% - 📥 Download completed, verifying files...
📊 Gradual Progress: 48% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 48% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 48%
🎯 UI Progress Updated: 48% - 📥 Download completed, verifying files...
📊 Gradual Progress: 49% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 49% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 49%
🎯 UI Progress Updated: 49% - 📥 Download completed, verifying files...
📊 Gradual Progress: 50% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 50% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 50%
🎯 UI Progress Updated: 50% - 📥 Download completed, verifying files...
📊 Gradual Progress: 51% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 51% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 51%
🎯 UI Progress Updated: 51% - 📥 Download completed, verifying files...
📊 Gradual Progress: 52% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 52% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 52%
🎯 UI Progress Updated: 52% - 📥 Download completed, verifying files...
📊 Gradual Progress: 53% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 53% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 53%
🎯 UI Progress Updated: 53% - 📥 Download completed, verifying files...
📊 Gradual Progress: 54% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 54% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 54%
🎯 UI Progress Updated: 54% - 📥 Download completed, verifying files...
📊 Gradual Progress: 55% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 55% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 55%
🎯 UI Progress Updated: 55% - 📥 Download completed, verifying files...
📊 Gradual Progress: 56% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 56% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 56%
🎯 UI Progress Updated: 56% - 📥 Download completed, verifying files...
📊 Gradual Progress: 57% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 57% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 57%
🎯 UI Progress Updated: 57% - 📥 Download completed, verifying files...
📊 Gradual Progress: 58% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 58% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 58%
🎯 UI Progress Updated: 58% - 📥 Download completed, verifying files...
📊 Gradual Progress: 59% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 59% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 59%
🎯 UI Progress Updated: 59% - 📥 Download completed, verifying files...
📊 Gradual Progress: 60% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 60% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 60%
🎯 UI Progress Updated: 60% - 📥 Download completed, verifying files...
📊 Gradual Progress: 61% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 61% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 61%
🎯 UI Progress Updated: 61% - 📥 Download completed, verifying files...
📊 Gradual Progress: 62% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 62% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 62%
🎯 UI Progress Updated: 62% - 📥 Download completed, verifying files...
📊 Gradual Progress: 63% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 63% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 63%
🎯 UI Progress Updated: 63% - 📥 Download completed, verifying files...
Progress: 100%
The cue entries (the index) are being written...
📊 Gradual Progress: 64% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 64% - 📥 Download completed, verifying files...
Multiplexing took 5 seconds.
🎯 Updated downloads table progress for row 0: 64%
🎯 UI Progress Updated: 64% - 📥 Download completed, verifying files...
✅ Movie merge completed successfully: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\downloads\The Wild Robot - 2024\The Wild Robot - 2024.360p.OSN+.WEB-DL.H264.AAC.mkv
⏹️ Stopped continuous progress monitor
🎉 Movie download and merge completed: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\downloads\The Wild Robot - 2024\The Wild Robot - 2024.360p.OSN+.WEB-DL.H264.AAC.mkv
🎯 MAIN: Received download_progress signal: 100% - ✅ Movie download completed successfully!
🎯 Updated downloads table progress for row 0: 100%
🎯 UI Progress Updated: 100% - ✅ Movie download completed successfully!
⚠️ Error in file monitoring loop: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\OSN_NEW\\OSN_NEW\\cache\\The Wild Robot - 2024.360p.OSN+.VIP.WEB-DL.H264.AAC.en-ddp.m4a'
🎯 MAIN: Received download_completed signal: True - C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\downloads\The Wild Robot - 2024\The Wild Robot - 2024.360p.OSN+.WEB-DL.H264.AAC.mkv
🧹 Cache directory C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\cache has been deleted.
📊 Gradual Progress: 65% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 65% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 65%
🎯 UI Progress Updated: 65% - 📥 Download completed, verifying files...
📊 Gradual Progress: 66% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 66% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 66%
🎯 UI Progress Updated: 66% - 📥 Download completed, verifying files...
📊 Gradual Progress: 67% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 67% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 67%
🎯 UI Progress Updated: 67% - 📥 Download completed, verifying files...
📊 Gradual Progress: 68% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 68% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 68%
🎯 UI Progress Updated: 68% - 📥 Download completed, verifying files...
📊 Gradual Progress: 69% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 69% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 69%
🎯 UI Progress Updated: 69% - 📥 Download completed, verifying files...
📊 Gradual Progress: 70% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 70% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 70%
🎯 UI Progress Updated: 70% - 📥 Download completed, verifying files...
📊 Gradual Progress: 71% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 71% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 71%
🎯 UI Progress Updated: 71% - 📥 Download completed, verifying files...
📊 Gradual Progress: 72% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 72% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 72%
🎯 UI Progress Updated: 72% - 📥 Download completed, verifying files...
📊 Gradual Progress: 73% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 73% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 73%
🎯 UI Progress Updated: 73% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 74% - 📥 Download completed, verifying files...
📊 Gradual Progress: 74% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 74%
🎯 UI Progress Updated: 74% - 📥 Download completed, verifying files...
📊 Gradual Progress: 75% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 75% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 75%
🎯 UI Progress Updated: 75% - 📥 Download completed, verifying files...
📊 Gradual Progress: 76% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 76% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 76%
🎯 UI Progress Updated: 76% - 📥 Download completed, verifying files...
📊 Gradual Progress: 77% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 77% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 77%
🎯 UI Progress Updated: 77% - 📥 Download completed, verifying files...
📊 Gradual Progress: 78% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 78% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 78%
🎯 UI Progress Updated: 78% - 📥 Download completed, verifying files...
📊 Gradual Progress: 79% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 79% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 79%
🎯 UI Progress Updated: 79% - 📥 Download completed, verifying files...
📊 Gradual Progress: 80% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 80% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 80%
🎯 UI Progress Updated: 80% - 📥 Download completed, verifying files...
📊 Gradual Progress: 81% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 81% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 81%
🎯 UI Progress Updated: 81% - 📥 Download completed, verifying files...
📊 Gradual Progress: 82% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 82% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 82%
🎯 UI Progress Updated: 82% - 📥 Download completed, verifying files...
📊 Gradual Progress: 83% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 83% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 83%
🎯 UI Progress Updated: 83% - 📥 Download completed, verifying files...
📊 Gradual Progress: 84% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 84% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 84%
🎯 UI Progress Updated: 84% - 📥 Download completed, verifying files...
📊 Gradual Progress: 85% - 📥 Download completed, verifying files...
🎯 MAIN: Received download_progress signal: 85% - 📥 Download completed, verifying files...
🎯 Updated downloads table progress for row 0: 85%
🎯 UI Progress Updated: 85% - 📥 Download completed, verifying files...