﻿LOG 2025/06/08
Save Path: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\binaries\Logs
Task Start: 2025/06/08 23:06:21
Task CommandLine: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\binaries\N_m3u8DL-RE.exe https://osn-video.anghcdn.co/public/154179-57503-PR681509-BX-AS045367-283435-a2bcb382d51e5bbfa901c5b763fa53c3-1749045009/stream.mpd?token=P2Fkcy1lbmFibGVkPTAmY2xpZW50LXBsYXRmb3JtPXdlYi1vc24mY2xpZW50LXZlcnNpb249MS4xLjEmY29udGVudC1pZD01NzUwMyZleHBpcnk9MTc0OTQ1NjM3NyZzaWduYXR1cmU9MDQ4YjUwODI3ODQ3NTM5NDY3ZTk3ZmQ1NTFmODkyOTdlODUwMjM3ZiZzdHJlYW0taWQ9MTI4MDA5JnVzZXItaWQ9MTAzMDI2MDY5 -mt --select-video id=79b4173d-a247-400d-acab-b301e09ca7fd --select-audio lang=ar --drop-subtitle .* --tmp-dir C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\cache --save-dir C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\cache --save-name "Al Mushardoon S01E28.720p.OSN+.WEB-DL.H264.AAC" --decryption-binary-path=C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\binaries\mp4decrypt.exe --key-text-file=C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\KEYS\OSNPLUS_KEYS.txt --log-level OFF

23:06:21.601 EXTRA: ffmpeg => C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\binaries\ffmpeg.exe
23:06:21.947 EXTRA: DropSubtitleFilter => For: best
23:06:21.948 EXTRA: VideoFilter => GroupIdReg: 79b4173d-a247-400d-acab-b301e09ca7fd For: best
23:06:21.948 EXTRA: AudioFilter => LanguageReg: ar For: best
