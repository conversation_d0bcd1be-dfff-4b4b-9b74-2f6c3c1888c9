﻿LOG 2025/05/29
Save Path: F:\TEEFA\MBC\shahid_template\binaries\Logs
Task Start: 2025/05/29 03:32:48
Task CommandLine: F:\TEEFA\MBC\shahid_template\binaries\N_m3u8DL-RE.exe https://mbcvod-enc.edgenextcdn.net/out/v1/31ce2c70254640b4865ae054c9f2c46d/22166333d51a4d528478144cc30fc048/65aee16b2775429e8add39c54f7b6995/index.mpd?aws.manifestfilter=video_height:144-1080;video_codec:H264&video_height=144-1080&video_codec=H264 -sv id=4:for=best --select-audio lang=ar:for=all --sub-only=false --tmp-dir F:\TEEFA\MBC\shahid_template\cache\episode_12 --save-dir F:\TEEFA\MBC\shahid_template\cache\episode_12 --save-name "<PERSON><PERSON> Adad ++.S03.E14.468p.SHAHID.VIP.WEB-DL.H264.AAC" --base-url https://mbcvod-enc.edgenextcdn.net/out/v1/31ce2c70254640b4865ae054c9f2c46d/22166333d51a4d528478144cc30fc048/65aee16b2775429e8add39c54f7b6995/ --decryption-binary-path=F:\TEEFA\MBC\shahid_template\binaries\mp4decrypt.exe --key-text-file=F:\TEEFA\MBC\shahid_template\KEYS\KEYS.txt --log-level INFO --binary-merge --thread-count 16 --download-retry-count 3 --sub-format SRT -mt

03:32:48.728 INFO : N_m3u8DL-RE (Beta version) 20230628
03:32:48.738 EXTRA: ffmpeg => F:\TEEFA\MBC\shahid_template\binaries\ffmpeg.exe
03:32:48.738 INFO : Loading URL: https://mbcvod-enc.edgenextcdn.net/out/v1/31ce2c70254640b4865ae054c9f2c46d/22166333d51a4d528478144cc30fc048/65aee16b2775429e8add39c54f7b6995/index.mpd?aws.manifestfilter=video_height:144-1080;video_codec:H264&video_height=144-1080&video_codec=H264
03:32:48.780 INFO : Content Matched: Dynamic Adaptive Streaming over HTTP
03:32:48.781 INFO : Parsing streams...
03:32:48.876 WARN : Writing meta json
03:32:48.895 INFO : Extracted, there are 40 streams, with 10 basic streams, 6 audio streams, 24 subtitle streams
03:32:48.900 INFO : Vid *CENC 1920x1080 | 2600 Kbps | 2 | 25 | avc1.640028 | 457 Segments | ~45m40s
03:32:48.902 INFO : Vid *CENC 1280x720 | 1287 Kbps | 3 | 25 | avc1.4D401F | 457 Segments | ~45m40s
03:32:48.902 INFO : Vid *CENC 1024x576 | 1012 Kbps | 1 | 25 | avc1.4D401F | 457 Segments | ~45m40s
03:32:48.903 INFO : Vid *CENC 832x468 | 610 Kbps | 4 | 25 | avc1.4D401F | 457 Segments | ~45m40s
03:32:48.903 INFO : Vid *CENC 640x360 | 461 Kbps | 5 | 25 | avc1.4D401E | 457 Segments | ~45m40s
03:32:48.904 INFO : Vid *CENC 512x288 | 368 Kbps | 6 | 25 | avc1.4D401E | 457 Segments | ~45m40s
03:32:48.908 INFO : Vid *CENC 448x252 | 333 Kbps | 7 | 25 | avc1.4D401E | 457 Segments | ~45m40s
03:32:48.909 INFO : Vid *CENC 448x252 | 318 Kbps | 8 | 25 | avc1.4D401E | 457 Segments | ~45m40s
03:32:48.909 INFO : Vid *CENC 256x144 | 219 Kbps | 9 | 25 | avc1.4D401E | 457 Segments | ~45m40s
03:32:48.909 INFO : Vid *CENC 256x144 | 149 Kbps | 10 | 25 | avc1.4D401E | 457 Segments | ~45m40s
03:32:48.910 INFO : Aud *CENC 11 | 127 Kbps | mp4a.40.2 | ar | 2CH | 457 Segments | ~45m40s
03:32:48.910 INFO : Aud *CENC 12 | 127 Kbps | mp4a.40.2 | qaa | 2CH | 457 Segments | ~45m40s
03:32:48.911 INFO : Aud *CENC 13 | 127 Kbps | mp4a.40.2 | qab | 2CH | 457 Segments | ~45m40s
03:32:48.912 INFO : Aud *CENC 14 | 127 Kbps | mp4a.40.2 | qac | 2CH | 457 Segments | ~45m40s
03:32:48.912 INFO : Aud *CENC 15 | 127 Kbps | mp4a.40.2 | qad | 2CH | 457 Segments | ~45m40s
03:32:48.914 INFO : Aud *CENC 16 | 127 Kbps | mp4a.40.2 | qae | 2CH | 457 Segments | ~45m40s
03:32:48.914 INFO : Sub 17 | en | stpp | 457 Segments | ~45m40s
03:32:48.915 INFO : Sub 18 | ar | stpp | 457 Segments | ~45m40s
03:32:48.915 INFO : Sub 19 | ar-fn | stpp | 457 Segments | ~45m40s
03:32:48.915 INFO : Sub 20 | en-fn | stpp | 457 Segments | ~45m40s
03:32:48.916 INFO : Sub 21 | fr-fn | stpp | 457 Segments | ~45m40s
03:32:48.916 INFO : Sub 22 | ja | stpp | 457 Segments | ~45m40s
03:32:48.916 INFO : Sub 23 | it | stpp | 457 Segments | ~45m40s
03:32:48.917 INFO : Sub 24 | bn | stpp | 457 Segments | ~45m40s
03:32:48.917 INFO : Sub 25 | ko | stpp | 457 Segments | ~45m40s
03:32:48.917 INFO : Sub 26 | no | stpp | 457 Segments | ~45m40s
03:32:48.918 INFO : Sub 27 | pl | stpp | 457 Segments | ~45m40s
03:32:48.918 INFO : Sub 28 | nl | stpp | 457 Segments | ~45m40s
03:32:48.918 INFO : Sub 29 | hi | stpp | 457 Segments | ~45m40s
03:32:48.919 INFO : Sub 30 | id | stpp | 457 Segments | ~45m40s
03:32:48.920 INFO : Sub 31 | pt | stpp | 457 Segments | ~45m40s
03:32:48.920 INFO : Sub 32 | tl | stpp | 457 Segments | ~45m40s
03:32:48.921 INFO : Sub 33 | tr | stpp | 457 Segments | ~45m40s
03:32:48.921 INFO : Sub 34 | ms | stpp | 457 Segments | ~45m40s
03:32:48.921 INFO : Sub 35 | ml | stpp | 457 Segments | ~45m40s
03:32:48.921 INFO : Sub 36 | el | stpp | 457 Segments | ~45m40s
03:32:48.922 INFO : Sub 37 | ct | stpp | 457 Segments | ~45m40s
03:32:48.922 INFO : Sub 38 | de | stpp | 457 Segments | ~45m40s
03:32:48.922 INFO : Sub 39 | ru | stpp | 457 Segments | ~45m40s
03:32:48.923 INFO : Sub 40 | es | stpp | 457 Segments | ~45m40s
03:32:48.923 EXTRA: VideoFilter => GroupIdReg: 4 For: best
03:32:48.923 EXTRA: AudioFilter => LanguageReg: ar For: all
03:32:48.923 INFO : Parsing streams...
03:32:48.924 INFO : Selected streams:
03:32:48.924 INFO : Vid *CENC 832x468 | 610 Kbps | 4 | 25 | avc1.4D401F | 457 Segments | ~45m40s
03:32:48.925 INFO : Aud *CENC 11 | 127 Kbps | mp4a.40.2 | ar | 2CH | 457 Segments | ~45m40s
03:32:48.926 WARN : Writing meta json
03:32:48.927 INFO : Save Name: Kamel El Adad ++.S03.E14.468p.SHAHID.VIP.WEB-DL.H264.AAC
03:32:48.928 INFO : Start downloading...Aud 11 | 127 Kbps | mp4a.40.2 | ar | 2CH
03:32:48.928 INFO : Start downloading...Vid 832x468 | 610 Kbps | 4 | 25 | avc1.4D401F
03:32:48.945 WARN : Type: cenc
03:32:48.946 WARN : PSSH(WV): CAESEJsBFN9uTk1uo8QgY//hgQ4aBWluc3lzIiQ5YjAxMTRkZi02ZTRlLTRkNmUtYTNjNC0yMDYzZmZlMTgxMGUqAlNEMgA=
03:32:48.946 WARN : KID: 9b0114df6e4e4d6ea3c42063ffe1810e
03:32:48.947 INFO : Trying to search for KEY from text file...
03:32:48.947 INFO : OK 9b0114df6e4e4d6ea3c42063ffe1810e:005bde6bdf3fa8d59334cc326c4a324a
03:32:48.947 WARN : Reading media info...
03:32:48.953 WARN : Type: cenc
03:32:48.954 WARN : PSSH(WV): CAESEJsBFN9uTk1uo8QgY//hgQ4aBWluc3lzIiQ5YjAxMTRkZi02ZTRlLTRkNmUtYTNjNC0yMDYzZmZlMTgxMGUqAlNEMgA=
03:32:48.956 WARN : KID: 9b0114df6e4e4d6ea3c42063ffe1810e
03:32:48.956 INFO : Trying to search for KEY from text file...
03:32:48.957 INFO : OK 9b0114df6e4e4d6ea3c42063ffe1810e:005bde6bdf3fa8d59334cc326c4a324a
03:32:48.957 WARN : Reading media info...
03:32:48.971 INFO : [0x1]: Audio, aac (mp4a)
03:32:48.978 INFO : [0x1]: Video, h264 (avc1), 832x468
03:32:50.468 INFO : New version detected! v0.3.0-beta
03:33:16.197 INFO : Binary merging...
03:33:16.348 INFO : Decrypting...
03:33:42.001 INFO : Binary merging...
03:33:42.229 INFO : Decrypting...
03:33:42.984 INFO : Done
