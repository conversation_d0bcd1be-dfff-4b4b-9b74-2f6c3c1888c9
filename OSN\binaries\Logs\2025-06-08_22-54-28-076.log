﻿LOG 2025/06/08
Save Path: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\binaries\Logs
Task Start: 2025/06/08 22:54:28
Task CommandLine: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\binaries\N_m3u8DL-RE.exe https://osn-video.anghcdn.co/public/154171-57500-PR681507-BX-AS045365-283432-d7445b0fc8555dfd28be5066965ebdbb-1749042608/stream.mpd?token=P2Fkcy1lbmFibGVkPTAmY2xpZW50LXBsYXRmb3JtPXdlYi1vc24mY2xpZW50LXZlcnNpb249MS4xLjEmY29udGVudC1pZD01NzUwMCZleHBpcnk9MTc0OTQ1NTY2NSZzaWduYXR1cmU9YWQ3Mjg5ZWZjNmVhMGQ5ZDNlNTFmYmYxMDJhZDZlNTZlNTUwZDM2NCZzdHJlYW0taWQ9MTI4MDAzJnVzZXItaWQ9MTAzMDI2MDY5 -mt --select-video id=81d25b31-e8d7-42e5-a564-aa9f52c173b5 --select-audio lang=ar --drop-subtitle .* --tmp-dir C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\cache --save-dir C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\cache --save-name "Al Mushardoon S01E26.720p.OSN+.WEB-DL.H264.AAC" --decryption-binary-path=C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\binaries\mp4decrypt.exe --key-text-file=C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\KEYS\OSNPLUS_KEYS.txt --log-level OFF

22:54:28.079 EXTRA: ffmpeg => C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\binaries\ffmpeg.exe
22:54:28.449 EXTRA: DropSubtitleFilter => For: best
22:54:28.449 EXTRA: VideoFilter => GroupIdReg: 81d25b31-e8d7-42e5-a564-aa9f52c173b5 For: best
22:54:28.449 EXTRA: AudioFilter => LanguageReg: ar For: best
