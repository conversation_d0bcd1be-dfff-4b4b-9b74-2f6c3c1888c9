﻿LOG 2025/05/29
Save Path: F:\TEEFA\MBC\shahid_template\binaries\Logs
Task Start: 2025/05/29 03:31:36
Task CommandLine: F:\TEEFA\MBC\shahid_template\binaries\N_m3u8DL-RE.exe https://mbc-aws-ksa.mncdn.com/out/v1/f1394e4e537d44878f94d0f1c29aaf38/7c201da305b543e0ae55a3673f9aa3a5/840aa317c6f34c8fb0ef18117f07c496/index.mpd?aws.manifestfilter=video_height:144-1080;video_codec:H264&video_height=144-1080&video_codec=H264 -sv id=4:for=best --select-audio lang=ar:for=all --sub-only=false --tmp-dir F:\TEEFA\MBC\shahid_template\cache\episode_11 --save-dir F:\TEEFA\MBC\shahid_template\cache\episode_11 --save-name "<PERSON><PERSON>d ++.S03.E13.468p.SHAHID.VIP.WEB-DL.H264.AAC" --base-url https://mbc-aws-ksa.mncdn.com/out/v1/f1394e4e537d44878f94d0f1c29aaf38/7c201da305b543e0ae55a3673f9aa3a5/840aa317c6f34c8fb0ef18117f07c496/ --decryption-binary-path=F:\TEEFA\MBC\shahid_template\binaries\mp4decrypt.exe --key-text-file=F:\TEEFA\MBC\shahid_template\KEYS\KEYS.txt --log-level INFO --binary-merge --thread-count 16 --download-retry-count 3 --sub-format SRT -mt

03:31:36.966 INFO : N_m3u8DL-RE (Beta version) 20230628
03:31:36.976 EXTRA: ffmpeg => F:\TEEFA\MBC\shahid_template\binaries\ffmpeg.exe
03:31:36.976 INFO : Loading URL: https://mbc-aws-ksa.mncdn.com/out/v1/f1394e4e537d44878f94d0f1c29aaf38/7c201da305b543e0ae55a3673f9aa3a5/840aa317c6f34c8fb0ef18117f07c496/index.mpd?aws.manifestfilter=video_height:144-1080;video_codec:H264&video_height=144-1080&video_codec=H264
03:31:37.129 INFO : Content Matched: Dynamic Adaptive Streaming over HTTP
03:31:37.130 INFO : Parsing streams...
03:31:37.221 WARN : Writing meta json
03:31:37.240 INFO : Extracted, there are 40 streams, with 10 basic streams, 6 audio streams, 24 subtitle streams
03:31:37.241 INFO : Vid *CENC 1920x1080 | 2594 Kbps | 2 | 25 | avc1.640028 | 464 Segments | ~46m24s
03:31:37.242 INFO : Vid *CENC 1280x720 | 1479 Kbps | 3 | 25 | avc1.4D401F | 464 Segments | ~46m24s
03:31:37.242 INFO : Vid *CENC 1024x576 | 1119 Kbps | 1 | 25 | avc1.4D401F | 464 Segments | ~46m24s
03:31:37.243 INFO : Vid *CENC 832x468 | 698 Kbps | 4 | 25 | avc1.4D401F | 464 Segments | ~46m24s
03:31:37.243 INFO : Vid *CENC 640x360 | 519 Kbps | 5 | 25 | avc1.4D401E | 464 Segments | ~46m24s
03:31:37.244 INFO : Vid *CENC 512x288 | 408 Kbps | 6 | 25 | avc1.4D401E | 464 Segments | ~46m24s
03:31:37.244 INFO : Vid *CENC 448x252 | 354 Kbps | 7 | 25 | avc1.4D401E | 464 Segments | ~46m24s
03:31:37.244 INFO : Vid *CENC 448x252 | 324 Kbps | 8 | 25 | avc1.4D401E | 464 Segments | ~46m24s
03:31:37.245 INFO : Vid *CENC 256x144 | 219 Kbps | 9 | 25 | avc1.4D401E | 464 Segments | ~46m24s
03:31:37.245 INFO : Vid *CENC 256x144 | 149 Kbps | 10 | 25 | avc1.4D401E | 464 Segments | ~46m24s
03:31:37.246 INFO : Aud *CENC 11 | 127 Kbps | mp4a.40.2 | ar | 2CH | 464 Segments | ~46m24s
03:31:37.247 INFO : Aud *CENC 12 | 127 Kbps | mp4a.40.2 | qaa | 2CH | 464 Segments | ~46m24s
03:31:37.247 INFO : Aud *CENC 13 | 127 Kbps | mp4a.40.2 | qab | 2CH | 464 Segments | ~46m24s
03:31:37.248 INFO : Aud *CENC 14 | 127 Kbps | mp4a.40.2 | qac | 2CH | 464 Segments | ~46m24s
03:31:37.248 INFO : Aud *CENC 15 | 127 Kbps | mp4a.40.2 | qad | 2CH | 464 Segments | ~46m24s
03:31:37.249 INFO : Aud *CENC 16 | 127 Kbps | mp4a.40.2 | qae | 2CH | 464 Segments | ~46m24s
03:31:37.249 INFO : Sub 17 | en | stpp | 464 Segments | ~46m24s
03:31:37.249 INFO : Sub 18 | ar | stpp | 464 Segments | ~46m24s
03:31:37.250 INFO : Sub 19 | ar-fn | stpp | 464 Segments | ~46m24s
03:31:37.250 INFO : Sub 20 | en-fn | stpp | 464 Segments | ~46m24s
03:31:37.250 INFO : Sub 21 | fr-fn | stpp | 464 Segments | ~46m24s
03:31:37.250 INFO : Sub 22 | ml | stpp | 464 Segments | ~46m24s
03:31:37.251 INFO : Sub 23 | pl | stpp | 464 Segments | ~46m24s
03:31:37.251 INFO : Sub 24 | bn | stpp | 464 Segments | ~46m24s
03:31:37.251 INFO : Sub 25 | ms | stpp | 464 Segments | ~46m24s
03:31:37.252 INFO : Sub 26 | ru | stpp | 464 Segments | ~46m24s
03:31:37.252 INFO : Sub 27 | hi | stpp | 464 Segments | ~46m24s
03:31:37.252 INFO : Sub 28 | ct | stpp | 464 Segments | ~46m24s
03:31:37.253 INFO : Sub 29 | el | stpp | 464 Segments | ~46m24s
03:31:37.253 INFO : Sub 30 | ja | stpp | 464 Segments | ~46m24s
03:31:37.254 INFO : Sub 31 | tr | stpp | 464 Segments | ~46m24s
03:31:37.254 INFO : Sub 32 | es | stpp | 464 Segments | ~46m24s
03:31:37.254 INFO : Sub 33 | ko | stpp | 464 Segments | ~46m24s
03:31:37.254 INFO : Sub 34 | no | stpp | 464 Segments | ~46m24s
03:31:37.255 INFO : Sub 35 | nl | stpp | 464 Segments | ~46m24s
03:31:37.255 INFO : Sub 36 | tl | stpp | 464 Segments | ~46m24s
03:31:37.255 INFO : Sub 37 | pt | stpp | 464 Segments | ~46m24s
03:31:37.256 INFO : Sub 38 | de | stpp | 464 Segments | ~46m24s
03:31:37.256 INFO : Sub 39 | it | stpp | 464 Segments | ~46m24s
03:31:37.256 INFO : Sub 40 | id | stpp | 464 Segments | ~46m24s
03:31:37.257 EXTRA: VideoFilter => GroupIdReg: 4 For: best
03:31:37.257 EXTRA: AudioFilter => LanguageReg: ar For: all
03:31:37.257 INFO : Parsing streams...
03:31:37.258 INFO : Selected streams:
03:31:37.259 INFO : Vid *CENC 832x468 | 698 Kbps | 4 | 25 | avc1.4D401F | 464 Segments | ~46m24s
03:31:37.259 INFO : Aud *CENC 11 | 127 Kbps | mp4a.40.2 | ar | 2CH | 464 Segments | ~46m24s
03:31:37.260 WARN : Writing meta json
03:31:37.261 INFO : Save Name: Kamel El Adad ++.S03.E13.468p.SHAHID.VIP.WEB-DL.H264.AAC
03:31:37.262 INFO : Start downloading...Vid 832x468 | 698 Kbps | 4 | 25 | avc1.4D401F
03:31:37.262 INFO : Start downloading...Aud 11 | 127 Kbps | mp4a.40.2 | ar | 2CH
03:31:37.318 INFO : New version detected! v0.3.0-beta
03:31:37.334 WARN : Type: cenc
03:31:37.339 WARN : PSSH(WV): CAESEGGM5nbnc0+brZPStbb9E1waBWluc3lzIhIzMDEwMDM3MTA0X3N0eG1sNGsyAA==
03:31:37.339 WARN : KID: 618ce676e7734f9bad93d2b5b6fd135c
03:31:37.340 INFO : Trying to search for KEY from text file...
03:31:37.340 INFO : OK 618ce676e7734f9bad93d2b5b6fd135c:62a0aa64bd05f6ec90f3574514e2de8c
03:31:37.340 WARN : Reading media info...
03:31:37.366 INFO : [0x1]: Video, h264 (avc1), 832x468
03:31:37.449 WARN : Type: cenc
03:31:37.454 WARN : PSSH(WV): CAESEGGM5nbnc0+brZPStbb9E1waBWluc3lzIhIzMDEwMDM3MTA0X3N0eG1sNGsyAA==
03:31:37.455 WARN : KID: 618ce676e7734f9bad93d2b5b6fd135c
03:31:37.455 INFO : Trying to search for KEY from text file...
03:31:37.456 INFO : OK 618ce676e7734f9bad93d2b5b6fd135c:62a0aa64bd05f6ec90f3574514e2de8c
03:31:37.456 WARN : Reading media info...
03:31:37.482 INFO : [0x1]: Audio, aac (mp4a)
03:32:07.192 INFO : Binary merging...
03:32:07.324 INFO : Decrypting...
03:32:41.301 INFO : Binary merging...
03:32:41.568 INFO : Decrypting...
03:32:42.602 INFO : Done
