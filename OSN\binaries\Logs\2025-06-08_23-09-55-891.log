﻿LOG 2025/06/08
Save Path: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\binaries\Logs
Task Start: 2025/06/08 23:09:55
Task CommandLine: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\binaries\N_m3u8DL-RE.exe https://osn-video.anghcdn.co/public/154173-57501-PR681510-BX-AS045368-283433-312787828d560d475684214aca6be65f-1749044411/stream.mpd?token=P2Fkcy1lbmFibGVkPTAmY2xpZW50LXBsYXRmb3JtPXdlYi1vc24mY2xpZW50LXZlcnNpb249MS4xLjEmY29udGVudC1pZD01NzUwMSZleHBpcnk9MTc0OTQ1NjM3OCZzaWduYXR1cmU9Y2ViOTE4ZGNiY2NjNTE2NGRkMDM2NTQzNjRiN2QwNjQ0YmVmOWZjNCZzdHJlYW0taWQ9MTI4MDA3JnVzZXItaWQ9MTAzMDI2MDY5 -mt --select-video id=de57ae15-969c-40b7-9a14-ae915082e6b9 --select-audio lang=ar --drop-subtitle .* --tmp-dir C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\cache --save-dir C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\cache --save-name "Al Mushardoon S01E29.720p.OSN+.WEB-DL.H264.AAC" --decryption-binary-path=C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\binaries\mp4decrypt.exe --key-text-file=C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\KEYS\OSNPLUS_KEYS.txt --log-level OFF

23:09:55.896 EXTRA: ffmpeg => C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\binaries\ffmpeg.exe
23:09:56.277 EXTRA: DropSubtitleFilter => For: best
23:09:56.277 EXTRA: VideoFilter => GroupIdReg: de57ae15-969c-40b7-9a14-ae915082e6b9 For: best
23:09:56.277 EXTRA: AudioFilter => LanguageReg: ar For: best
