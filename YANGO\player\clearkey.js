/**
 * Video Player MPD/M3U8/M3U/EPG Helper/Converter
 *
 * <AUTHOR>
 * @license Video Player MPD/M3U8/M3U/EPG © 2023 by Sharkiller is licensed under CC BY-NC-ND 4.0.
 * To view a copy of this license, visit https://creativecommons.org/licenses/by-nc-nd/4.0/
 */
// Wrap the original code in a function or wait for DOMContentLoaded
document.addEventListener('DOMContentLoaded', () => {

    // --- START: Added code for URL parameter handling ---
    function populateFromUrlParams() {
        const urlParams = new URLSearchParams(window.location.search);
        const mpdUrlFromParam = urlParams.get('mpd_url');
        const clearKeyDataFromParam = urlParams.get('clearkey_data'); // Expects "kid:key"

        let populated = false;

        if (mpdUrlFromParam) {
            const channelUrlInput = document.querySelector('input[name="channel-url"]');
            if (channelUrlInput) {
                console.log("Populating channel-url from URL parameter:", mpdUrlFromParam);
                channelUrlInput.value = mpdUrlFromParam;
                populated = true;
            } else {
                console.error('Input with name="channel-url" not found.');
            }
        }

        if (clearKeyDataFromParam) {
            const clearKeyInput = document.querySelector('input[name="drm-clearkey"]');
            if (clearKeyInput) {
                 console.log("Populating drm-clearkey from URL parameter:", clearKeyDataFromParam);
                clearKeyInput.value = clearKeyDataFromParam;
                populated = true;
            } else {
                console.error('Input with name="drm-clearkey" not found.');
            }
        }
        return populated; // Return true if any field was populated
    }
    // --- END: Added code for URL parameter handling ---


    // --- Original Code Starts Here (modified slightly) ---
    class a {
        static toUint8(e, t = 0, r = 1 / 0) { return this.view_(e, t, r, Uint8Array) }
        static view_(e, t, r, a) { var n = this.unsafeGetArrayBuffer_(e), s = (e.byteOffset || 0) + e.byteLength, e = (e.byteOffset || 0) + t, t = Math.max(0, Math.min(e, s)); return new a(n, t, Math.min(t + Math.max(r, 0), s) - t) }
        static unsafeGetArrayBuffer_(e) { return e instanceof ArrayBuffer ? e : e.buffer }
    }
    class t {
        parsed;
        constructor(e) { this.resetParsed(), this.data = e, this.drmClearkeys(), this.drmWidevine(), this.drmWidevineCert(), this.headers(), this.channelInfo() }
        getParsed() { return this.parsed }
        resetParsed() { this.parsed = { clearkeys: !1, widevine: !1, widevine_cert: !1, headers: !1, channel: !1 } }
        drmClearkeys() {
            // Using original inner class definition directly
            this.parsed.clearkeys = class {
                static extractClearkeys(e) {
                    e = e.trim(); let t = { url: !1, hex: !1, base: !1 }; if (/^https?/.test(e)) { t.url = e; var r = new XMLHttpRequest; try { if (r.open("GET", e, !1), r.send(), 200 !== r.status) return t } catch (e) { return t } e = r.responseText.trim() } try { if (/[ {"']*([0-9a-f]{32})[ "']*:[ "']*([0-9a-f]{32})[ }"']*/i.test(e)) { t.hex = {}, t.base = {}; for (const n of e.matchAll(/[ {"']*([0-9a-f]{32})[ "']*:[ "']*([0-9a-f]{32})[ }"']*/gi)) t.hex[n[1]] = n[2], t.base[this.hex2base(n[1])] = this.hex2base(n[2]); return t } } catch (e) { return !!t.url && t } try { var a = JSON.parse(e); return Array.isArray(a.keys) ? (t.hex = {}, t.base = {}, a.keys.forEach(e => { t.base[e.kid] = e.k, t.hex[this.base2hex(e.kid)] = this.base2hex(e.k) }), t) : !!t.url && t } catch (e) { return !!t.url && t }
                }
                static hex2base(e) { return this.toBase64(this.fromHex(e)) }
                static base2hex(e) { return this.toHex(this.fromBase64(e)) }
                static toStandardBase64(e) { e = String.fromCharCode.apply(null, a.toUint8(e)); return btoa(e) }
                static toBase64(e) { return this.toStandardBase64(e).replace(/\+/g, "-").replace(/\//g, "_").replace(/=*$/, "") }
                static fromBase64(e) { e = e.replace(/-/g, "+").replace(/_/g, "/"); var t = window.atob(e), r = new Uint8Array(t.length); for (let e = 0; e < t.length; ++e)r[e] = t.charCodeAt(e); return r }
                static toHex(e) { var t; let r = ""; for (t of a.toUint8(e))1 === (t = t.toString(16)).length && (t = "0" + t), r += t; return r }
                static fromHex(t) { var r = t.length / 2, a = new Uint8Array(r); for (let e = 0; e < r; e++)a[e] = window.parseInt(t.substring(2 * e, 2 * e + 2), 16); return a }
            }.extractClearkeys(this.data.get("drm-clearkey") || "") // Added || "" safety
        }
        drmWidevine() { var e = (this.data.get("drm-widevine") || "").trim(); this.parsed.widevine = !!/^https?:\/\/[^"]+$/.test(e) && e }
        drmWidevineCert() { var e = (this.data.get("drm-widevine-cert") || "").trim(); this.parsed.widevine_cert = !!/^https?:\/\/[^"]+$/.test(e) && e }
        headers() {
            let r = {}; var e = this.data.getAll("headers-name"); let a = this.data.getAll("headers-value"); const n = /^[\w-]+$/, s = /^[\w\s:;.,/"'?!(){}@<>=+#$&`|~^*%[\]\-]+$/; e.forEach((e, t) => { n.test(e.trim()) && s.test(a[t].trim()) && (r[e.trim()] = a[t].trim()) }), this.parsed.headers = 0 < Object.keys(r).length && r
        }
        channelInfo() {
            var e = { name: !1, tvg_id: !1, group: !1, logo: !1, url: !1, type: !1 }, t = (this.data.get("channel-name") || "").trim(), t = (/^[^"]+$/.test(t) && (e.name = t), (this.data.get("channel-tvg-id") || "").trim()), t = (/^[^"]+$/.test(t) && (e.tvg_id = t), (this.data.get("channel-group") || "").trim()), t = (/^[^"]+$/.test(t) && (e.group = t), (this.data.get("channel-logo") || "").trim()), t = (/^https?[^"]+$/.test(t) && (e.logo = t), (this.data.get("channel-url") || "").trim()); /^https?[^"]+$/.test(t) && !/\.(m3u8|mpd)(?:\?|$)/.test(e.url = t) && (e.type = this.data.get("channel-type")), this.parsed.channel = !!(e.name || e.tvg_id || e.group || e.logo || e.url) && e
        }
    }
    // --- Localization can stay as is ---
    ; (class { static replace(e, t) { var r = t.replace(/__MSG_(\w+)__/g, (e, t) => { if (t) { let e = chrome.i18n.getMessage(t); return e = 0 === e.length ? t : e } return "" }); r !== t && (e.innerHTML = r) } static translate() { let e, t; var r, a = document.querySelectorAll("[data-localize]"); for (r in a) a.hasOwnProperty(r) && (e = a[r], t = e.getAttribute("data-localize").toString(), this.replace(e, t)); var n = document.querySelector("body"); t = n.innerHTML.toString(), this.replace(n, t) } }).translate();

    let r = document.querySelector("#converter");
    if (!r) {
        console.error("Form with id='converter' not found!");
        return; // Stop if the form isn't present
    }

    let n = !1; // Timeout variable for debouncing

    // --- Main update function ---
    const s = () => {
        console.log("Running update function 's'");
        n = !1;
        var e = new FormData(r); // Read data from the form
        var parsedData = new t(e).getParsed(); // Parse form data
        console.log("Parsed Data:", parsedData);
        l(parsedData); // Update clearkey outputs
        c(parsedData); // Update direct link output
        o(parsedData); // Update M3U output
    };

    // --- Debounce function ---
    const i = e => {
        // Ignore modifier keys for keyup debouncing
        if (e instanceof window.KeyboardEvent && ["Shift", "Control", "Alt", "Tab", "Meta", "CapsLock", "Escape", "ArrowLeft", "ArrowRight", "ArrowUp", "ArrowDown"].includes(e.key)) {
           return;
        }
        console.log("Input changed, debouncing update...");
        n && window.clearTimeout(n); // Clear existing timeout
        n = window.setTimeout(s, 500); // Set new timeout (reduced delay slightly)
    };

    // --- Output update functions ---
    const l = e => {
        const hexOutput = document.querySelector("#clearkey-hex");
        const baseOutput = document.querySelector("#clearkey-base");
        if (!hexOutput || !baseOutput) {
             console.error("ClearKey output elements not found"); return;
        }
        if (e.clearkeys) {
            hexOutput.value = e.clearkeys.hex ? JSON.stringify(e.clearkeys.hex, null, 2) : ""; // Pretty print JSON
            if (e.clearkeys.base) {
                var t, r, a = { keys: [], type: "temporary" };
                for ([t, r] of Object.entries(e.clearkeys.base)) a.keys.push({ kty: "oct", kid: t, k: r });
                baseOutput.value = JSON.stringify(a, null, 2); // Pretty print JSON
            } else {
                baseOutput.value = "";
            }
        } else {
            hexOutput.value = "";
            baseOutput.value = "";
        }
         // Auto-adjust height
         [hexOutput, baseOutput].forEach(el => {
            el.style.height = "auto";
            el.style.height = el.scrollHeight + "px";
        });
    };
    const c = e => {
        const directLinkOutput = document.querySelector("#direct-link");
         if (!directLinkOutput) {
             console.error("Direct link output element not found"); return;
         }
        var t;
        if (e.channel?.url) {
            try {
                 t = new URL(location.origin + location.pathname); // Use current page as base, avoids errors if channel.url is relative? Or just use placeholder? Let's try current page base.
                 t.searchParams.set("src", e.channel.url); // Add the actual source URL as 'src'

                if(e.clearkeys) {
                    // Encode the original input string if possible, or the parsed hex JSON
                    const ckInputElement = document.querySelector('input[name="drm-clearkey"]');
                    const ckValue = ckInputElement ? ckInputElement.value : (e.clearkeys.hex ? JSON.stringify(e.clearkeys.hex) : '');
                    if(ckValue) t.searchParams.set("ck", window.btoa(ckValue)); // Base64 encode
                } else if (e.widevine) {
                    t.searchParams.set("wv", window.btoa(e.widevine));
                    if (e.widevine_cert) t.searchParams.set("wvc", window.btoa(e.widevine_cert));
                }
                if (e.headers) t.searchParams.set("headers", window.btoa(JSON.stringify(e.headers)));
                if (e.channel.type) t.searchParams.set("manifest_type", e.channel.type); // No encoding needed? Assuming simple value
                if (e.channel.logo) t.searchParams.set("image", window.btoa(e.channel.logo));
                if (e.channel.name) t.searchParams.set("title", encodeURIComponent(e.channel.name)); // Use URI component encoding for title

                directLinkOutput.value = t.toString();
            } catch (error) {
                console.error("Error creating direct link URL:", error);
                directLinkOutput.value = "Error creating URL";
            }

        } else {
            directLinkOutput.value = "";
        }
         // Auto-adjust height
         directLinkOutput.style.height = "auto";
         directLinkOutput.style.height = directLinkOutput.scrollHeight + "px";
    };
    const o = t => {
        var r = document.querySelector("#m3u");
         if (!r) {
             console.error("M3U output element not found"); return;
         }
        if (t.channel?.url && t.channel?.name) {
            let e = "";
            if (t.channel.type && (e += "#KODIPROP:inputstream.adaptive.file_type=" + t.channel.type + "\n"), t.clearkeys) {
                 e += "#KODIPROP:inputstream.adaptive.license_type=clearkey\n#KODIPROP:inputstream.adaptive.license_key=";
                // Use original input string if available, otherwise hex JSON
                 const ckInputElement = document.querySelector('input[name="drm-clearkey"]');
                 const ckValue = ckInputElement ? ckInputElement.value : (t.clearkeys.hex ? JSON.stringify(t.clearkeys.hex) : '');
                 e += ckValue + "\n";

            } else if (t.widevine) {
                e = (e += "#KODIPROP:inputstream.adaptive.license_type=com.widevine.alpha\n") + "#KODIPROP:inputstream.adaptive.license_key=" + t.widevine + "\n";
                if (t.widevine_cert) (e += "#KODIPROP:inputstream.adaptive.server_certificate=" + t.widevine_cert + "\n");
            }
            if (t.headers) for (var [a, n] of Object.entries(t.headers)) e += "#KODIPROP:inputstream.adaptive.stream_headers=" + a + "=" + n + "\n";
            e += "#EXTINF:-1";
            if (t.channel.tvg_id && (e += ' tvg-id="' + t.channel.tvg_id + '"'), t.channel.logo && (e += ' tvg-logo="' + t.channel.logo + '"'), t.channel.group && (e += ' group-title="' + t.channel.group + '"'), e = (e += "," + t.channel.name + "\n") + t.channel.url, r.value = e);
            else r.value = ""
        } else r.value = "";
        r.style.height = "auto", r.style.height = r.scrollHeight + "px"
    };

    // --- Setup Headers ---
    const headerList = document.querySelector("ul.headers");
    const headerTemplate = document.querySelector("#headers-template");

    if (!headerList || !headerTemplate) {
         console.error("Header list or template not found!");
    } else {
        const d = headerTemplate.content; // Template content

        // Function to add event listeners to a header row
        function setupHeaderRow(headerElement) {
            const nameInput = headerElement.querySelector('input[name="headers-name"]');
            const valueInput = headerElement.querySelector('input[name="headers-value"]');
            const removeButton = headerElement.querySelector(".remove-header");
            const clearButtons = headerElement.querySelectorAll(".form-clear");

            if (nameInput) nameInput.addEventListener("keyup", i);
            if (valueInput) valueInput.addEventListener("keyup", i);

            if (removeButton) {
                removeButton.addEventListener("click", e => {
                    e.currentTarget.closest(".form-floating.header-row")?.remove(); // Target the whole row
                    i(); // Trigger update after removing
                });
            }

            clearButtons.forEach(btn => {
                 btn.addEventListener("click", e => {
                    const inputToClear = e.currentTarget.closest(".form-double")?.querySelector("input");
                    if (inputToClear) inputToClear.value = "";
                    i(); // Trigger update after clearing
                });
            });
        }

        // Add initial header row and set it up
        const initialHeaderRow = d.cloneNode(true);
        headerList.appendChild(initialHeaderRow);
        setupHeaderRow(headerList.querySelector('.header-row:last-child')); // Setup the newly added row


        // Add Header Button
        const addHeaderButton = document.querySelector(".form-extra .add-header");
        if (addHeaderButton) {
             addHeaderButton.addEventListener("click", () => {
                var e = d.cloneNode(true);
                headerList.appendChild(e);
                setupHeaderRow(headerList.querySelector('.header-row:last-child')); // Setup the new row
            });
        } else {
            console.error("Add header button not found.");
        }
    }

    // --- Setup Event Listeners for Inputs and Buttons ---
    r.addEventListener("submit", e => { e.preventDefault(); s(); }); // Also update on submit explicitly
    document.querySelectorAll("#converter input, #converter select").forEach(e => {
        if (e.type === 'text' || e.type === 'url' || e.tagName === 'SELECT') {
             e.addEventListener("keyup", i);
             e.addEventListener("change", i); // Also trigger on change (for select, paste etc)
        }
    });

    // Clear All button
    const clearAllButton = document.querySelector(".form-extra .form-clear-all");
    if (clearAllButton) {
        clearAllButton.addEventListener("click", () => {
             document.querySelectorAll("#converter input").forEach(e => { e.value = ""; });
             document.querySelectorAll("ul.headers .header-row:not(:first-child)").forEach(row => row.remove()); // Remove extra headers
             i(); // Trigger update
        });
    }

    // General Clear buttons (already handled in setupHeaderRow and clearAll)
    // Need to handle clears for non-header inputs if any exist
     document.querySelectorAll(".form-clear:not(.header-clear)").forEach(e => { // Add :not(.header-clear) if header clears have a specific class
        e.addEventListener("click", e => {
            const inputElement = e.currentTarget.closest(".form-double,.form-floating")?.querySelector("input");
             if (inputElement) inputElement.value = "";
             i(); // Trigger update
        });
    });


     // Select change listener
     const channelTypeSelect = document.querySelector('select[name="channel-type"]');
     if (channelTypeSelect) channelTypeSelect.addEventListener("change", i);

    // Result buttons (Copy/Open)
    document.querySelectorAll(".result .result-open").forEach(btn => {
         btn.addEventListener("click", e => {
             const outputId = e.currentTarget.dataset.id;
             const outputElement = document.querySelector("#" + outputId);
             const value = outputElement ? outputElement.value : '';
             if (/^https?:\/\//.test(value)) {
                 window.open(value, "_blank");
             } else {
                 console.warn("Cannot open non-URL value:", value);
             }
        });
    });
    document.querySelectorAll(".result .result-copy").forEach(t => {
        t.addEventListener("click", () => {
            const outputId = t.dataset.id;
            const outputElement = document.querySelector("#" + outputId);
            const value = outputElement ? outputElement.value : '';
            if (navigator.clipboard && value) {
                navigator.clipboard.writeText(value)
                    .then(() => console.log("Copied to clipboard:", outputId))
                    .catch(err => console.error("Failed to copy:", err));
            } else if (value) {
                // Basic fallback for older browsers (less reliable)
                try {
                    outputElement.select();
                    document.execCommand('copy');
                    console.log("Copied to clipboard (fallback):", outputId);
                } catch (err) {
                     console.error("Fallback copy failed:", err);
                }
            }
        });
    });

    // --- Auto-populate and run initial update ---
    if (populateFromUrlParams()) {
        console.log("URL parameters processed, triggering initial update.");
        s(); // Run the update function immediately after populating
    } else {
        console.log("No URL parameters found, running default initial update.");
        s(); // Run update once on load anyway
    }

}); // End DOMContentLoaded