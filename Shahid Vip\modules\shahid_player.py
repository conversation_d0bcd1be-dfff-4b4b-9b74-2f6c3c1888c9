#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Shahid Player Module
This module handles playing MPD content with the custom player in the player directory
"""

import os
import threading
import urllib.parse
import http.server
import socketserver
from PySide6.QtCore import QObject, Signal, QUrl, Qt
from PySide6.QtWidgets import Q<PERSON>ialog, QVBoxLayout, QWidget, QHBoxLayout, QPushButton, QLabel
from PySide6.QtWebEngineWidgets import QWebEngineView
from PySide6.QtGui import QIcon

# Import player modules
try:
    from .cef_player import CEFPlayerWidget
    CEF_AVAILABLE = True
except ImportError as e:
    print(f"CEF Player not available: {e}")
    CEF_AVAILABLE = False

try:
    from .chrome_player import ChromePlayerWidget
    CHROME_AVAILABLE = True
except ImportError as e:
    print(f"Chrome Player not available: {e}")
    CHROME_AVAILABLE = False

try:
    from .browser_player import BrowserPlayerWidget
    BROWSER_AVAILABLE = True
except ImportError as e:
    print(f"Browser Player not available: {e}")
    BROWSER_AVAILABLE = False

class SimpleHTTPRequestHandlerWithDir(http.server.SimpleHTTPRequestHandler):
    """Custom HTTP request handler that serves files from a specific directory."""

    def __init__(self, *args, directory=None, **kwargs):
        self.directory = directory
        super().__init__(*args, **kwargs)

    def translate_path(self, path):
        """Translate a /-separated PATH to the local filename syntax."""
        # Remove query parameters
        path = path.split('?', 1)[0]
        path = path.split('#', 1)[0]

        # Normalize path
        path = urllib.parse.unquote(path)
        path = path.lstrip('/')

        # Join with the specified directory
        if self.directory:
            return os.path.join(self.directory, path)
        else:
            return super().translate_path(path)

    def log_message(self, format, *args):
        """Log HTTP requests."""
        print(f"[HTTP] {format % args}")

    def end_headers(self):
        """Add CORS headers to allow cross-origin requests."""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        # Add cache control headers for better performance
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        # Add headers for QWebEngineView compatibility
        self.send_header('X-Frame-Options', 'ALLOWALL')
        self.send_header('Content-Security-Policy', "default-src * 'unsafe-inline' 'unsafe-eval'; script-src * 'unsafe-inline' 'unsafe-eval'; connect-src * 'unsafe-inline'; img-src * data: blob: 'unsafe-inline'; frame-src *; style-src * 'unsafe-inline';")
        super().end_headers()

    def do_OPTIONS(self):
        """Handle OPTIONS requests for CORS preflight."""
        self.send_response(200)
        self.end_headers()

    def guess_type(self, path):
        """Guess the type of a file based on its URL."""
        mimetype, encoding = super().guess_type(path)

        # Add specific MIME types for player files
        if path.endswith('.js'):
            return 'application/javascript', encoding
        elif path.endswith('.css'):
            return 'text/css', encoding
        elif path.endswith('.html'):
            return 'text/html', encoding
        elif path.endswith('.mpd'):
            return 'application/dash+xml', encoding
        elif path.endswith('.m3u8'):
            return 'application/vnd.apple.mpegurl', encoding

        return mimetype, encoding

class PlayerSignals(QObject):
    """Signals for player events."""
    player_started = Signal(bool)  # success
    player_stopped = Signal()
    player_error = Signal(str)  # error message

class PlayerWindow(QDialog):
    """Window for displaying the Player."""
    def __init__(self, url, parent=None, on_close_callback=None):
        super().__init__(parent)
        self.setWindowTitle("Shahid Player - شاهد")
        self.resize(1280, 720)
        self.player_url = url  # Store URL for reference
        self.on_close_callback = on_close_callback  # Store callback function

        # Set window flags for better integration
        self.setWindowFlags(Qt.Window | Qt.WindowMaximizeButtonHint | Qt.WindowCloseButtonHint)

        # Set window icon if available
        try:
            icon_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'icon.ico')
            if os.path.exists(icon_path):
                self.setWindowIcon(QIcon(icon_path))
        except Exception as e:
            print(f"Could not set window icon: {e}")

        # Create layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)

        # Create web view
        self.web_view = QWebEngineView()

        # Set web view settings for better video playback
        settings = self.web_view.page().settings()
        try:
            from PySide6.QtWebEngineCore import QWebEngineSettings

            # Enable all necessary features for video playback
            settings.setAttribute(QWebEngineSettings.WebAttribute.PluginsEnabled, True)
            settings.setAttribute(QWebEngineSettings.WebAttribute.JavascriptEnabled, True)
            settings.setAttribute(QWebEngineSettings.WebAttribute.LocalContentCanAccessRemoteUrls, True)
            settings.setAttribute(QWebEngineSettings.WebAttribute.LocalContentCanAccessFileUrls, True)
            settings.setAttribute(QWebEngineSettings.WebAttribute.AllowRunningInsecureContent, True)
            settings.setAttribute(QWebEngineSettings.WebAttribute.LocalStorageEnabled, True)
            settings.setAttribute(QWebEngineSettings.WebAttribute.WebGLEnabled, True)
            settings.setAttribute(QWebEngineSettings.WebAttribute.Accelerated2dCanvasEnabled, True)
            settings.setAttribute(QWebEngineSettings.WebAttribute.AutoLoadImages, True)
            settings.setAttribute(QWebEngineSettings.WebAttribute.ErrorPageEnabled, True)
            settings.setAttribute(QWebEngineSettings.WebAttribute.FullScreenSupportEnabled, True)

            # Set user agent to mimic a regular browser
            profile = self.web_view.page().profile()
            profile.setHttpUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

            print("PlayerWindow web settings configured successfully")
        except Exception as e:
            print(f"Could not configure PlayerWindow web settings: {e}")

        # Connect signals for debugging
        try:
            self.web_view.loadStarted.connect(self._on_load_started)
            self.web_view.loadProgress.connect(self._on_load_progress)
            self.web_view.loadFinished.connect(self._on_load_finished)

            # Enable JavaScript console logging
            self.web_view.page().javaScriptConsoleMessage.connect(self._handle_js_console)
        except Exception as e:
            print(f"Warning: Could not connect web view signals: {e}")

        # Load the URL
        print(f"Loading URL in WebView: {url}")
        self.web_view.load(QUrl(url))
        layout.addWidget(self.web_view)

        # Set dialog to delete on close
        self.setAttribute(Qt.WA_DeleteOnClose)

    def _on_load_started(self):
        print("Web page load started")

    def _on_load_progress(self, progress):
        print(f"Web page load progress: {progress}%")

    def _on_load_finished(self, ok):
        print(f"Web page load finished: {'Success' if ok else 'Failed'}")


    def _handle_js_console(self, level, message, line, source):
        """Handle JavaScript console messages."""
        level_str = {
            0: "DEBUG",
            1: "INFO",
            2: "WARNING",
            3: "ERROR"
        }.get(level, "UNKNOWN")

        print(f"JS {level_str}: {message} (line {line}, source: {source})")

    def closeEvent(self, event):
        """Handle window close event."""
        print("Player window closeEvent called")
        # Clean up web view
        self.web_view.stop()
        self.web_view.page().deleteLater()
        self.web_view.deleteLater()

        # Call the callback function if provided
        if self.on_close_callback:
            print("Calling on_close_callback")
            self.on_close_callback()

        # Call parent class closeEvent
        super().closeEvent(event)

class SimpleHTTPRequestHandlerWithDir(http.server.SimpleHTTPRequestHandler):
    """Simple HTTP request handler with custom directory."""
    def __init__(self, *args, directory=None, **kwargs):
        self.directory = directory
        super().__init__(*args, directory=directory, **kwargs)

    def log_message(self, format, *args):
        """Override to customize logging."""
        print(f"[HTTP] {format % args}")

class EmbeddedPlayerWidget(QWebEngineView):
    """Embedded player widget that can be added to any layout."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.player_url = None

        # Set web view settings for better video playback
        try:
            settings = self.page().settings()
            from PySide6.QtWebEngineCore import QWebEngineSettings

            # Enable all necessary features for video playback
            settings.setAttribute(QWebEngineSettings.WebAttribute.PluginsEnabled, True)
            settings.setAttribute(QWebEngineSettings.WebAttribute.JavascriptEnabled, True)
            settings.setAttribute(QWebEngineSettings.WebAttribute.LocalContentCanAccessRemoteUrls, True)
            settings.setAttribute(QWebEngineSettings.WebAttribute.LocalContentCanAccessFileUrls, True)
            settings.setAttribute(QWebEngineSettings.WebAttribute.AllowRunningInsecureContent, True)
            settings.setAttribute(QWebEngineSettings.WebAttribute.LocalStorageEnabled, True)
            settings.setAttribute(QWebEngineSettings.WebAttribute.WebGLEnabled, True)
            settings.setAttribute(QWebEngineSettings.WebAttribute.Accelerated2dCanvasEnabled, True)
            settings.setAttribute(QWebEngineSettings.WebAttribute.AutoLoadImages, True)
            settings.setAttribute(QWebEngineSettings.WebAttribute.ErrorPageEnabled, True)
            settings.setAttribute(QWebEngineSettings.WebAttribute.FullScreenSupportEnabled, True)

            # Set user agent to mimic a regular browser
            from PySide6.QtWebEngineCore import QWebEngineProfile
            profile = self.page().profile()
            profile.setHttpUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

            # Allow all permissions (check if method exists)
            try:
                profile.setPermissionRequestInterceptor(self._permission_interceptor)
                print("Permission interceptor set successfully")
            except AttributeError:
                print("setPermissionRequestInterceptor not available in this Qt version")

            # Disable web security for local content
            profile.setHttpCacheType(profile.HttpCacheType.NoCache)

            # Inject early JavaScript to override JW Player before it loads
            early_js = """
                // Early injection to override JW Player license check
                console.log('Early JavaScript injection for JW Player compatibility');

                // Override navigator properties immediately
                Object.defineProperty(navigator, 'userAgent', {
                    get: function() {
                        return 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
                    },
                    configurable: true
                });

                Object.defineProperty(navigator, 'vendor', {
                    get: function() { return 'Google Inc.'; },
                    configurable: true
                });

                // Remove Qt properties immediately
                if (window.qt) delete window.qt;
                if (window.qmlEngine) delete window.qmlEngine;

                // Set up JW Player override before any scripts load
                window.jwplayerOverrideReady = true;

                console.log('Early JavaScript injection completed');
            """

            # Inject the script into all pages
            try:
                from PySide6.QtWebEngineCore import QWebEngineScript
                script = QWebEngineScript()
                script.setSourceCode(early_js)
                script.setName("EarlyJWPlayerFix")
                script.setWorldId(QWebEngineScript.ScriptWorldId.MainWorld)
                script.setInjectionPoint(QWebEngineScript.InjectionPoint.DocumentCreation)
                script.setRunsOnSubFrames(True)

                profile.scripts().insert(script)
                print("Early JavaScript injection script added to profile")
            except Exception as e:
                print(f"Could not add early JavaScript injection: {e}")

            print("Web settings configured successfully")
        except Exception as e:
            print(f"Could not configure web settings: {e}")

        # Connect additional signals for debugging
        try:
            if hasattr(self, 'loadStarted') and hasattr(self.loadStarted, 'connect'):
                self.loadStarted.connect(self._on_load_started)
                print("Connected loadStarted signal")

            if hasattr(self, 'loadProgress') and hasattr(self.loadProgress, 'connect'):
                self.loadProgress.connect(self._on_load_progress)
                print("Connected loadProgress signal")

            if hasattr(self.page(), 'javaScriptConsoleMessage'):
                self.page().javaScriptConsoleMessage.connect(self._on_js_console_message)
                print("Connected JavaScript console message signal")

            print("Debugging signals connected successfully")
        except Exception as e:
            print(f"Warning: Could not connect debugging signals: {e}")

        print("EmbeddedPlayerWidget initialized successfully")

    def _permission_interceptor(self, url, feature):
        """Allow all permissions for the player."""
        try:
            from PySide6.QtWebEngineCore import QWebEnginePermission
            print(f"Permission requested for {url}, feature: {feature}")
            # Allow all permissions
            return QWebEnginePermission.PermissionPolicy.Grant
        except Exception as e:
            print(f"Permission interceptor error: {e}")
            return None

    def createWindow(self, window_type):
        """Handle window creation requests (for popups, etc.)."""
        print(f"Window creation requested: {window_type}")
        # Return self to handle popups in the same view
        return self

    def load_player(self, url):
        """Load the player with the given URL."""
        try:
            self.player_url = url
            print(f"Loading player URL in embedded widget: {url}")

            # Connect to loadFinished to inject JavaScript
            self.loadFinished.connect(self._on_load_finished)

            self.load(QUrl(url))
            return True
        except Exception as e:
            print(f"Error loading player URL: {e}")
            return False

    def _on_load_finished(self, success):
        """Handle page load finished event."""
        if success:
            print("Page loaded successfully, injecting compatibility JavaScript...")
            # Inject JavaScript to override navigator properties and hide QWebEngineView
            js_code = """
                console.log('Injecting QWebEngineView compatibility fixes...');

                // Override navigator properties to hide QWebEngineView
                Object.defineProperty(navigator, 'userAgent', {
                    get: function() {
                        return 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
                    },
                    configurable: true
                });

                Object.defineProperty(navigator, 'vendor', {
                    get: function() { return 'Google Inc.'; },
                    configurable: true
                });

                Object.defineProperty(navigator, 'platform', {
                    get: function() { return 'Win32'; },
                    configurable: true
                });

                Object.defineProperty(navigator, 'appName', {
                    get: function() { return 'Netscape'; },
                    configurable: true
                });

                Object.defineProperty(navigator, 'appVersion', {
                    get: function() {
                        return '5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
                    },
                    configurable: true
                });

                // Remove Qt-specific properties
                if (window.qt) {
                    try { delete window.qt; } catch(e) { window.qt = undefined; }
                }
                if (window.qmlEngine) {
                    try { delete window.qmlEngine; } catch(e) { window.qmlEngine = undefined; }
                }

                // Override window.location.protocol if it's file:
                if (window.location.protocol === 'file:') {
                    Object.defineProperty(window.location, 'protocol', {
                        get: function() { return 'http:'; },
                        configurable: true
                    });
                }

                console.log('QWebEngineView compatibility fixes applied');
                console.log('Navigator userAgent:', navigator.userAgent);
                console.log('Navigator vendor:', navigator.vendor);
                console.log('Navigator platform:', navigator.platform);

                // Additional JW Player compatibility fixes
                window.isQWebEngineView = false;  // Hide QWebEngineView flag

                // Override any potential license checks
                if (window.jwplayer && typeof window.jwplayer === 'function') {
                    console.log('JW Player detected, applying additional fixes...');

                    // Store original jwplayer function
                    const originalJwplayer = window.jwplayer;

                    // Override jwplayer function
                    window.jwplayer = function(id) {
                        console.log('JW Player called with ID:', id);
                        const instance = originalJwplayer(id);

                        if (instance && instance.setup) {
                            const originalSetup = instance.setup;
                            instance.setup = function(config) {
                                console.log('JW Player setup called with config:', config);

                                // Force free license
                                config.key = 'FREE';
                                config.analytics = false;
                                config.advertising = false;
                                config.hlshtml5 = true;
                                config.primary = 'html5';

                                console.log('Modified JW Player config:', config);
                                return originalSetup.call(this, config);
                            };
                        }

                        return instance;
                    };

                    // Copy static properties
                    Object.keys(originalJwplayer).forEach(key => {
                        if (key !== 'prototype') {
                            window.jwplayer[key] = originalJwplayer[key];
                        }
                    });
                }

                console.log('All compatibility fixes applied successfully');
            """

            self.page().runJavaScript(js_code, self._on_js_result)
        else:
            print("Page failed to load")

    def _on_js_result(self, result):
        """Handle JavaScript execution result."""
        print(f"JavaScript injection result: {result}")
        # Disconnect the signal to avoid multiple injections
        try:
            self.loadFinished.disconnect(self._on_load_finished)
        except:
            pass

    def _on_load_started(self):
        """Handle load started event."""
        print("🔄 EmbeddedPlayerWidget: Load started")

    def _on_load_progress(self, progress):
        """Handle load progress event."""
        print(f"📊 EmbeddedPlayerWidget: Load progress: {progress}%")

    def _on_js_console_message(self, level, message, line, source):
        """Handle JavaScript console messages."""
        level_names = {0: "DEBUG", 1: "INFO", 2: "WARNING", 3: "ERROR"}
        level_name = level_names.get(level, "UNKNOWN")
        print(f"🔍 JS [{level_name}]: {message} (line {line}, source: {source})")

        # Check for specific JW Player errors
        if "102630" in message:
            print("❌ JW Player Error 102630 detected!")
        elif "jwplayer" in message.lower() and "error" in message.lower():
            print(f"❌ JW Player Error: {message}")
        elif "shaka" in message.lower() and "error" in message.lower():
            print(f"❌ Shaka Player Error: {message}")
        elif "ready" in message.lower():
            print(f"✅ Player Ready: {message}")

class ShahidPlayer:
    def __init__(self, parent=None):
        """
        Initialize the Shahid Player module.

        Args:
            parent (QWidget, optional): Parent widget for the player window
        """
        # Get the script directory (parent of the modules directory)
        self.dir_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.player_dir = os.path.join(self.dir_path, 'player')
        print(f"Player initialized with base directory: {self.dir_path}")
        print(f"Player directory: {self.player_dir}")

        self.signals = PlayerSignals()
        self.http_server = None
        self.server_thread = None
        self.webview_window = None
        self.embedded_widget = None
        self.parent = parent

    def _start_http_server(self):
        """
        Start a local HTTP server to serve the player files.

        Returns:
            tuple: (success, port)
        """
        # Try multiple ports if the default is busy
        for port in range(8002, 8010):
            try:
                print(f"Trying to start HTTP server on port {port}...")

                # Create and start the HTTP server
                handler = lambda *args, **kwargs: SimpleHTTPRequestHandlerWithDir(
                    *args,
                    directory=self.player_dir,
                    **kwargs
                )

                self.http_server = socketserver.TCPServer(("localhost", port), handler)
                self.http_server.allow_reuse_address = True

                # Start the server in a separate thread
                self.server_thread = threading.Thread(
                    target=self.http_server.serve_forever,
                    daemon=True
                )
                self.server_thread.start()

                print(f"Started HTTP server on port {port}")
                print(f"Serving files from: {self.player_dir}")

                # Test if server is responding
                import time
                time.sleep(0.5)  # Give server time to start

                return True, port

            except OSError as e:
                if "Address already in use" in str(e):
                    print(f"Port {port} is busy, trying next port...")
                    continue
                else:
                    print(f"Error starting HTTP server on port {port}: {e}")
                    break
            except Exception as e:
                print(f"Error starting HTTP server on port {port}: {e}")
                break

        print("Failed to start HTTP server on any port")
        self._stop_http_server()
        return False, None

    def _stop_http_server(self):
        """Stop the HTTP server."""
        if self.http_server:
            try:
                self.http_server.shutdown()
                self.http_server.server_close()
                print("Stopped HTTP server")
            except Exception as e:
                print(f"Error stopping HTTP server: {e}")
            finally:
                self.http_server = None
                self.server_thread = None

    def play(self, mpd_url, drm_key=None):
        """
        Play a video using the player.

        Args:
            mpd_url (str): The MPD URL to play
            drm_key (str, optional): The DRM key in the format "kid:key"

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Stop any existing player
            self.stop()

            # Validate inputs
            if not mpd_url:
                error_msg = "MPD URL is required"
                print(f"ERROR: {error_msg}")
                self.signals.player_error.emit(error_msg)
                return False

            # Parse the DRM key
            if drm_key and ':' in drm_key:
                key_id, key = drm_key.split(':', 1)
                print(f"Using DRM key - KID: {key_id}, KEY: {key}")
            else:
                key_id, key = None, None
                print("Warning: Invalid DRM key format. Expected 'kid:key'")

            # Start the HTTP server
            success, port = self._start_http_server()
            if not success or not port:
                error_msg = "Failed to start HTTP server"
                print(f"ERROR: {error_msg}")
                self.signals.player_error.emit(error_msg)
                return False

            # Create the player URL with query parameters
            # Use shahid_player.html (original player)
            base_url = f"http://localhost:{port}/shahid_player.html"

            # Create query parameters
            query_params = {
                'mpd': mpd_url
            }

            # Add DRM key parameters if available
            if key_id and key:
                query_params['keyId'] = key_id
                query_params['key'] = key

            player_url = f"{base_url}?{urllib.parse.urlencode(query_params)}"
            print(f"Player URL: {player_url}")

            # Open the player in an embedded window instead of browser
            print("Opening player in embedded window...")
            try:
                # Create and show the player window
                self.webview_window = PlayerWindow(
                    url=player_url,
                    parent=self.parent,
                    on_close_callback=self._on_player_closed
                )

                # Show the window maximized
                self.webview_window.showMaximized()

                print("Player opened in embedded window")

                # Emit player started event
                try:
                    if hasattr(self.signals, 'player_started') and hasattr(self.signals.player_started, 'emit'):
                        self.signals.player_started.emit(True)
                except Exception as e:
                    print(f"Warning: Could not emit player_started signal: {e}")

                return True
            except Exception as e:
                error_msg = f"Error opening player in embedded window: {e}"
                print(f"ERROR: {error_msg}")
                self._stop_http_server()
                try:
                    self.signals.player_error.emit(error_msg)
                except Exception as e:
                    print(f"Warning: Could not emit player_error signal: {e}")
                return False

        except Exception as e:
            error_msg = f"Error starting player: {e}"
            print(f"ERROR: {error_msg}")
            self._stop_http_server()
            try:
                self.signals.player_error.emit(error_msg)
            except Exception as e:
                print(f"Warning: Could not emit player_error signal: {e}")
            return False

    def _on_player_closed(self):
        """Handle player window closed event."""
        print("Player window closed callback received")
        # Set window reference to None
        self.webview_window = None
        # Stop the HTTP server
        self._stop_http_server()
        # Emit the player stopped signal
        try:
            self.signals.player_stopped.emit()
        except Exception as e:
            print(f"Warning: Could not emit player_stopped signal: {e}")
        print("Player cleanup completed")

    def stop(self):
        """Stop the player."""
        if self.webview_window:
            print("Stopping player...")
            self.webview_window.close()
            self.webview_window = None

        # Stop the HTTP server (this will stop the player in the browser)
        self._stop_http_server()

        # Emit the player stopped signal
        try:
            self.signals.player_stopped.emit()
        except Exception as e:
            print(f"Warning: Could not emit player_stopped signal: {e}")

        print("Player stopped")
        return True

    def play_in_browser(self, mpd_url, drm_key=None):
        """
        Play content in the default browser.

        Args:
            mpd_url (str): The MPD URL to play
            drm_key (str, optional): The DRM key in the format "kid:key"

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Stop any existing player
            self.stop()

            # Validate inputs
            if not mpd_url:
                error_msg = "MPD URL is required"
                print(f"ERROR: {error_msg}")
                return False

            # Parse the DRM key
            if drm_key and ':' in drm_key:
                key_id, key = drm_key.split(':', 1)
                print(f"Using DRM key - KID: {key_id}, KEY: {key}")
            else:
                key_id, key = None, None
                if drm_key:
                    print("Warning: Invalid DRM key format. Expected 'kid:key'")

            # Start the HTTP server
            success, port = self._start_http_server()
            if not success or not port:
                error_msg = "Failed to start HTTP server"
                print(f"ERROR: {error_msg}")
                return False

            # Create the player URL with query parameters
            base_url = f"http://localhost:{port}/shahid_player.html"

            # Create query parameters
            query_params = {
                'mpd': mpd_url
            }

            # Add DRM key parameters if available
            if key_id and key:
                query_params['keyId'] = key_id
                query_params['key'] = key

            player_url = f"{base_url}?{urllib.parse.urlencode(query_params)}"
            print(f"Browser Player URL: {player_url}")

            # Open the player URL in the default browser
            print("Opening player in browser...")
            try:
                import webbrowser
                import sys
                import os

                # On Windows, try to open maximized
                if sys.platform == 'win32':
                    try:
                        # Use start /max to open maximized on Windows
                        os.system(f'start /max "" "{player_url}"')
                    except Exception as e:
                        print(f"Error opening maximized, using alternative method: {e}")
                        webbrowser.open(player_url)
                else:
                    # On other systems, use standard method
                    webbrowser.open(player_url)

                print("Player opened in browser")
                return True

            except Exception as e:
                error_msg = f"Error opening player in browser: {e}"
                print(f"ERROR: {error_msg}")
                self._stop_http_server()
                return False

        except Exception as e:
            error_msg = f"Error starting browser player: {e}"
            print(f"ERROR: {error_msg}")
            self._stop_http_server()
            return False

    def create_embedded_widget(self, mpd_url, drm_key=None):
        """
        Create an embedded player widget that can be added to any layout.

        Args:
            mpd_url (str): The MPD URL to play
            drm_key (str, optional): The DRM key in the format "kid:key"

        Returns:
            EmbeddedPlayerWidget: The player widget, or None if failed
        """
        try:
            print("Starting create_embedded_widget...")

            # Validate inputs
            if not mpd_url:
                print("ERROR: MPD URL is required")
                return None

            print(f"MPD URL: {mpd_url}")
            print(f"DRM Key: {drm_key}")

            # Parse the DRM key
            if drm_key and ':' in drm_key:
                key_id, key = drm_key.split(':', 1)
                print(f"Using DRM key - KID: {key_id}, KEY: {key}")
            else:
                key_id, key = None, None
                if drm_key:
                    print("Warning: Invalid DRM key format. Expected 'kid:key'")

            # Start the HTTP server
            print("Starting HTTP server...")
            success, port = self._start_http_server()
            if not success or not port:
                print("ERROR: Failed to start HTTP server")
                return None

            print(f"HTTP server started on port {port}")

            # Create the player URL with query parameters
            base_url = f"http://localhost:{port}/shahid_player.html"

            # Create query parameters
            query_params = {
                'mpd': mpd_url
            }

            # Add DRM key parameters if available
            if key_id and key:
                query_params['keyId'] = key_id
                query_params['key'] = key

            player_url = f"{base_url}?{urllib.parse.urlencode(query_params)}"
            print(f"Embedded Player URL: {player_url}")

            # Create the embedded widget
            print("Creating EmbeddedPlayerWidget...")
            self.embedded_widget = EmbeddedPlayerWidget(self.parent)

            print("Loading player URL...")
            success = self.embedded_widget.load_player(player_url)

            if success:
                print("Embedded player widget created successfully")
                return self.embedded_widget
            else:
                print("Failed to load player URL")
                self._stop_http_server()
                return None

        except Exception as e:
            print(f"ERROR: Error creating embedded player widget: {e}")
            import traceback
            traceback.print_exc()
            self._stop_http_server()
            return None

# Function to play content directly without creating a class instance
def play_mpd(mpd_url, drm_key=None, parent=None):
    """
    Play MPD content with the custom player (standalone function).

    Args:
        mpd_url (str): The URL of the MPD file to play
        drm_key (str, optional): The DRM key for decryption in the format "kid:key"
        parent (QWidget, optional): Parent widget for the player window

    Returns:
        bool: True if player started successfully, False otherwise
    """
    player = ShahidPlayer(parent)
    return player.play(mpd_url, drm_key)

# Function to create embedded player widget
def create_embedded_player(mpd_url, drm_key=None, parent=None):
    """
    Create an embedded player widget that can be added to any layout.

    Args:
        mpd_url (str): The URL of the MPD file to play
        drm_key (str, optional): The DRM key for decryption in the format "kid:key"
        parent (QWidget, optional): Parent widget for the player

    Returns:
        EmbeddedPlayerWidget: The player widget, or None if failed
    """
    try:
        print("create_embedded_player called")
        player = ShahidPlayer(parent)
        result = player.create_embedded_widget(mpd_url, drm_key)
        print(f"create_embedded_player result: {result}")
        return result
    except Exception as e:
        print(f"Error in create_embedded_player: {e}")
        import traceback
        traceback.print_exc()
        return None

# Function to play content in browser
def play_in_browser(mpd_url, drm_key=None, parent=None):
    """
    Play MPD content in the default browser.

    Args:
        mpd_url (str): The URL of the MPD file to play
        drm_key (str, optional): The DRM key for decryption in the format "kid:key"
        parent (QWidget, optional): Parent widget (not used for browser)

    Returns:
        bool: True if player started successfully, False otherwise
    """
    try:
        print("play_in_browser called")
        player = ShahidPlayer(parent)
        result = player.play_in_browser(mpd_url, drm_key)
        print(f"play_in_browser result: {result}")
        return result
    except Exception as e:
        print(f"Error in play_in_browser: {e}")
        import traceback
        traceback.print_exc()
        return False

# Function to create Chrome player widget
def create_chrome_player(mpd_url, drm_key=None, parent=None):
    """
    Create a Chrome embedded player widget.

    Args:
        mpd_url (str): The URL of the MPD file to play
        drm_key (str, optional): The DRM key for decryption in the format "kid:key"
        parent (QWidget, optional): Parent widget for the player

    Returns:
        ChromePlayerWidget: The Chrome player widget, or None if failed
    """
    try:
        if not CHROME_AVAILABLE:
            print("Chrome Player not available")
            return None

        print("create_chrome_player called")
        chrome_widget = ChromePlayerWidget()

        if chrome_widget.load_player(mpd_url, drm_key):
            print("Chrome player created successfully")
            return chrome_widget
        else:
            print("Failed to load Chrome player")
            return None

    except Exception as e:
        print(f"Error in create_chrome_player: {e}")
        import traceback
        traceback.print_exc()
        return None

# Function to open player in Chrome window
def play_in_browser(mpd_url, drm_key=None, parent=None):
    """
    Open the Chrome player in a separate Chrome window.

    Args:
        mpd_url (str): The URL of the MPD file to play
        drm_key (str, optional): The DRM key for decryption in the format "kid:key"
        parent (QWidget, optional): Parent widget

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        print("play_in_browser called")
        print("🚀 Opening Chrome player in separate window...")
        print(f"MPD URL: {mpd_url}")
        print(f"DRM Key: {drm_key}")

        # Create a ShahidPlayer instance to handle the server
        player = ShahidPlayer(parent)

        # Validate inputs
        if not mpd_url:
            print("ERROR: MPD URL is required")
            return False

        # Parse the DRM key
        if drm_key and ':' in drm_key:
            key_id, key = drm_key.split(':', 1)
            print(f"Using DRM key - KID: {key_id}, KEY: {key}")
        else:
            key_id, key = None, None
            if drm_key:
                print("Warning: Invalid DRM key format. Expected 'kid:key'")

        # Start the HTTP server
        success, port = player._start_http_server()
        if not success or not port:
            print("ERROR: Failed to start HTTP server")
            return False

        # Create the player URL with query parameters
        base_url = f"http://localhost:{port}/shahid_player.html"

        # Create query parameters
        query_params = {
            'mpd': mpd_url
        }

        # Add DRM key parameters if available
        if key_id and key:
            query_params['keyId'] = key_id
            query_params['key'] = key

        player_url = f"{base_url}?{urllib.parse.urlencode(query_params)}"
        print(f"Player URL: {player_url}")

        # Open in Chrome window with app mode
        import subprocess
        import os

        # Try to find Chrome executable
        chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe"),
            "chrome.exe",  # If in PATH
            "google-chrome",  # Linux
            "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"  # macOS
        ]

        chrome_exe = None
        for path in chrome_paths:
            if os.path.exists(path):
                chrome_exe = path
                break

        if not chrome_exe:
            print("Chrome not found, falling back to default browser")
            import webbrowser
            webbrowser.open(player_url)
            return True

        # Launch Chrome in app mode (separate window)
        cmd = [
            chrome_exe,
            f"--app={player_url}",
            "--window-size=1280,720",
            "--window-position=100,100",
            f"--user-data-dir={os.path.join(os.path.expanduser('~'), 'shahid_player_chrome')}",
            "--disable-web-security",
            "--disable-features=VizDisplayCompositor",
            "--autoplay-policy=no-user-gesture-required",
            "--disable-infobars",
            "--disable-notifications",
            "--disable-default-apps",
            "--disable-extensions",
            "--no-first-run",
            "--no-default-browser-check",
            "--disable-translate",
            "--disable-background-timer-throttling",
            "--disable-backgrounding-occluded-windows",
            "--disable-renderer-backgrounding",
            "--disable-field-trial-config",
            "--disable-ipc-flooding-protection",
            "--test-type",
            "--disable-logging",
            "--silent-debugger-extension-api",
            "--disable-component-extensions-with-background-pages",
            "--disable-background-networking",
            "--disable-sync",
            "--metrics-recording-only",
            "--no-report-upload",
            "--disable-prompt-on-repost",
            "--disable-hang-monitor",
            "--disable-client-side-phishing-detection",
            "--disable-popup-blocking",
            "--disable-dev-shm-usage",
            "--no-sandbox"
        ]

        print(f"Launching Chrome with command: {' '.join(cmd)}")
        subprocess.Popen(cmd, shell=False)

        print("Chrome player opened in separate window successfully")
        return True

    except Exception as e:
        print(f"❌ Error opening Chrome player in separate window: {e}")
        import traceback
        traceback.print_exc()
        return False