﻿LOG 2025/06/08
Save Path: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\binaries\Logs
Task Start: 2025/06/08 23:13:04
Task CommandLine: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\binaries\N_m3u8DL-RE.exe https://osn-video.anghcdn.co/public/154185-57504-PR681511-BX-AS045369-283438-05d4503d996d2807eb8151af48a9c795-1749048008/stream.mpd?token=P2Fkcy1lbmFibGVkPTAmY2xpZW50LXBsYXRmb3JtPXdlYi1vc24mY2xpZW50LXZlcnNpb249MS4xLjEmY29udGVudC1pZD01NzUwNCZleHBpcnk9MTc0OTQ1NjM4MCZzaWduYXR1cmU9MzMyMzA0ZGE1OTJlMWZiMDE1MDRmOTViODg0ZmZkMDc5ODdlNGI2MyZzdHJlYW0taWQ9MTI4MDExJnVzZXItaWQ9MTAzMDI2MDY5 -mt --select-video id=8086f046-9792-4e9f-8798-5a15c30b6104 --select-audio lang=ar --drop-subtitle .* --tmp-dir C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\cache --save-dir C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\cache --save-name "Al Mushardoon S01E30.720p.OSN+.WEB-DL.H264.AAC" --decryption-binary-path=C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\binaries\mp4decrypt.exe --key-text-file=C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\KEYS\OSNPLUS_KEYS.txt --log-level OFF

23:13:04.890 EXTRA: ffmpeg => C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\binaries\ffmpeg.exe
23:13:05.255 EXTRA: DropSubtitleFilter => For: best
23:13:05.256 EXTRA: VideoFilter => GroupIdReg: 8086f046-9792-4e9f-8798-5a15c30b6104 For: best
23:13:05.256 EXTRA: AudioFilter => LanguageReg: ar For: best
