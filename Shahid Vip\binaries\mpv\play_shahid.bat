@echo off
echo ========================================
echo Running MPV with DRM key...
echo ========================================
echo MPD URL: https://mbcvod-enc.edgenextcdn.net/out/v1/254ae95ecbe34c3fa1ad30509b724b78/7c201da305b543e0ae55a3673f9aa3a5/840aa317c6f34c8fb0ef18117f07c496/index.mpd?aws.manifestfilter=video_height:144-1080;video_codec:H264^&video_height=144-1080^&video_codec=H264
echo DRM Key: c0949af00e2c423bb715060eba7903f7:0a65e6eb0e360c7334b2e96e62b6c7e6
echo ========================================
echo Command: "C:\Users\<USER>\OneDrive\Desktop\DSH\shahid_template\binaries\mpv\mpv.exe" --stream-lavf-o=cenc_decryption_key=c0949af00e2c423bb715060eba7903f7:0a65e6eb0e360c7334b2e96e62b6c7e6 "https://mbcvod-enc.edgenextcdn.net/out/v1/254ae95ecbe34c3fa1ad30509b724b78/7c201da305b543e0ae55a3673f9aa3a5/840aa317c6f34c8fb0ef18117f07c496/index.mpd?aws.manifestfilter=video_height:144-1080;video_codec:H264^&video_height=144-1080^&video_codec=H264"
echo ========================================
echo.
echo Trying with --stream-lavf-o=cenc_decryption_key format...
"C:\Users\<USER>\OneDrive\Desktop\DSH\shahid_template\binaries\mpv\mpv.exe" --msg-level=all=v --stream-lavf-o=cenc_decryption_key=c0949af00e2c423bb715060eba7903f7:0a65e6eb0e360c7334b2e96e62b6c7e6 "https://mbcvod-enc.edgenextcdn.net/out/v1/254ae95ecbe34c3fa1ad30509b724b78/7c201da305b543e0ae55a3673f9aa3a5/840aa317c6f34c8fb0ef18117f07c496/index.mpd?aws.manifestfilter=video_height:144-1080;video_codec:H264^&video_height=144-1080^&video_codec=H264"
if %ERRORLEVEL% NEQ 0 (
  echo First attempt failed with error code %ERRORLEVEL%
  echo.
  echo Trying with alternative format 1...
  "C:\Users\<USER>\OneDrive\Desktop\DSH\shahid_template\binaries\mpv\mpv.exe" --msg-level=all=v --demuxer-lavf-o=decryption_key=c0949af00e2c423bb715060eba7903f7:0a65e6eb0e360c7334b2e96e62b6c7e6 "https://mbcvod-enc.edgenextcdn.net/out/v1/254ae95ecbe34c3fa1ad30509b724b78/7c201da305b543e0ae55a3673f9aa3a5/840aa317c6f34c8fb0ef18117f07c496/index.mpd?aws.manifestfilter=video_height:144-1080;video_codec:H264^&video_height=144-1080^&video_codec=H264"
  if %ERRORLEVEL% NEQ 0 (
    echo Second attempt failed with error code %ERRORLEVEL%
    echo.
    echo Trying with alternative format 2...
    "C:\Users\<USER>\OneDrive\Desktop\DSH\shahid_template\binaries\mpv\mpv.exe" --msg-level=all=v --demuxer-lavf-o="cenc_decryption_key=c0949af00e2c423bb715060eba7903f7:0a65e6eb0e360c7334b2e96e62b6c7e6" "https://mbcvod-enc.edgenextcdn.net/out/v1/254ae95ecbe34c3fa1ad30509b724b78/7c201da305b543e0ae55a3673f9aa3a5/840aa317c6f34c8fb0ef18117f07c496/index.mpd?aws.manifestfilter=video_height:144-1080;video_codec:H264^&video_height=144-1080^&video_codec=H264"
    if %ERRORLEVEL% NEQ 0 (
      echo Third attempt failed with error code %ERRORLEVEL%
      echo.
      echo Trying with alternative format 3...
      "C:\Users\<USER>\OneDrive\Desktop\DSH\shahid_template\binaries\mpv\mpv.exe" --msg-level=all=v --demuxer-lavf-o="decryption_key=c0949af00e2c423bb715060eba7903f7:0a65e6eb0e360c7334b2e96e62b6c7e6" "https://mbcvod-enc.edgenextcdn.net/out/v1/254ae95ecbe34c3fa1ad30509b724b78/7c201da305b543e0ae55a3673f9aa3a5/840aa317c6f34c8fb0ef18117f07c496/index.mpd?aws.manifestfilter=video_height:144-1080;video_codec:H264^&video_height=144-1080^&video_codec=H264"
      if %ERRORLEVEL% NEQ 0 (
        echo Fourth attempt failed with error code %ERRORLEVEL%
        echo.
        echo Trying with alternative format 4 (no quotes)...
        "C:\Users\<USER>\OneDrive\Desktop\DSH\shahid_template\binaries\mpv\mpv.exe" --msg-level=all=v --demuxer-lavf-o=decryption_key=c0949af00e2c423bb715060eba7903f7:0a65e6eb0e360c7334b2e96e62b6c7e6 "https://mbcvod-enc.edgenextcdn.net/out/v1/254ae95ecbe34c3fa1ad30509b724b78/7c201da305b543e0ae55a3673f9aa3a5/840aa317c6f34c8fb0ef18117f07c496/index.mpd?aws.manifestfilter=video_height:144-1080;video_codec:H264^&video_height=144-1080^&video_codec=H264"
        if %ERRORLEVEL% NEQ 0 (
          echo Fifth attempt failed with error code %ERRORLEVEL%
          echo.
          echo Trying with the exact format you mentioned...
          "C:\Users\<USER>\OneDrive\Desktop\DSH\shahid_template\binaries\mpv\mpv.exe" "https://mbcvod-enc.edgenextcdn.net/out/v1/254ae95ecbe34c3fa1ad30509b724b78/7c201da305b543e0ae55a3673f9aa3a5/840aa317c6f34c8fb0ef18117f07c496/index.mpd?aws.manifestfilter=video_height:144-1080;video_codec:H264^&video_height=144-1080^&video_codec=H264" --stream-lavf-o=cenc_decryption_key=c0949af00e2c423bb715060eba7903f7:0a65e6eb0e360c7334b2e96e62b6c7e6
          if %ERRORLEVEL% NEQ 0 (
            echo Sixth attempt failed with error code %ERRORLEVEL%
            echo.
            echo Trying with a completely different approach...
            echo Creating temporary MPV config file...
            echo demuxer-lavf-o=cenc_decryption_key=c0949af00e2c423bb715060eba7903f7:0a65e6eb0e360c7334b2e96e62b6c7e6 > mpv_temp_config.conf
            echo Using config file approach...
            "C:\Users\<USER>\OneDrive\Desktop\DSH\shahid_template\binaries\mpv\mpv.exe" --config-dir=. --include=mpv_temp_config.conf "https://mbcvod-enc.edgenextcdn.net/out/v1/254ae95ecbe34c3fa1ad30509b724b78/7c201da305b543e0ae55a3673f9aa3a5/840aa317c6f34c8fb0ef18117f07c496/index.mpd?aws.manifestfilter=video_height:144-1080;video_codec:H264^&video_height=144-1080^&video_codec=H264"
            del mpv_temp_config.conf
          )
        )
      )
    )
  )
)
echo.
echo Playback completed or failed.
pause
