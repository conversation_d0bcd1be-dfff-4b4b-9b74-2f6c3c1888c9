﻿LOG 2025/05/29
Save Path: F:\TEEFA\MBC\shahid_template\binaries\Logs
Task Start: 2025/05/29 03:25:46
Task CommandLine: F:\TEEFA\MBC\shahid_template\binaries\N_m3u8DL-RE.exe https://mbc-aws-ksa.mncdn.com/out/v1/69250fd4f12a4a9e91d3ea6d61e27358/7c201da305b543e0ae55a3673f9aa3a5/840aa317c6f34c8fb0ef18117f07c496/index.mpd?aws.manifestfilter=video_height:144-1080;video_codec:H264&video_height=144-1080&video_codec=H264 -sv id=4:for=best --select-audio lang=ar:for=all --sub-only=false --tmp-dir F:\TEEFA\MBC\shahid_template\cache\episode_6 --save-dir F:\TEEFA\MBC\shahid_template\cache\episode_6 --save-name "<PERSON><PERSON> ++.S03.E08.468p.SHAHID.VIP.WEB-DL.H264.AAC" --base-url https://mbc-aws-ksa.mncdn.com/out/v1/69250fd4f12a4a9e91d3ea6d61e27358/7c201da305b543e0ae55a3673f9aa3a5/840aa317c6f34c8fb0ef18117f07c496/ --decryption-binary-path=F:\TEEFA\MBC\shahid_template\binaries\mp4decrypt.exe --key-text-file=F:\TEEFA\MBC\shahid_template\KEYS\KEYS.txt --log-level INFO --binary-merge --thread-count 16 --download-retry-count 3 --sub-format SRT -mt

03:25:46.244 INFO : N_m3u8DL-RE (Beta version) 20230628
03:25:46.257 EXTRA: ffmpeg => F:\TEEFA\MBC\shahid_template\binaries\ffmpeg.exe
03:25:46.257 INFO : Loading URL: https://mbc-aws-ksa.mncdn.com/out/v1/69250fd4f12a4a9e91d3ea6d61e27358/7c201da305b543e0ae55a3673f9aa3a5/840aa317c6f34c8fb0ef18117f07c496/index.mpd?aws.manifestfilter=video_height:144-1080;video_codec:H264&video_height=144-1080&video_codec=H264
03:25:46.435 INFO : Content Matched: Dynamic Adaptive Streaming over HTTP
03:25:46.435 INFO : Parsing streams...
03:25:46.531 WARN : Writing meta json
03:25:46.548 INFO : Extracted, there are 40 streams, with 10 basic streams, 6 audio streams, 24 subtitle streams
03:25:46.549 INFO : Vid *CENC 1920x1080 | 2651 Kbps | 2 | 25 | avc1.640028 | 478 Segments | ~47m47s
03:25:46.549 INFO : Vid *CENC 1280x720 | 1510 Kbps | 3 | 25 | avc1.4D401F | 478 Segments | ~47m47s
03:25:46.550 INFO : Vid *CENC 1024x576 | 1164 Kbps | 1 | 25 | avc1.4D401F | 478 Segments | ~47m47s
03:25:46.550 INFO : Vid *CENC 832x468 | 715 Kbps | 4 | 25 | avc1.4D401F | 478 Segments | ~47m47s
03:25:46.550 INFO : Vid *CENC 640x360 | 531 Kbps | 5 | 25 | avc1.4D401E | 478 Segments | ~47m47s
03:25:46.551 INFO : Vid *CENC 512x288 | 418 Kbps | 6 | 25 | avc1.4D401E | 478 Segments | ~47m47s
03:25:46.551 INFO : Vid *CENC 448x252 | 367 Kbps | 7 | 25 | avc1.4D401E | 478 Segments | ~47m47s
03:25:46.551 INFO : Vid *CENC 448x252 | 329 Kbps | 8 | 25 | avc1.4D401E | 478 Segments | ~47m47s
03:25:46.552 INFO : Vid *CENC 256x144 | 219 Kbps | 9 | 25 | avc1.4D401E | 478 Segments | ~47m47s
03:25:46.552 INFO : Vid *CENC 256x144 | 149 Kbps | 10 | 25 | avc1.4D401E | 478 Segments | ~47m47s
03:25:46.552 INFO : Aud *CENC 11 | 128 Kbps | mp4a.40.2 | ar | 2CH | 478 Segments | ~47m47s
03:25:46.553 INFO : Aud *CENC 12 | 127 Kbps | mp4a.40.2 | qaa | 2CH | 478 Segments | ~47m47s
03:25:46.553 INFO : Aud *CENC 13 | 127 Kbps | mp4a.40.2 | qab | 2CH | 478 Segments | ~47m47s
03:25:46.553 INFO : Aud *CENC 14 | 127 Kbps | mp4a.40.2 | qac | 2CH | 478 Segments | ~47m47s
03:25:46.560 INFO : Aud *CENC 15 | 127 Kbps | mp4a.40.2 | qad | 2CH | 478 Segments | ~47m47s
03:25:46.566 INFO : Aud *CENC 16 | 127 Kbps | mp4a.40.2 | qae | 2CH | 478 Segments | ~47m47s
03:25:46.566 INFO : Sub 17 | en | stpp | 478 Segments | ~47m47s
03:25:46.567 INFO : Sub 18 | ar | stpp | 478 Segments | ~47m47s
03:25:46.567 INFO : Sub 19 | ar-fn | stpp | 478 Segments | ~47m47s
03:25:46.567 INFO : Sub 20 | en-fn | stpp | 478 Segments | ~47m47s
03:25:46.568 INFO : Sub 21 | fr-fn | stpp | 478 Segments | ~47m47s
03:25:46.568 INFO : Sub 22 | ko | stpp | 478 Segments | ~47m47s
03:25:46.568 INFO : Sub 23 | tl | stpp | 478 Segments | ~47m47s
03:25:46.568 INFO : Sub 24 | ru | stpp | 478 Segments | ~47m47s
03:25:46.569 INFO : Sub 25 | bn | stpp | 478 Segments | ~47m47s
03:25:46.569 INFO : Sub 26 | pl | stpp | 478 Segments | ~47m47s
03:25:46.569 INFO : Sub 27 | no | stpp | 478 Segments | ~47m47s
03:25:46.569 INFO : Sub 28 | es | stpp | 478 Segments | ~47m47s
03:25:46.570 INFO : Sub 29 | el | stpp | 478 Segments | ~47m47s
03:25:46.570 INFO : Sub 30 | tr | stpp | 478 Segments | ~47m47s
03:25:46.570 INFO : Sub 31 | de | stpp | 478 Segments | ~47m47s
03:25:46.571 INFO : Sub 32 | ct | stpp | 478 Segments | ~47m47s
03:25:46.571 INFO : Sub 33 | pt | stpp | 478 Segments | ~47m47s
03:25:46.571 INFO : Sub 34 | ja | stpp | 478 Segments | ~47m47s
03:25:46.571 INFO : Sub 35 | it | stpp | 478 Segments | ~47m47s
03:25:46.572 INFO : Sub 36 | nl | stpp | 478 Segments | ~47m47s
03:25:46.572 INFO : Sub 37 | id | stpp | 478 Segments | ~47m47s
03:25:46.573 INFO : Sub 38 | hi | stpp | 478 Segments | ~47m47s
03:25:46.573 INFO : Sub 39 | ms | stpp | 478 Segments | ~47m47s
03:25:46.573 INFO : Sub 40 | ml | stpp | 478 Segments | ~47m47s
03:25:46.574 EXTRA: VideoFilter => GroupIdReg: 4 For: best
03:25:46.574 EXTRA: AudioFilter => LanguageReg: ar For: all
03:25:46.574 INFO : Parsing streams...
03:25:46.575 INFO : Selected streams:
03:25:46.575 INFO : Vid *CENC 832x468 | 715 Kbps | 4 | 25 | avc1.4D401F | 478 Segments | ~47m47s
03:25:46.575 INFO : Aud *CENC 11 | 128 Kbps | mp4a.40.2 | ar | 2CH | 478 Segments | ~47m47s
03:25:46.576 WARN : Writing meta json
03:25:46.577 INFO : Save Name: Kamel El Adad ++.S03.E08.468p.SHAHID.VIP.WEB-DL.H264.AAC
03:25:46.578 INFO : Start downloading...Vid 832x468 | 715 Kbps | 4 | 25 | avc1.4D401F
03:25:46.578 INFO : Start downloading...Aud 11 | 128 Kbps | mp4a.40.2 | ar | 2CH
03:25:46.584 INFO : New version detected! v0.3.0-beta
03:25:46.649 WARN : Type: cenc
03:25:46.650 WARN : PSSH(WV): CAESEHwFtHWFa0+3ohxNVROB9ikaBWluc3lzIhIzMDEwMDM3MDk5X3N0eG1sNGsyAA==
03:25:46.650 WARN : KID: 7c05b475856b4fb7a21c4d551381f629
03:25:46.657 INFO : Trying to search for KEY from text file...
03:25:46.658 INFO : OK 7c05b475856b4fb7a21c4d551381f629:7250a3817ab43dc276dfdeccd94dc670
03:25:46.666 WARN : Reading media info...
03:25:46.690 INFO : [0x1]: Video, h264 (avc1), 832x468
03:25:46.703 WARN : Type: cenc
03:25:46.703 WARN : PSSH(WV): CAESEHwFtHWFa0+3ohxNVROB9ikaBWluc3lzIhIzMDEwMDM3MDk5X3N0eG1sNGsyAA==
03:25:46.704 WARN : KID: 7c05b475856b4fb7a21c4d551381f629
03:25:46.704 INFO : Trying to search for KEY from text file...
03:25:46.704 INFO : OK 7c05b475856b4fb7a21c4d551381f629:7250a3817ab43dc276dfdeccd94dc670
03:25:46.705 WARN : Reading media info...
03:25:46.726 INFO : [0x1]: Audio, aac (mp4a)
03:26:19.598 INFO : Binary merging...
03:26:19.717 INFO : Decrypting...
03:26:53.615 INFO : Binary merging...
03:26:53.858 INFO : Decrypting...
03:26:54.917 INFO : Done
