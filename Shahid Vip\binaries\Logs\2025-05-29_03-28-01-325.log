﻿LOG 2025/05/29
Save Path: F:\TEEFA\MBC\shahid_template\binaries\Logs
Task Start: 2025/05/29 03:28:01
Task CommandLine: F:\TEEFA\MBC\shahid_template\binaries\N_m3u8DL-RE.exe https://mbc-aws-ksa.mncdn.com/out/v1/a21d3ae67cbf464c8f5654382c6ab75b/7c201da305b543e0ae55a3673f9aa3a5/840aa317c6f34c8fb0ef18117f07c496/index.mpd?aws.manifestfilter=video_height:144-1080;video_codec:H264&video_height=144-1080&video_codec=H264 -sv id=4:for=best --select-audio lang=ar:for=all --sub-only=false --tmp-dir F:\TEEFA\MBC\shahid_template\cache\episode_8 --save-dir F:\TEEFA\MBC\shahid_template\cache\episode_8 --save-name "<PERSON><PERSON> Adad ++.S03.E10.468p.SHAHID.VIP.WEB-DL.H264.AAC" --base-url https://mbc-aws-ksa.mncdn.com/out/v1/a21d3ae67cbf464c8f5654382c6ab75b/7c201da305b543e0ae55a3673f9aa3a5/840aa317c6f34c8fb0ef18117f07c496/ --decryption-binary-path=F:\TEEFA\MBC\shahid_template\binaries\mp4decrypt.exe --key-text-file=F:\TEEFA\MBC\shahid_template\KEYS\KEYS.txt --log-level INFO --binary-merge --thread-count 16 --download-retry-count 3 --sub-format SRT -mt

03:28:01.327 INFO : N_m3u8DL-RE (Beta version) 20230628
03:28:01.336 EXTRA: ffmpeg => F:\TEEFA\MBC\shahid_template\binaries\ffmpeg.exe
03:28:01.336 INFO : Loading URL: https://mbc-aws-ksa.mncdn.com/out/v1/a21d3ae67cbf464c8f5654382c6ab75b/7c201da305b543e0ae55a3673f9aa3a5/840aa317c6f34c8fb0ef18117f07c496/index.mpd?aws.manifestfilter=video_height:144-1080;video_codec:H264&video_height=144-1080&video_codec=H264
03:28:01.495 INFO : Content Matched: Dynamic Adaptive Streaming over HTTP
03:28:01.495 INFO : Parsing streams...
03:28:01.577 WARN : Writing meta json
03:28:01.592 INFO : Extracted, there are 39 streams, with 10 basic streams, 6 audio streams, 23 subtitle streams
03:28:01.594 INFO : Vid *CENC 1920x1080 | 2678 Kbps | 2 | 25 | avc1.640028 | 418 Segments | ~41m45s
03:28:01.595 INFO : Vid *CENC 1280x720 | 1396 Kbps | 3 | 25 | avc1.4D401F | 418 Segments | ~41m45s
03:28:01.595 INFO : Vid *CENC 1024x576 | 1093 Kbps | 1 | 25 | avc1.4D401F | 418 Segments | ~41m45s
03:28:01.596 INFO : Vid *CENC 832x468 | 651 Kbps | 4 | 25 | avc1.4D401F | 418 Segments | ~41m45s
03:28:01.596 INFO : Vid *CENC 640x360 | 487 Kbps | 5 | 25 | avc1.4D401E | 418 Segments | ~41m45s
03:28:01.597 INFO : Vid *CENC 512x288 | 389 Kbps | 6 | 25 | avc1.4D401E | 418 Segments | ~41m45s
03:28:01.597 INFO : Vid *CENC 448x252 | 349 Kbps | 7 | 25 | avc1.4D401E | 418 Segments | ~41m45s
03:28:01.598 INFO : Vid *CENC 448x252 | 324 Kbps | 8 | 25 | avc1.4D401E | 418 Segments | ~41m45s
03:28:01.598 INFO : Vid *CENC 256x144 | 219 Kbps | 9 | 25 | avc1.4D401E | 418 Segments | ~41m45s
03:28:01.598 INFO : Vid *CENC 256x144 | 149 Kbps | 10 | 25 | avc1.4D401E | 418 Segments | ~41m45s
03:28:01.599 INFO : Aud *CENC 11 | 128 Kbps | mp4a.40.2 | ar | 2CH | 418 Segments | ~41m45s
03:28:01.600 INFO : Aud *CENC 12 | 127 Kbps | mp4a.40.2 | qaa | 2CH | 418 Segments | ~41m45s
03:28:01.600 INFO : Aud *CENC 13 | 127 Kbps | mp4a.40.2 | qab | 2CH | 418 Segments | ~41m45s
03:28:01.601 INFO : Aud *CENC 14 | 127 Kbps | mp4a.40.2 | qac | 2CH | 418 Segments | ~41m45s
03:28:01.601 INFO : Aud *CENC 15 | 127 Kbps | mp4a.40.2 | qad | 2CH | 418 Segments | ~41m45s
03:28:01.602 INFO : Aud *CENC 16 | 127 Kbps | mp4a.40.2 | qae | 2CH | 418 Segments | ~41m45s
03:28:01.602 INFO : Sub 17 | en | stpp | 418 Segments | ~41m45s
03:28:01.603 INFO : Sub 18 | ar | stpp | 418 Segments | ~41m45s
03:28:01.603 INFO : Sub 19 | ar-fn | stpp | 418 Segments | ~41m45s
03:28:01.604 INFO : Sub 20 | en-fn | stpp | 418 Segments | ~41m45s
03:28:01.604 INFO : Sub 21 | fr-fn | stpp | 418 Segments | ~41m45s
03:28:01.604 INFO : Sub 22 | id | stpp | 418 Segments | ~41m45s
03:28:01.605 INFO : Sub 23 | it | stpp | 418 Segments | ~41m45s
03:28:01.605 INFO : Sub 24 | ja | stpp | 418 Segments | ~41m45s
03:28:01.606 INFO : Sub 25 | pt | stpp | 418 Segments | ~41m45s
03:28:01.607 INFO : Sub 26 | ru | stpp | 418 Segments | ~41m45s
03:28:01.607 INFO : Sub 27 | de | stpp | 418 Segments | ~41m45s
03:28:01.607 INFO : Sub 28 | es | stpp | 418 Segments | ~41m45s
03:28:01.608 INFO : Sub 29 | ko | stpp | 418 Segments | ~41m45s
03:28:01.608 INFO : Sub 30 | ml | stpp | 418 Segments | ~41m45s
03:28:01.609 INFO : Sub 31 | tl | stpp | 418 Segments | ~41m45s
03:28:01.609 INFO : Sub 32 | tr | stpp | 418 Segments | ~41m45s
03:28:01.609 INFO : Sub 33 | pl | stpp | 418 Segments | ~41m45s
03:28:01.609 INFO : Sub 34 | hi | stpp | 418 Segments | ~41m45s
03:28:01.610 INFO : Sub 35 | ms | stpp | 418 Segments | ~41m45s
03:28:01.610 INFO : Sub 36 | nl | stpp | 418 Segments | ~41m45s
03:28:01.610 INFO : Sub 37 | no | stpp | 418 Segments | ~41m45s
03:28:01.610 INFO : Sub 38 | bn | stpp | 418 Segments | ~41m45s
03:28:01.611 INFO : Sub 39 | ct | stpp | 418 Segments | ~41m45s
03:28:01.611 EXTRA: VideoFilter => GroupIdReg: 4 For: best
03:28:01.611 EXTRA: AudioFilter => LanguageReg: ar For: all
03:28:01.611 INFO : Parsing streams...
03:28:01.613 INFO : Selected streams:
03:28:01.613 INFO : Vid *CENC 832x468 | 651 Kbps | 4 | 25 | avc1.4D401F | 418 Segments | ~41m45s
03:28:01.614 INFO : Aud *CENC 11 | 128 Kbps | mp4a.40.2 | ar | 2CH | 418 Segments | ~41m45s
03:28:01.614 WARN : Writing meta json
03:28:01.616 INFO : Save Name: Kamel El Adad ++.S03.E10.468p.SHAHID.VIP.WEB-DL.H264.AAC
03:28:01.617 INFO : Start downloading...Vid 832x468 | 651 Kbps | 4 | 25 | avc1.4D401F
03:28:01.617 INFO : Start downloading...Aud 11 | 128 Kbps | mp4a.40.2 | ar | 2CH
03:28:01.658 INFO : New version detected! v0.3.0-beta
03:28:01.669 WARN : Type: cenc
03:28:01.670 WARN : PSSH(WV): CAESEOCQURKU9UN8nGqcoZDjrfUaBWluc3lzIhIzMDEwMDM3MTAxX3N0eG1sNGsyAA==
03:28:01.672 WARN : KID: e090511294f5437c9c6a9ca190e3adf5
03:28:01.675 INFO : Trying to search for KEY from text file...
03:28:01.676 INFO : OK e090511294f5437c9c6a9ca190e3adf5:f1e72cd87811c72c47e5cc1ab1ce5bf5
03:28:01.679 WARN : Reading media info...
03:28:01.701 INFO : [0x1]: Video, h264 (avc1), 832x468
03:28:01.714 WARN : Type: cenc
03:28:01.715 WARN : PSSH(WV): CAESEOCQURKU9UN8nGqcoZDjrfUaBWluc3lzIhIzMDEwMDM3MTAxX3N0eG1sNGsyAA==
03:28:01.715 WARN : KID: e090511294f5437c9c6a9ca190e3adf5
03:28:01.716 INFO : Trying to search for KEY from text file...
03:28:01.716 INFO : OK e090511294f5437c9c6a9ca190e3adf5:f1e72cd87811c72c47e5cc1ab1ce5bf5
03:28:01.716 WARN : Reading media info...
03:28:01.738 INFO : [0x1]: Audio, aac (mp4a)
03:28:26.525 INFO : Binary merging...
03:28:26.647 INFO : Decrypting...
03:28:56.493 INFO : Binary merging...
03:28:56.713 INFO : Decrypting...
03:28:57.501 INFO : Done
