import requests
import json
import os
import threading
import time
from urllib.parse import unquote
from PySide6.QtCore import QObject, Signal

class OSNApi(QObject):
    # Signals for UI updates
    login_status_changed = Signal(bool, str)
    content_found = Signal(dict)
    episodes_found = Signal(dict)
    error_occurred = Signal(str)

    def __init__(self):
        super().__init__()
        self.saved_access_token = None
        self.base_url = "https://api.osnplus.com"
        self.auth_url = "https://koussa-osn.anghami.com"
        self.cookies_file = "cookies.txt"

        # Load initial token
        self.saved_access_token = self.refresh_access_token(silent_mode=True)

        # Start auto refresh token thread
        self.start_auto_refresh()

    def load_refresh_token_from_cookies(self, file_path="cookies.txt"):
        """Load refreshToken from cookies file"""
        try:
            if not os.path.exists(file_path):
                self.error_occurred.emit("Cookies file not found. Please export your OSN+ cookies to cookies.txt")
                return None

            with open(file_path, "r") as file:
                lines = file.readlines()
                for line in lines:
                    if "auth" in line and not line.startswith("#"):
                        try:
                            # Split the cookie line and get the value
                            parts = line.strip().split("\t")
                            if len(parts) >= 7:
                                auth_cookie = parts[-1].strip()
                                decoded_auth = unquote(auth_cookie)

                                # Try to decode twice if needed
                                try:
                                    auth_data = json.loads(decoded_auth)
                                except json.JSONDecodeError:
                                    decoded_auth = unquote(decoded_auth)
                                    auth_data = json.loads(decoded_auth)

                                refresh_token = auth_data.get("refreshToken")
                                if refresh_token:
                                    return refresh_token
                        except (json.JSONDecodeError, IndexError) as e:
                            continue

                # If no auth cookie found, check for other authentication cookies
                for line in lines:
                    if not line.startswith("#") and "\t" in line:
                        parts = line.strip().split("\t")
                        if len(parts) >= 7:
                            cookie_name = parts[5]
                            cookie_value = parts[6]

                            # Check for profile cookie which might contain auth info
                            if cookie_name == "profile" and cookie_value:
                                try:
                                    decoded_profile = unquote(cookie_value)
                                    profile_data = json.loads(decoded_profile)
                                    profile_token = profile_data.get("profileToken")
                                    if profile_token:
                                        # This is a profile token, not refresh token
                                        # But we can use it to indicate user is logged in
                                        pass
                                except:
                                    pass

        except (FileNotFoundError, json.JSONDecodeError, Exception) as e:
            self.error_occurred.emit(f"Error reading cookies file: {str(e)}")
            return None

        self.error_occurred.emit("No valid refresh token found in cookies. Please login to OSN+ and export fresh cookies.")
        return None

    def refresh_access_token(self, silent_mode=False):
        """Refresh accessToken using refreshToken"""
        refresh_token_value = self.load_refresh_token_from_cookies()
        if not refresh_token_value:
            if not silent_mode:
                self.error_occurred.emit("No refreshToken found. Please login to OSN+ and export cookies.")
            return None

        url = f"{self.auth_url}/osn/auth/v1/refresh-token"
        headers = {
            'accept': '*/*',
            'accept-language': 'en-US,en;q=0.9',
            'priority': 'u=1, i',
            'referer': 'https://osnplus.com/en-ae/',
            'rsc': '1',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36 Edg/124.0.0.0',
            'Content-Type': 'application/json',
        }

        payload = {"refreshToken": refresh_token_value}

        try:
            response = requests.post(url, headers=headers, json=payload, timeout=30)

            if response.status_code == 200:
                data = response.json()
                access_token = data.get("token")
                if access_token:
                    self.save_access_token(access_token)
                    self.saved_access_token = access_token
                    if not silent_mode:
                        self.login_status_changed.emit(True, "Token refreshed successfully!")
                    return access_token
                else:
                    if not silent_mode:
                        self.error_occurred.emit("No token received from server")
            elif response.status_code == 401:
                if not silent_mode:
                    self.error_occurred.emit("Refresh token expired. Please login to OSN+ again and export fresh cookies.")
            elif response.status_code == 403:
                if not silent_mode:
                    self.error_occurred.emit("Access forbidden. Please check your cookies and try again.")
            else:
                if not silent_mode:
                    self.error_occurred.emit(f"Failed to refresh token. Status code: {response.status_code}")

        except requests.exceptions.Timeout:
            if not silent_mode:
                self.error_occurred.emit("Request timeout. Please check your internet connection.")
        except requests.exceptions.ConnectionError:
            if not silent_mode:
                self.error_occurred.emit("Connection error. Please check your internet connection.")
        except Exception as e:
            if not silent_mode:
                self.error_occurred.emit(f"Error refreshing token: {str(e)}")

        return None

    def save_access_token(self, access_token, file_path="refresh_token.txt"):
        """Save token to file"""
        try:
            with open(file_path, "w") as file:
                file.write(f"accessToken: {access_token}")
        except Exception as e:
            self.error_occurred.emit(f"Error saving token: {str(e)}")

    def get_access_token(self):
        """Get current token, refresh if needed"""
        if not self.saved_access_token:
            self.saved_access_token = self.refresh_access_token(silent_mode=True)
        return self.saved_access_token

    def auto_refresh_token(self, interval=1800):  # Refresh every 30 minutes
        """Auto refresh token in background"""
        while True:
            self.refresh_access_token(silent_mode=True)
            time.sleep(interval)

    def start_auto_refresh(self):
        """Start auto refresh thread"""
        refresh_thread = threading.Thread(target=self.auto_refresh_token, daemon=True)
        refresh_thread.start()

    def check_content_type(self, content_id):
        """Quick check to determine if content ID is movie or series"""
        try:
            print(f"🔍 Quick check for content type of ID: {content_id}")
            access_token = self.get_access_token()
            if not access_token:
                return None

            # First try a lightweight movie check
            movie_url = f"{self.base_url}/osn/media/v1/get-watch-content"
            movie_payload = {"contentIds": [content_id]}

            headers = {
                'accept': '*/*',
                'access_token': access_token,
                'client_platform': 'web-osn',
                'content-type': 'application/json',
            }

            movie_response = requests.post(
                movie_url,
                json=movie_payload,
                headers=headers,
                timeout=10
            )

            if movie_response.status_code == 200:
                movie_data = movie_response.json()
                if movie_data.get("watchContents") and content_id in movie_data["watchContents"]:
                    print(f"✅ ID {content_id} is a MOVIE")
                    return "movie"

            # If not a movie, try series check
            series_url = f"{self.auth_url}/osn/media/v1/get-series-page"
            series_payload = {"contentId": content_id}

            series_response = requests.post(
                series_url,
                json=series_payload,
                headers=headers,
                timeout=10
            )

            if series_response.status_code == 200:
                series_data = series_response.json()
                if series_data.get("series"):
                    print(f"✅ ID {content_id} is a SERIES")
                    return "series"

            print(f"❌ ID {content_id} not found in either movies or series")
            return None

        except Exception as e:
            print(f"❌ Error checking content type: {str(e)}")
            return None

    def get_movie_details(self, movie_link, silent_fallback=False):
        """Get movie details from API and automatically fetch streams"""
        access_token = self.get_access_token()
        if not access_token:
            self.error_occurred.emit("No access token available. Please login first.")
            return None

        url = f"{self.base_url}/osn/media/v1/get-watch-content"

        headers = {
            'accept': '*/*',
            'accept-language': 'en-US,en;q=0.9',
            'access_token': access_token,
            'client_platform': 'web-osn',
            'client_version': '1.1.1',
            'content-type': 'application/json',
            'device_unique_identifier': '35b4ffb9-654f-4fca-a132-8ed1011d1927',
            'language': 'en',
            'origin': 'https://osnplus.com',
            'referer': 'https://osnplus.com/',
        }

        # Extract movie ID from link
        try:
            if "osnplus.com" in movie_link:
                # Extract from URL like: https://osnplus.com/en-ae/shows/1035538-al-abqari
                movie_id = movie_link.split("-")[-1]
            else:
                # Assume it's just the ID
                movie_id = movie_link.strip()
        except:
            self.error_occurred.emit("Invalid movie URL or ID format")
            return None

        payload = {
            "contentIds": [movie_id]
        }

        # DEBUG: Print request details
        print("\n" + "="*100)
        print("📤 SENDING REQUEST TO get-watch-content (MOVIE):")
        print("="*100)
        print(f"URL: {url}")
        print(f"Headers: {headers}")
        print(f"Payload: {payload}")
        print("="*100)

        try:
            response = requests.post(url, headers=headers, json=payload, timeout=30)

            if response.status_code == 200:
                data = response.json()

                # DEBUG: Print full API response
                print("\n" + "="*100)
                print("🔍 FULL API RESPONSE FROM get-watch-content (MOVIE):")
                print("="*100)
                import json
                print(json.dumps(data, indent=2, ensure_ascii=False))
                print("="*100)

                # Extract movie details
                try:
                    # Check if watchContents is empty
                    if not data.get("watchContents") or movie_id not in data["watchContents"]:
                        print(f"❌ Movie ID {movie_id} not found in watchContents, might be a series")
                        # Always emit error to trigger fallback, but mark it as silent if needed
                        if silent_fallback:
                            self.error_occurred.emit("SILENT_FALLBACK:Movie not found. This might be a series ID.")
                        else:
                            self.error_occurred.emit("Movie not found. This might be a series ID.")
                        return None

                    movie_data = data["watchContents"][movie_id]["movie"]["movie"]

                    # Always emit movie data without auto-show streams
                    # Let UI handle the display flow like series
                    print(f"✅ Movie found with {len(movie_data.get('streams', []))} streams")
                    self.content_found.emit({
                        'type': 'movie',
                        'data': movie_data,
                        'movie_id': movie_id
                    })

                    return movie_data
                except KeyError:
                    print(f"❌ KeyError: Movie ID {movie_id} structure not found, might be a series")
                    # Only emit error if this is not a silent fallback attempt
                    if not silent_fallback:
                        self.error_occurred.emit("Movie not found. This might be a series ID.")
                    return None
            elif response.status_code == 401:
                # Token expired, try to refresh
                new_token = self.refresh_access_token(silent_mode=True)
                if new_token:
                    headers['access_token'] = new_token
                    response = requests.post(url, headers=headers, json=payload, timeout=30)
                    if response.status_code == 200:
                        data = response.json()
                        try:
                            movie_data = data["watchContents"][movie_id]["movie"]["movie"]

                            # Always emit movie data without auto-show streams
                            print(f"✅ Movie found with {len(movie_data.get('streams', []))} streams")
                            self.content_found.emit({
                                'type': 'movie',
                                'data': movie_data,
                                'movie_id': movie_id
                            })

                            return movie_data
                        except KeyError:
                            self.error_occurred.emit("Movie details not found in response.")
                            return None
                self.error_occurred.emit("Authentication failed. Please login to OSN+ and export fresh cookies.")
                return None
            elif response.status_code == 403:
                self.error_occurred.emit("Access forbidden. Content may not be available in your region.")
                return None
            elif response.status_code == 404:
                self.error_occurred.emit("Movie not found. Please check the URL or ID.")
                return None
            else:
                self.error_occurred.emit(f"Failed to retrieve movie details. Status code: {response.status_code}")
                return None

        except requests.exceptions.Timeout:
            self.error_occurred.emit("Request timeout. Please check your internet connection.")
            return None
        except requests.exceptions.ConnectionError:
            self.error_occurred.emit("Connection error. Please check your internet connection.")
            return None
        except Exception as e:
            self.error_occurred.emit(f"Error fetching movie details: {str(e)}")
            return None

    def extract_series_id(self, series_link):
        """Extract series ID from link"""
        try:
            # Extract series ID from URL
            if "/series/" in series_link:
                series_id = series_link.split("/series/")[-1].split("-")[-1]
                return series_id
            return None
        except:
            return None

    def get_series_details(self, series_link, silent_fallback=False):
        """Get series details from API"""
        access_token = self.get_access_token()
        if not access_token:
            self.error_occurred.emit("No access token available. Please login first.")
            return None

        url = f"{self.auth_url}/osn/media/v1/get-series-page"

        headers = {
            'accept': '*/*',
            'accept-language': 'en-US,en;q=0.9',
            'access_token': access_token,
            'client_platform': 'web-osn',
            'client_version': '1.1.1',
            'content-type': 'application/json',
            'device_unique_identifier': '35b4ffb9-654f-4fca-a132-8ed1011d1927',
            'language': 'en',
            'origin': 'https://osnplus.com',
            'referer': 'https://osnplus.com/',
        }

        # Handle both URL and direct ID input
        if series_link.isdigit():
            # Direct ID input
            series_id = series_link
        else:
            # URL input - extract ID
            series_id = self.extract_series_id(series_link)
            if not series_id:
                self.error_occurred.emit("Invalid series link. Please check the URL.")
                return None

        payload = {
            "contentId": series_id
        }

        # DEBUG: Print request details
        print("\n" + "="*100)
        print("📤 SENDING REQUEST TO get-series-page:")
        print("="*100)
        print(f"URL: {url}")
        print(f"Headers: {headers}")
        print(f"Payload: {payload}")
        print("="*100)

        try:
            response = requests.post(url, headers=headers, json=payload, timeout=30)

            if response.status_code == 200:
                data = response.json()

                # DEBUG: Print full API response
                print("\n" + "="*100)
                print("🔍 FULL API RESPONSE FROM get-series-page:")
                print("="*100)
                import json
                print(json.dumps(data, indent=2, ensure_ascii=False))
                print("="*100)

                # Check if series was found
                if not data.get('notFound', True):
                    print(f"✅ Series found! notFound = {data.get('notFound')}")
                    self.content_found.emit({
                        'type': 'series',
                        'data': data,
                        'series_id': series_id
                    })
                    return data
                else:
                    print(f"❌ Series not found! notFound = {data.get('notFound')}")
                    # Only emit error if this is not a silent fallback attempt
                    if not silent_fallback:
                        self.error_occurred.emit("Series not found. Please check the URL or ID.")
                    return None
            elif response.status_code == 401:
                # Token expired, try to refresh
                new_token = self.refresh_access_token(silent_mode=True)
                if new_token:
                    headers['access_token'] = new_token
                    response = requests.post(url, headers=headers, json=payload, timeout=30)
                    if response.status_code == 200:
                        data = response.json()
                        if not data.get('notFound', True):
                            self.content_found.emit({
                                'type': 'series',
                                'data': data,
                                'series_id': series_id
                            })
                            return data
                        else:
                            # Only emit error if this is not a silent fallback attempt
                            if not silent_fallback:
                                self.error_occurred.emit("Series not found. Please check the URL or ID.")
                            return None
                self.error_occurred.emit("Authentication failed. Please login to OSN+ and export fresh cookies.")
                return None
            elif response.status_code == 403:
                self.error_occurred.emit("Access forbidden. Content may not be available in your region.")
                return None
            elif response.status_code == 404:
                self.error_occurred.emit("Series not found. Please check the URL or ID.")
                return None
            else:
                self.error_occurred.emit(f"Failed to retrieve series details. Status code: {response.status_code}")
                return None

        except requests.exceptions.Timeout:
            self.error_occurred.emit("Request timeout. Please check your internet connection.")
            return None
        except requests.exceptions.ConnectionError:
            self.error_occurred.emit("Connection error. Please check your internet connection.")
            return None
        except Exception as e:
            self.error_occurred.emit(f"Error fetching series details: {str(e)}")
            return None

    def get_episodes_by_season(self, season_id, series_title, season_number):
        """Get episodes for a specific season"""
        access_token = self.get_access_token()
        if not access_token:
            self.error_occurred.emit("No access token available. Please login first.")
            return None

        url = "https://api.osnplus.com/osn/media/v1/get-episodes-by-season"

        headers = {
            'accept': '*/*',
            'access_token': access_token,
            'client_platform': 'web-osn',
            'content-type': 'application/json',
        }

        payload = {"seasonContentIds": [season_id]}



        try:
            response = requests.post(url, headers=headers, json=payload, timeout=30)

            if response.status_code == 200:
                data = response.json()

                # DEBUG: Print full API response
                print("\n" + "="*100)
                print("🔍 FULL API RESPONSE FROM get-episodes-by-season:")
                print("="*100)
                import json
                print(json.dumps(data, indent=2, ensure_ascii=False))
                print("="*100)

                # Process episodes data like the original code
                try:
                    season_data = data["episodesBySeason"].get(season_id, {})
                    if season_data and "episodes" in season_data:
                        episodes = season_data["episodes"]
                        actual_season_number = season_data.get("seasonNumber", season_number)

                        # Emit signal with episodes data
                        self.episodes_found.emit({
                            'season_id': season_id,
                            'series_title': series_title,
                            'season_number': actual_season_number,
                            'episodes': episodes
                        })

                        # Display episodes table in console like original code
                        self.display_episodes_table(episodes, series_title, actual_season_number)

                        return episodes
                    else:
                        self.error_occurred.emit("No episodes found for this season.")
                        return None

                except KeyError as e:
                    self.error_occurred.emit(f"Error extracting episode details: {str(e)}")
                    return None

            elif response.status_code == 401:
                # Token expired, try to refresh
                new_token = self.refresh_access_token(silent_mode=True)
                if new_token:
                    headers['access_token'] = new_token
                    response = requests.post(url, headers=headers, json=payload, timeout=30)
                    if response.status_code == 200:
                        data = response.json()
                        season_data = data["episodesBySeason"].get(season_id, {})
                        if season_data and "episodes" in season_data:
                            episodes = season_data["episodes"]
                            actual_season_number = season_data.get("seasonNumber", season_number)

                            self.episodes_found.emit({
                                'season_id': season_id,
                                'series_title': series_title,
                                'season_number': actual_season_number,
                                'episodes': episodes
                            })

                            self.display_episodes_table(episodes, series_title, actual_season_number)
                            return episodes

                self.error_occurred.emit("Authentication failed. Please refresh your cookies.")
                return None
            else:
                self.error_occurred.emit(f"Failed to retrieve episodes. Status code: {response.status_code}")
                return None

        except requests.exceptions.Timeout:
            self.error_occurred.emit("Request timeout while fetching episodes.")
            return None
        except requests.exceptions.ConnectionError:
            self.error_occurred.emit("Connection error while fetching episodes.")
            return None
        except Exception as e:
            self.error_occurred.emit(f"Error fetching episodes: {str(e)}")
            return None

    def display_episodes_table(self, episodes, series_title, season_number):
        """Display episodes table in console like the original code"""
        try:
            from tabulate import tabulate

            table_data = []

            for index, episode in enumerate(episodes, start=1):
                episode_title = episode["title"].get("en", "Title not found")
                table_data.append([index, episode_title, season_number])

            print(f"\n{'='*80}")
            print(f"📺 EPISODES LIST - {series_title} Season {season_number}")
            print(f"{'='*80}")

            headers = ["Number", "Title", "Season"]
            table_output = tabulate(table_data, headers=headers, tablefmt="fancy_grid", colalign=("center", "left", "center"))
            print(table_output)

            print(f"\n{'='*80}")
            print("🎬 Episodes loaded successfully!")
            print(f"{'='*80}")

        except Exception as e:
            print(f"Error displaying episodes table: {str(e)}")
