#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CEF (Chromium Embedded Framework) Player Widget
The best solution for embedding Chromium in Qt applications
"""

import os
import sys
import time
import threading
import socketserver
import urllib.parse
import http.server
from PySide6.QtWidgets import <PERSON>W<PERSON><PERSON>, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QFrame, QApplication
from PySide6.QtCore import Qt, QTimer, Signal, QObject

# CEF imports
try:
    from cefpython3 import cefpython as cef
    CEF_AVAILABLE = True
    print("✅ CEF Python available")
except ImportError as e:
    CEF_AVAILABLE = False
    print(f"❌ CEF Python not available: {e}")

class CEFPlayerSignals(QObject):
    """Signals for CEF player events."""
    player_started = Signal(bool)
    player_stopped = Signal()
    player_error = Signal(str)
    page_loaded = Signal()

class SimpleHTTPHandler(http.server.SimpleHTTPRequestHandler):
    """Simple HTTP request handler with custom directory."""

    def __init__(self, *args, directory=None, **kwargs):
        if directory is None:
            directory = os.getcwd()
        self.directory = directory
        super().__init__(*args, directory=directory, **kwargs)

    def log_message(self, format, *args):
        """Log HTTP requests."""
        print(f"[HTTP] {format % args}")

    def end_headers(self):
        """Add CORS headers and security headers for CEF compatibility."""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS, PUT, DELETE')
        self.send_header('Access-Control-Allow-Headers', '*')
        self.send_header('Access-Control-Allow-Credentials', 'true')
        self.send_header('Cross-Origin-Embedder-Policy', 'unsafe-none')
        self.send_header('Cross-Origin-Opener-Policy', 'unsafe-none')
        self.send_header('Cross-Origin-Resource-Policy', 'cross-origin')
        self.send_header('X-Frame-Options', 'ALLOWALL')
        self.send_header('Content-Security-Policy', "default-src * 'unsafe-inline' 'unsafe-eval' data: blob:; media-src * data: blob:; script-src * 'unsafe-inline' 'unsafe-eval'; style-src * 'unsafe-inline';")
        super().end_headers()

class CEFPlayerWidget(QWidget):
    """Widget that embeds CEF (Chromium) for playing content."""

    def __init__(self, parent=None):
        super().__init__(parent)

        if not CEF_AVAILABLE:
            raise ImportError("CEF Python is not available")

        self.browser = None
        self.http_server = None
        self.server_thread = None
        self.player_url = None
        self.signals = CEFPlayerSignals()
        self.cef_initialized = False

        self.setup_ui()
        self.setup_cef()

    def setup_ui(self):
        """Setup the UI for the CEF player widget."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)

        # Create header with controls
        header = QFrame()
        header.setFixedHeight(60)
        header.setStyleSheet("""
            QFrame {
                background-color: #2b2b2b;
                border-bottom: 2px solid #444;
            }
        """)

        header_layout = QHBoxLayout(header)

        # Title
        title_label = QLabel("🚀 CEF Player (Chromium Embedded)")
        title_label.setStyleSheet("color: white; font-size: 16px; font-weight: bold;")
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # Controls
        self.refresh_btn = QPushButton("🔄 Refresh")
        self.refresh_btn.clicked.connect(self.refresh_browser)
        self.refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                padding: 8px 16px;
                border-radius: 4px;
                border: none;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        header_layout.addWidget(self.refresh_btn)

        self.devtools_btn = QPushButton("🔧 DevTools")
        self.devtools_btn.clicked.connect(self.show_devtools)
        self.devtools_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                padding: 8px 16px;
                border-radius: 4px;
                border: none;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        header_layout.addWidget(self.devtools_btn)

        self.close_btn = QPushButton("❌ Close")
        self.close_btn.clicked.connect(self.close_browser)
        self.close_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                padding: 8px 16px;
                border-radius: 4px;
                border: none;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
        """)
        header_layout.addWidget(self.close_btn)

        layout.addWidget(header)

        # Browser container
        self.browser_container = QFrame()
        self.browser_container.setMinimumSize(800, 600)  # Set minimum size
        self.browser_container.setStyleSheet("""
            QFrame {
                background-color: #000;
                border: 2px solid #444;
            }
        """)

        browser_layout = QVBoxLayout(self.browser_container)
        browser_layout.setContentsMargins(0, 0, 0, 0)

        # Status label
        self.status_label = QLabel("🚀 Initializing CEF (Chromium Embedded Framework)...")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("""
            color: white;
            font-size: 18px;
            padding: 50px;
            background-color: rgba(0,0,0,0.8);
        """)
        browser_layout.addWidget(self.status_label)

        layout.addWidget(self.browser_container)

    def setup_cef(self):
        """Initialize CEF."""
        try:
            print("🔧 Setting up CEF...")

            # CEF settings
            settings = {
                "debug": False,
                "log_severity": cef.LOGSEVERITY_INFO,
                "log_file": "",
                "multi_threaded_message_loop": False,
                "auto_zooming": "system_dpi",
                "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            }

            # CEF switches (command line arguments) - Enhanced for player compatibility
            switches = {
                "disable-web-security": "",
                "disable-features": "VizDisplayCompositor,CrossOriginOpenerPolicy",
                "autoplay-policy": "no-user-gesture-required",
                "allow-running-insecure-content": "",
                "disable-popup-blocking": "",
                "ignore-certificate-errors": "",
                "allow-file-access-from-files": "",
                "allow-file-access": "",
                "disable-site-isolation-trials": "",
                "disable-cors-checks": "",
                "allow-cross-origin-auth-prompt": "",
                "disable-background-timer-throttling": "",
                "disable-backgrounding-occluded-windows": "",
                "disable-renderer-backgrounding": "",
                "disable-field-trial-config": "",
                "enable-media-stream": "",
                "use-fake-ui-for-media-stream": "",
                "enable-usermedia-screen-capturing": "",
                "allow-http-screen-capture": "",
            }

            # Initialize CEF
            cef.Initialize(settings, switches)
            self.cef_initialized = True

            print("✅ CEF initialized successfully!")
            self.status_label.setText("✅ CEF initialized - Ready to load content!")

        except Exception as e:
            error_msg = f"Failed to initialize CEF: {e}"
            print(f"❌ {error_msg}")
            self.status_label.setText(f"❌ {error_msg}")
            self.signals.player_error.emit(error_msg)

    def start_http_server(self, player_dir):
        """Start HTTP server to serve player files."""
        for port in range(8002, 8010):
            try:
                print(f"Trying to start HTTP server on port {port}...")
                print(f"Serving files from directory: {player_dir}")

                # Verify the directory exists
                if not os.path.exists(player_dir):
                    print(f"❌ Player directory does not exist: {player_dir}")
                    return None

                # List files in the directory
                files = os.listdir(player_dir)
                print(f"Files in player directory: {files}")

                # Check if shahid_player.html exists
                html_file = os.path.join(player_dir, 'shahid_player.html')
                if os.path.exists(html_file):
                    print(f"✅ Found shahid_player.html at: {html_file}")
                else:
                    print(f"❌ shahid_player.html not found at: {html_file}")

                handler = lambda *args, **kwargs: SimpleHTTPHandler(
                    *args, directory=player_dir, **kwargs
                )

                self.http_server = socketserver.TCPServer(("localhost", port), handler)
                self.http_server.allow_reuse_address = True

                self.server_thread = threading.Thread(
                    target=self.http_server.serve_forever,
                    daemon=True
                )
                self.server_thread.start()

                print(f"✅ HTTP server started on port {port}")
                print(f"🌐 Server URL: http://localhost:{port}/")
                return port

            except OSError as e:
                if "Address already in use" in str(e):
                    continue
                else:
                    print(f"Error starting HTTP server: {e}")
                    break

        return None

    def stop_http_server(self):
        """Stop the HTTP server."""
        if self.http_server:
            try:
                self.http_server.shutdown()
                self.http_server.server_close()
                print("HTTP server stopped")
            except Exception as e:
                print(f"Error stopping HTTP server: {e}")
            finally:
                self.http_server = None
                self.server_thread = None

    def load_player(self, mpd_url, drm_key=None, player_dir=None):
        """Load the player in CEF browser."""
        try:
            if not self.cef_initialized:
                self.status_label.setText("❌ CEF not initialized")
                return False

            print(f"🚀 Loading player in CEF...")
            print(f"MPD URL: {mpd_url}")
            print(f"DRM Key: {drm_key}")

            # Start HTTP server
            if not player_dir:
                # Get player directory
                base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                player_dir = os.path.join(base_dir, 'player')

            port = self.start_http_server(player_dir)
            if not port:
                self.status_label.setText("❌ Failed to start HTTP server")
                return False

            # Create player URL
            query_params = {'mpd': mpd_url}
            if drm_key and ':' in drm_key:
                key_id, key = drm_key.split(':', 1)
                query_params['keyId'] = key_id
                query_params['key'] = key

            self.player_url = f"http://localhost:{port}/shahid_player.html?{urllib.parse.urlencode(query_params)}"
            print(f"Player URL: {self.player_url}")

            # Create CEF browser with delay to ensure UI is ready
            QTimer.singleShot(500, self.create_browser)

            return True

        except Exception as e:
            error_msg = f"Error loading CEF player: {e}"
            print(f"❌ {error_msg}")
            self.status_label.setText(f"❌ {error_msg}")
            self.signals.player_error.emit(error_msg)
            return False

    def create_browser(self):
        """Create the CEF browser widget."""
        try:
            print("🌐 Creating CEF browser...")

            # Make sure the container is visible and has proper size
            self.browser_container.show()
            self.browser_container.update()

            # Get window info
            window_info = cef.WindowInfo()

            # Get the container widget's window handle
            container_hwnd = int(self.browser_container.winId())
            print(f"🔧 Container HWND: {container_hwnd}")

            # Set parent window with proper size
            rect = self.browser_container.rect()
            print(f"🔧 Container rect: {rect.width()}x{rect.height()}")

            # Ensure minimum size
            width = max(rect.width(), 800)
            height = max(rect.height(), 600)

            window_info.SetAsChild(container_hwnd, [0, 0, width, height])

            # Browser settings - Only valid CEF settings
            browser_settings = {
                "web_security_disabled": True,
                "file_access_from_file_urls_allowed": True,
                "universal_access_from_file_urls_allowed": True,
                "javascript_disabled": False,
                "plugins_disabled": False,
                "java_disabled": True,
                "application_cache_disabled": False,
                "databases_disabled": False,
                "local_storage_disabled": False,
            }

            print(f"🌐 Creating browser with URL: {self.player_url}")

            # Create browser
            self.browser = cef.CreateBrowserSync(
                window_info=window_info,
                url=self.player_url,
                settings=browser_settings
            )

            if not self.browser:
                raise Exception("Failed to create CEF browser - browser is None")

            # Set client handlers
            client_handlers = ClientHandlers(self)
            self.browser.SetClientHandler(client_handlers)

            print("✅ CEF browser created successfully!")
            self.status_label.setText("🌐 Loading player...")

            # Load the actual player URL after browser is created
            QTimer.singleShot(1000, self.load_player_url)

            # Hide status label after a delay
            QTimer.singleShot(3000, self.status_label.hide)

            self.signals.player_started.emit(True)

        except Exception as e:
            error_msg = f"Error creating CEF browser: {e}"
            print(f"❌ {error_msg}")
            self.status_label.setText(f"❌ {error_msg}")
            self.signals.player_error.emit(error_msg)

    def load_player_url(self):
        """Load the actual player URL after browser is ready."""
        if self.browser and self.player_url:
            print(f"🔄 Loading player URL: {self.player_url}")
            try:
                self.browser.LoadUrl(self.player_url)
                print("✅ Player URL loaded successfully!")
            except Exception as e:
                print(f"❌ Error loading player URL: {e}")

    def refresh_browser(self):
        """Refresh the CEF browser."""
        if self.browser:
            print("🔄 Refreshing CEF browser...")
            self.browser.Reload()
            self.status_label.setText("🔄 Refreshing...")
            self.status_label.show()
            QTimer.singleShot(2000, self.status_label.hide)

    def show_devtools(self):
        """Show CEF developer tools."""
        if self.browser:
            print("🔧 Opening CEF DevTools...")
            self.browser.ShowDevTools()

    def close_browser(self):
        """Close the CEF browser and cleanup."""
        try:
            if self.browser:
                print("🗑️ Closing CEF browser...")
                self.browser.CloseBrowser(True)
                self.browser = None

            self.stop_http_server()
            self.status_label.setText("🔄 Browser closed")
            self.status_label.show()
            self.signals.player_stopped.emit()

        except Exception as e:
            print(f"Error closing CEF browser: {e}")

    def resizeEvent(self, event):
        """Handle resize events to resize CEF browser."""
        super().resizeEvent(event)

        if self.browser:
            try:
                # Resize CEF browser to match container
                rect = self.browser_container.rect()
                self.browser.SetBounds(0, 0, rect.width(), rect.height())
            except Exception as e:
                print(f"Error resizing CEF browser: {e}")

    def closeEvent(self, event):
        """Handle widget close event."""
        self.close_browser()
        super().closeEvent(event)

class ClientHandlers:
    """CEF client handlers for events."""

    def __init__(self, player_widget):
        self.player_widget = player_widget

    def OnLoadEnd(self, browser, **_):
        """Called when page loading is complete."""
        print("✅ CEF page loaded successfully!")
        self.player_widget.status_label.setText("✅ Player loaded successfully!")

        # Force browser to be visible
        if self.player_widget.browser:
            print("🔧 Forcing browser visibility...")
            try:
                # Try to make browser visible
                self.player_widget.browser.WasResized()
                self.player_widget.browser.SendFocusEvent(True)
                print("🔧 Browser visibility commands sent")
            except Exception as e:
                print(f"⚠️ Error setting browser visibility: {e}")

        QTimer.singleShot(2000, self.player_widget.status_label.hide)
        self.player_widget.signals.page_loaded.emit()

    def OnLoadError(self, browser, frame, error_code, error_text, failed_url, **_):
        """Called when page loading fails."""
        error_msg = f"CEF load error: {error_text} ({error_code})"
        print(f"❌ {error_msg}")
        self.player_widget.status_label.setText(f"❌ {error_msg}")
        self.player_widget.signals.player_error.emit(error_msg)

# Function to create CEF player widget
def create_cef_player(mpd_url, drm_key=None, parent=None):
    """
    Create a CEF player widget.

    Args:
        mpd_url (str): The URL of the MPD file to play
        drm_key (str, optional): The DRM key for decryption in the format "kid:key"
        parent (QWidget, optional): Parent widget for the player

    Returns:
        CEFPlayerWidget: The player widget, or None if failed
    """
    try:
        if not CEF_AVAILABLE:
            print("❌ CEF Python is not available")
            return None

        print("🚀 Creating CEF player...")
        widget = CEFPlayerWidget(parent)

        # Get player directory
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        player_dir = os.path.join(base_dir, 'player')

        success = widget.load_player(mpd_url, drm_key, player_dir)

        if success:
            print("✅ CEF player created successfully")
            return widget
        else:
            print("❌ Failed to create CEF player")
            return None

    except Exception as e:
        print(f"❌ Error in create_cef_player: {e}")
        import traceback
        traceback.print_exc()
        return None

# CEF message loop integration
def cef_message_loop_work():
    """Perform CEF message loop work."""
    if CEF_AVAILABLE:
        cef.MessageLoopWork()

def shutdown_cef():
    """Shutdown CEF properly."""
    if CEF_AVAILABLE:
        print("🔄 Shutting down CEF...")
        cef.Shutdown()
