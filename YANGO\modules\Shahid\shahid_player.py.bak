#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Shahid Player Module
This module handles playing MPD content with the custom player in the player directory
"""

import os
import threading
import urllib.parse
import webbrowser
from http.server import HTTPServer, SimpleHTTPRequestHandler
from PySide6.QtCore import QObject, Signal
from PySide6.QtWidgets import Q<PERSON><PERSON>og, QVBoxLayout
from PySide6.QtWebEngineWidgets import QWebEngineView
from PySide6.QtCore import QUrl, Qt

class PlayerEvents:
    """Event handlers for player events."""
    def __init__(self):
        self.on_player_started = None  # Callback for player started
        self.on_player_stopped = None  # Callback for player stopped
        self.on_player_error = None    # Callback for player error
        
    def emit_player_started(self, success):
        """Emit player started event."""
        if self.on_player_started:
            self.on_player_started(success)
            
    def emit_player_stopped(self):
        """Emit player stopped event."""
        if self.on_player_stopped:
            self.on_player_stopped()
            
    def emit_player_error(self, error_msg):
        """Emit player error event."""
        if self.on_player_error:
            self.on_player_error(error_msg)

class PlayerWindow(QDialog):
    """Window for displaying the Player."""
    def __init__(self, url, parent=None, on_close_callback=None):
        super().__init__(parent)
        self.setWindowTitle("Shahid Player")
        self.resize(1280, 720)
        self.player_url = url  # Store URL for reference
        self.on_close_callback = on_close_callback  # Store callback function
        
        # Create layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # Create web view
        self.web_view = QWebEngineView()

        # Connect signals for debugging
        self.web_view.loadStarted.connect(lambda: print("Web page load started"))
        self.web_view.loadProgress.connect(lambda p: print(f"Web page load progress: {p}%"))
        self.web_view.loadFinished.connect(lambda ok: print(f"Web page load finished: {'Success' if ok else 'Failed'}"))
        
        # Enable JavaScript console logging
        self.web_view.page().javaScriptConsoleMessage.connect(self._handle_js_console)
        
        # Load the URL
        print(f"Loading URL in WebView: {url}")
        self.web_view.load(QUrl(url))
        layout.addWidget(self.web_view)
        
        # Set dialog to delete on close
        self.setAttribute(Qt.WA_DeleteOnClose)
        
    def _handle_js_console(self, level, message, line, source):
        """Handle JavaScript console messages."""
        level_str = {
            0: "DEBUG",
            1: "INFO",
            2: "WARNING",
            3: "ERROR"
        }.get(level, "UNKNOWN")
        
        print(f"JS {level_str}: {message} (line {line}, source: {source})")
        
    def closeEvent(self, event):
        """Handle window close event."""
        print("Player window closeEvent called")
        # Clean up web view
        self.web_view.stop()
        self.web_view.page().deleteLater()
        self.web_view.deleteLater()
        
        # Call the callback function if provided
        if self.on_close_callback:
            print("Calling on_close_callback")
            self.on_close_callback()
        
        # Call parent class closeEvent
        super().closeEvent(event)

class CustomHTTPRequestHandler(SimpleHTTPRequestHandler):
    """Custom HTTP request handler for the player."""
    def __init__(self, *args, player_dir=None, **kwargs):
        self.player_dir = player_dir
        super().__init__(*args, **kwargs)
    
    def translate_path(self, path):
        """Translate URL path to file system path."""
        # Remove query parameters
        path = path.split('?', 1)[0]
        path = path.split('#', 1)[0]
        
        # Normalize path
        path = path.strip('/')
        
        # If path is empty, serve index.html
        if not path:
            path = 'index.html'
        
        # Join with player directory
        return os.path.join(self.player_dir, path)
    
    def log_message(self, format, *args):
        """Override to customize logging."""
        print(f"[HTTP] {format % args}")

class PlayerSignals(QObject):
    """Signals for player events."""
    player_started = Signal(bool)  # success
    player_stopped = Signal()
    player_error = Signal(str)  # error message

class ShahidPlayer:
    def __init__(self, parent=None):
        """
        Initialize the Shahid Player module.
        
        Args:
            parent (QWidget, optional): Parent widget for the player window
        """
        # Get the script directory (parent of the modules directory)
        self.dir_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.player_dir = os.path.join(self.dir_path, 'player')
        print(f"Player initialized with base directory: {self.dir_path}")
        print(f"Player directory: {self.player_dir}")

        self.signals = PlayerSignals()
        self.events = PlayerEvents()  # For compatibility with both signal systems
        self.http_server = None
        self.server_thread = None
        self.webview_window = None
        self.parent = parent
    
    def _start_http_server(self):
        """
        Start a local HTTP server to serve the player files.
        
        Returns:
            tuple: (success, port)
        """
        # Find an available port
        port = 8002  # Default port
        
        # Create a custom request handler with the player directory
        handler = lambda *args, **kwargs: CustomHTTPRequestHandler(
            *args, 
            player_dir=self.player_dir,
            **kwargs
        )
        
        try:
            # Create and start the HTTP server
            self.http_server = HTTPServer(("localhost", port), handler)
            self.http_server.allow_reuse_address = True
            
            # Start the server in a separate thread
            self.server_thread = threading.Thread(
                target=self.http_server.serve_forever,
                daemon=True
            )
            self.server_thread.start()
            
            print(f"Started HTTP server on port {port}")
            return True, port
        except Exception as e:
            print(f"Error starting HTTP server: {e}")
            self._stop_http_server()
            return False, None
    
    def _stop_http_server(self):
        """Stop the HTTP server."""
        if self.http_server:
            try:
                self.http_server.shutdown()
                self.http_server.server_close()
                print("Stopped HTTP server")
            except Exception as e:
                print(f"Error stopping HTTP server: {e}")
            finally:
                self.http_server = None
                self.server_thread = None
    
    def play(self, mpd_url, drm_key=None):
        """
        Play a video using the player.
        
        Args:
            mpd_url (str): The MPD URL to play
            drm_key (str, optional): The DRM key in the format "kid:key"
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Stop any existing player
            self.stop()
            
            # Validate inputs
            if not mpd_url:
                error_msg = "MPD URL is required"
                print(f"ERROR: {error_msg}")
                self.signals.player_error.emit(error_msg)
                self.events.emit_player_error(error_msg)
                return False
            
            # Parse the DRM key
            if drm_key and ':' in drm_key:
                key_id, key = drm_key.split(':', 1)
                print(f"Using DRM key - KID: {key_id}, KEY: {key}")
            else:
                key_id, key = None, None
                print("Warning: Invalid DRM key format. Expected 'kid:key'")
            
            # Start the HTTP server
            success, port = self._start_http_server()
            if not success or not port:
                error_msg = "Failed to start HTTP server"
                print(f"ERROR: {error_msg}")
                self.signals.player_error.emit(error_msg)
                self.events.emit_player_error(error_msg)
                return False
            
            # Create the player URL with query parameters
            query_params = {
                'mpd': mpd_url
            }
            
            if key_id and key:
                query_params['keyId'] = key_id
                query_params['key'] = key
            
            player_url = f"http://localhost:{port}/?{urllib.parse.urlencode(query_params)}"
            print(f"Player URL: {player_url}")
            
            # Create and show player window
            print("Creating player window...")
            try:
                self.webview_window = PlayerWindow(player_url, self.parent, on_close_callback=self._on_player_closed)
                print("Showing player window...")
                self.webview_window.show()
                
                # Emit player started event
                self.signals.player_started.emit(True)
                self.events.emit_player_started(True)
                
                return True
            except Exception as e:
                error_msg = f"Error starting player: {e}"
                print(f"ERROR: {error_msg}")
                self._stop_http_server()
                self.signals.player_error.emit(error_msg)
                self.events.emit_player_error(error_msg)
                return False
                
        except Exception as e:
            error_msg = f"Error starting player: {e}"
            print(f"ERROR: {error_msg}")
            self._stop_http_server()
            self.signals.player_error.emit(error_msg)
            self.events.emit_player_error(error_msg)
            return False
    
    def _on_player_closed(self):
        """Handle player window closed event."""
        print("Player window closed callback received")
        # Set window reference to None
        self.webview_window = None
        # Stop the HTTP server
        self._stop_http_server()
        # Emit the player stopped signal
        self.signals.player_stopped.emit()
        self.events.emit_player_stopped()
        print("Player cleanup completed")
    
    def stop(self):
        """Stop the player."""
        if self.webview_window:
            print("Stopping player...")
            self.webview_window.close()
            self.webview_window = None
        
        self._stop_http_server()
        self.signals.player_stopped.emit()
        self.events.emit_player_stopped()
        return True

# Function to play content directly without creating a class instance
def play_mpd(mpd_url, drm_key=None, parent=None):
    """
    Play MPD content with the custom player (standalone function).

    Args:
        mpd_url (str): The URL of the MPD file to play
        drm_key (str, optional): The DRM key for decryption in the format "kid:key"
        parent (QWidget, optional): Parent widget for the player window

    Returns:
        bool: True if player started successfully, False otherwise
    """
    player = ShahidPlayer(parent)
    return player.play(mpd_url, drm_key)
