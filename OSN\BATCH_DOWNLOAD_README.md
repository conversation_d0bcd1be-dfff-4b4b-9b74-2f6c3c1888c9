# ميزة التحميل المتتالي للحلقات - Batch Episode Download

## نظرة عامة

تم إضافة ميزة التحميل المتتالي للحلقات إلى تطبيق OSN+ Downloader، والتي تتيح للمستخدمين تحميل عدة حلقات من المسلسل بشكل متتالي وليس متزامن.

## الميزات الجديدة

### 1. واجهة التحميل المتتالي
- **تبويب خيارات التحميل محسّن**: تم إضافة قسم "Batch Download Options" في تبويب Download Options
- **اختيار نطاق الحلقات**: إمكانية اختيار حلقات محددة (من الحلقة X إلى الحلقة Y)
- **تحميل جميع الحلقات**: خيار لتحميل جميع حلقات الموسم
- **إعدادات موحدة**: اختيار الجودة والصوت والترجمة مرة واحدة لجميع الحلقات

### 2. التحميل المتتالي
- **تحميل متسلسل**: تحميل حلقة واحدة في كل مرة لتجنب إرهاق الخادم
- **تتبع التقدم**: عرض تقدم التحميل لكل حلقة على حدة
- **معالجة الأخطاء**: إمكانية المتابعة أو التوقف عند حدوث خطأ
- **حفظ الإعدادات**: استخدام نفس إعدادات الجودة والصوت لجميع الحلقات

## كيفية الاستخدام

### 1. البحث عن المسلسل
1. أدخل رابط المسلسل في حقل البحث
2. انقر على "Search" للبحث عن المسلسل
3. انقر على "Continue" لتحميل المواسم والحلقات

### 2. اختيار الموسم والحلقات
1. في تبويب "Seasons"، اختر الموسم المطلوب
2. ستظهر قائمة بجميع حلقات الموسم
3. انتقل إلى تبويب "Download Options"

### 3. إعداد التحميل المتتالي
1. في قسم "Batch Download Options":
   - **اختيار النطاق**: 
     - ✅ "Download All Episodes" لتحميل جميع الحلقات
     - أو حدد نطاق معين باستخدام "From Episode" و "To Episode"
   
   - **اختيار الجودة**: اختر الجودة المطلوبة من القائمة المنسدلة
   - **اختيار الصوت**: اختر لغة الصوت (عربي/إنجليزي)
   - **اختيار الترجمة**: اختر لغة الترجمة أو "No Subtitles"

2. انقر على "Start Batch Download" لبدء التحميل

### 4. متابعة التحميل
- سيتم عرض تقدم التحميل في شريط الحالة
- سيتم تحميل الحلقات واحدة تلو الأخرى
- في حالة حدوث خطأ، ستظهر رسالة تسأل عن المتابعة أو التوقف

## التحسينات التقنية

### 1. ملف `osn_ui.py`
- إضافة واجهة التحميل المتتالي في `setup_download_options_tab()`
- دوال إدارة التحميل المتتالي:
  - `start_batch_download()`
  - `download_next_episode_in_batch()`
  - `handle_episode_download_completed()`
  - `show_batch_download_options_for_series()`

### 2. ملف `osn_downloader.py`
- تحسين دالة `_download_episode_thread()` للتعامل مع بيانات المسلسل المختلفة
- تحسين استخراج معرف الجودة من البيانات

### 3. ملف `main.py`
- تعديل `handle_download_completed()` لدعم التحميل المتتالي
- ربط إشارات التحميل بدوال التحميل المتتالي

## المتغيرات الجديدة

### في `OSNUi` class:
```python
# متغيرات التحميل المتتالي
self.current_episodes_list = []          # قائمة الحلقات الحالية
self.current_season_data = None          # بيانات الموسم الحالي
self.batch_download_settings = {         # إعدادات التحميل المتتالي
    'quality': None,
    'audio': None,
    'subtitle': None,
    'stream_data': None
}
self.batch_download_queue = []           # طابور التحميل
self.current_batch_index = 0             # فهرس الحلقة الحالية
self.is_batch_downloading = False        # حالة التحميل المتتالي
```

## ملفات الاختبار

### `test_batch_download.py`
ملف اختبار شامل للتأكد من عمل الميزة الجديدة:
```bash
python test_batch_download.py
```

## الاستخدام المتقدم

### معالجة الأخطاء
- عند حدوث خطأ في تحميل حلقة، يمكن للمستخدم:
  - المتابعة مع الحلقة التالية
  - إيقاف التحميل المتتالي

### إعدادات مخصصة
- يمكن تخصيص إعدادات مختلفة لكل حلقة (مستقبلاً)
- دعم تحميل مواسم متعددة (مستقبلاً)

## المتطلبات

- PySide6 >= 6.0.0
- جميع المتطلبات الأساسية للتطبيق
- ملفات الكوكيز الصحيحة من OSN+

## الملاحظات المهمة

1. **التحميل المتتالي**: الحلقات تُحمل واحدة تلو الأخرى لتجنب إرهاق الخادم
2. **حفظ الإعدادات**: يتم استخدام نفس إعدادات الجودة والصوت لجميع الحلقات
3. **معالجة الأخطاء**: يمكن المتابعة عند حدوث خطأ في حلقة واحدة
4. **التوافق**: الميزة متوافقة مع النظام الحالي للتحميل

## التطوير المستقبلي

- دعم تحميل مواسم متعددة
- إعدادات مخصصة لكل حلقة
- استئناف التحميل المتوقف
- تحميل متوازي محدود (2-3 حلقات)
- جدولة التحميل
