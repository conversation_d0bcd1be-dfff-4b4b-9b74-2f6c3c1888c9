import os
import subprocess
import requests
import threading
import shutil
import glob
import time
import xml.etree.ElementTree as ET
from PySide6.QtCore import QObject, Signal, QThread, QTimer
from pathlib import Path

class OSNDownloader(QObject):
    # Signals for UI updates
    download_progress = Signal(int, str)  # progress percentage, status message
    download_completed = Signal(str, bool)  # file path, success
    download_error = Signal(str)  # error message
    status_update = Signal(str)  # status message for enhanced solution
    video_progress = Signal(int)  # video progress signal
    audio_progress = Signal(int)  # audio progress signal
    subtitle_progress = Signal(int)  # subtitle progress signal

    # New signals for sequential downloading
    episode_completed = Signal(int, str, bool)  # episode_number, file_path, success
    all_episodes_completed = Signal()  # All episodes finished
    request_next_episode = Signal()  # Request next episode in sequence

    def __init__(self):
        super().__init__()
        self.setup_paths()
        self.current_downloads = {}
        self.current_poster_url = None  # Store poster URL for downloads

        # Sequential download state
        self.sequential_download_active = False
        self.current_episode_index = 0
        self.episodes_to_download = []
        self.quality_preferences = None

        # Dynamic Progress System - tracks real-time progress
        self.dynamic_progress = {
            'video': {'progress': 0, 'weight': 70, 'active': False, 'completed': False},
            'audio': {'progress': 0, 'weight': 20, 'active': False, 'completed': False},
            'subtitle': {'progress': 0, 'weight': 5, 'active': False, 'completed': False},
            'merge': {'progress': 0, 'weight': 5, 'active': False, 'completed': False}
        }
        self.last_unified_progress = 0
        self.progress_timer = None
        self.download_active = False
        self.file_monitor_progress = 0  # Track real file progress

        # Completion tracking to prevent duplicate signals
        self._completion_sent = False
        self._completed_files = set()

    def _emit_completion_once(self, file_path, success=True):
        """Emit completion signal only once per file to prevent duplicates"""
        try:
            file_path_str = str(file_path)

            # Check if we already sent completion for this file
            if file_path_str in self._completed_files:
                print(f"⚠️ Completion already sent for: {file_path_str} - Skipping duplicate")
                return False

            # Add to completed files set
            self._completed_files.add(file_path_str)

            # Emit the completion signal
            self.download_completed.emit(file_path_str, success)
            print(f"✅ Completion signal sent for: {file_path_str}")
            return True

        except Exception as e:
            print(f"❌ Error in completion emission: {str(e)}")
            return False

    def _reset_completion_tracking(self):
        """Reset completion tracking for new download"""
        self._completion_sent = False
        self._completed_files.clear()
        print("🔄 Completion tracking reset")

    def update_dynamic_progress(self, component, progress, message=""):
        """Update dynamic progress system that keeps moving continuously"""
        try:
            # Update the specific component progress and mark as active
            if component in self.dynamic_progress:
                self.dynamic_progress[component]['progress'] = min(progress, 100)
                self.dynamic_progress[component]['active'] = True

                # Mark as completed if progress is 100%
                if progress >= 100:
                    self.dynamic_progress[component]['completed'] = True
                    self.dynamic_progress[component]['active'] = False

                # Calculate dynamic weighted progress
                total_progress = 0
                active_components = []

                for comp, data in self.dynamic_progress.items():
                    if data['completed']:
                        # Completed components contribute their full weight
                        component_contribution = data['weight']
                        total_progress += component_contribution
                    elif data['active']:
                        # Active components contribute proportionally
                        component_contribution = (data['progress'] / 100) * data['weight']
                        total_progress += component_contribution
                        active_components.append(comp)
                    # Inactive components contribute 0

                # Round to integer - allow 100% when merge is completed
                if self.dynamic_progress['merge']['completed']:
                    unified_progress = 100
                else:
                    unified_progress = min(int(total_progress), 99)  # Never reach 100% until merge is done

                # Always emit progress (even if same) to keep UI responsive
                # Create unified status message
                if message:
                    status_message = message
                else:
                    # Create status based on active components
                    if active_components:
                        active_names = []
                        for comp in active_components:
                            comp_progress = self.dynamic_progress[comp]['progress']
                            if comp == 'video':
                                active_names.append(f"📹 Video: {comp_progress}%")
                            elif comp == 'audio':
                                active_names.append(f"🔊 Audio: {comp_progress}%")
                            elif comp == 'subtitle':
                                active_names.append(f"📝 Subtitles: {comp_progress}%")
                            elif comp == 'merge':
                                active_names.append(f"🔧 Merging: {comp_progress}%")

                        status_message = f"{' | '.join(active_names)} (Total: {unified_progress}%)"
                    else:
                        status_message = f"📥 Downloading: {unified_progress}%"

                # Don't emit progress here - let the smooth animation thread handle it
                # Just update the component data and let the animation thread do the smooth movement
                print(f"🎯 Component Updated: {component} -> {progress}% (Target will be: {unified_progress}%)")

                # Debug info
                print(f"📊 Component status: Video={self.dynamic_progress['video']['progress']}%{'✅' if self.dynamic_progress['video']['completed'] else '🔄' if self.dynamic_progress['video']['active'] else '⏸️'}, Audio={self.dynamic_progress['audio']['progress']}%{'✅' if self.dynamic_progress['audio']['completed'] else '🔄' if self.dynamic_progress['audio']['active'] else '⏸️'}, Subtitle={self.dynamic_progress['subtitle']['progress']}%{'✅' if self.dynamic_progress['subtitle']['completed'] else '🔄' if self.dynamic_progress['subtitle']['active'] else '⏸️'}, Merge={self.dynamic_progress['merge']['progress']}%{'✅' if self.dynamic_progress['merge']['completed'] else '🔄' if self.dynamic_progress['merge']['active'] else '⏸️'}")

                # Update last progress
                self.last_unified_progress = unified_progress

        except Exception as e:
            print(f"⚠️ Error updating dynamic progress: {e}")
            # Fallback to direct emission
            self.download_progress.emit(progress, message or f"{component}: {progress}%")

    def start_continuous_progress_monitor(self):
        """Start smooth continuous progress monitoring with real-time animation"""
        try:
            import threading
            import time

            self.download_active = True
            self.smooth_progress = 0
            self.target_progress = 0

            def smooth_progress_animation():
                """Smooth progress animation that moves 1% at a time"""
                while self.download_active:
                    try:
                        # Calculate target progress from components
                        total_progress = 0
                        for comp, data in self.dynamic_progress.items():
                            if data['completed']:
                                total_progress += data['weight']
                            elif data['active']:
                                component_contribution = (data['progress'] / 100) * data['weight']
                                total_progress += component_contribution

                        # Allow 100% when merge is completed
                        if self.dynamic_progress['merge']['completed']:
                            self.target_progress = 100
                        else:
                            self.target_progress = min(int(total_progress), 99)

                        # Smooth incremental movement - move 1% at a time
                        if self.smooth_progress < self.target_progress:
                            self.smooth_progress += 1

                            # Create status message based on active components
                            active_components = [comp for comp, data in self.dynamic_progress.items() if data['active']]
                            if self.smooth_progress == 100:
                                status_message = "✅ Download completed successfully!"
                            elif active_components:
                                status_message = f"📥 {', '.join(active_components).title()}: {self.smooth_progress}%"
                            else:
                                status_message = f"📥 Processing: {self.smooth_progress}%"

                            # Emit smooth progress
                            self.download_progress.emit(self.smooth_progress, status_message)
                            print(f"🎯 Smooth Progress: {self.smooth_progress}% -> Target: {self.target_progress}%")

                        time.sleep(0.2)  # Update every 200ms for smooth animation

                    except Exception as e:
                        print(f"⚠️ Error in smooth progress animation: {e}")
                        time.sleep(1)

            # Start smooth animation thread
            animation_thread = threading.Thread(target=smooth_progress_animation, daemon=True)
            animation_thread.start()
            print("🔄 Started smooth continuous progress monitor")

        except Exception as e:
            print(f"⚠️ Error starting continuous monitor: {e}")

    def stop_continuous_progress_monitor(self):
        """Stop continuous progress monitoring"""
        self.download_active = False
        print("⏹️ Stopped continuous progress monitor")

    def reset_dynamic_progress(self):
        """Reset dynamic progress system for new download"""
        try:
            for component in self.dynamic_progress:
                self.dynamic_progress[component]['progress'] = 0
                self.dynamic_progress[component]['active'] = False
                self.dynamic_progress[component]['completed'] = False
            self.last_unified_progress = 0
            self.download_active = False
            print("🔄 Dynamic progress system reset")
        except Exception as e:
            print(f"⚠️ Error resetting dynamic progress: {e}")

    def setup_paths(self):
        """Setup download paths and binaries"""
        # Point to YANGO root directory (3 levels up from OSN module)
        self.base_dir = Path(__file__).parent.parent.parent
        self.downloads_dir = self.base_dir / "downloads"
        self.cache_dir = self.base_dir / "cache"
        self.keys_dir = self.base_dir / "KEYS"
        self.binaries_dir = self.base_dir / "binaries"

        # Create directories if they don't exist
        self.downloads_dir.mkdir(exist_ok=True)
        self.cache_dir.mkdir(exist_ok=True)
        self.keys_dir.mkdir(exist_ok=True)

        # Binary paths
        self.n_m3u8dl_path = self.binaries_dir / "N_m3u8DL-RE.exe"
        self.yt_dlp_path = self.binaries_dir / "yt-dlp.exe"
        self.aria2c_path = self.binaries_dir / "aria2c.exe"  # Add aria2c path
        self.mp4decrypt_path = self.binaries_dir / "mp4decrypt.exe"
        self.mkvmerge_path = self.binaries_dir / "mkvmerge.exe"  # Add mkvmerge path
        self.ffmpeg_path = self.binaries_dir / "ffmpeg.exe"
        self.keys_file = self.keys_dir / "KEYS.txt"

        # Download method preference: yt-dlp (more stable) or N_m3u8DL-RE
        self.use_yt_dlp = False  # Set to True to use yt-dlp, False for N_m3u8DL-RE


    def sanitize_filename(self, filename):
        """Sanitize filename for Windows"""
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '')
        return filename.strip()

    def parse_mpd_file(self, mpd_url):
        """Parse MPD file to extract available qualities and tracks - Enhanced version"""
        try:
            print(f"📄 Parsing MPD file: {mpd_url}")
            response = requests.get(mpd_url)
            response.raise_for_status()

            root = ET.fromstring(response.content)
            namespaces = {'mpd': 'urn:mpeg:dash:schema:mpd:2011'}

            qualities = []
            audio_tracks = []
            subtitle_tracks = []

            # Extract video qualities with detailed information
            for adaptation_set in root.findall('.//mpd:Period/mpd:AdaptationSet', namespaces):
                mime_type = adaptation_set.get('mimeType', '')
                content_type = adaptation_set.get('contentType', '')

                # Video qualities
                if 'video' in mime_type or content_type == 'video':
                    for representation in adaptation_set.findall('.//mpd:Representation', namespaces):
                        width = representation.get('width')
                        height = representation.get('height')
                        bandwidth = representation.get('bandwidth')
                        codecs = representation.get('codecs', '')
                        uuid = representation.get('id')  # This is the crucial UUID for N_m3u8DL-RE

                        print(f"🎬 Found video representation: {width}x{height} - {bandwidth} bps - UUID: {uuid} - Codecs: {codecs}")

                        if width and height and uuid:  # Make sure we have UUID
                            quality_label = self.get_quality_label(int(height)) if height.isdigit() else 'Unknown'
                            quality_info = {
                                'resolution': f"{width}x{height}",
                                'bandwidth': bandwidth,
                                'codecs': codecs,
                                'uuid': uuid,  # Store the UUID for download command
                                'id': uuid,    # Also store as 'id' for compatibility
                                'quality_label': quality_label
                            }
                            print(f"🎬 Creating quality: {quality_label} ({width}x{height}) - UUID: {uuid} - Bandwidth: {bandwidth}")
                            qualities.append(quality_info)

                # Audio tracks
                elif 'audio' in mime_type or content_type == 'audio':
                    lang = adaptation_set.get('lang', 'unknown')
                    for representation in adaptation_set.findall('.//mpd:Representation', namespaces):
                        codecs = representation.get('codecs', '')
                        uuid = representation.get('id')
                        bandwidth = representation.get('bandwidth')

                        # Determine audio type based on codecs
                        audio_type = 'Unknown'
                        if 'ec-3' in codecs or 'eac3' in codecs:
                            audio_type = 'Dolby Digital Plus'
                        elif 'ac-3' in codecs:
                            audio_type = 'Dolby Digital'
                        elif 'mp4a' in codecs:
                            audio_type = 'AAC'

                        audio_info = {
                            'language': lang,
                            'codecs': codecs,
                            'id': uuid,
                            'bandwidth': bandwidth,
                            'type': audio_type,
                            'display_name': f"{lang} - {audio_type}"
                        }
                        audio_tracks.append(audio_info)
                        print(f"🎵 Found audio track: {lang} - {audio_type} (ID: {uuid})")

                # Subtitle tracks
                elif 'text' in mime_type or content_type == 'text':
                    lang = adaptation_set.get('lang', 'unknown')
                    subtitle_info = {
                        'language': lang,
                        'display_name': f"{lang} subtitles"
                    }
                    subtitle_tracks.append(subtitle_info)
                    print(f"📝 Found subtitle track: {lang}")

            # Sort qualities by bandwidth (highest first)
            qualities.sort(key=lambda x: int(x.get('bandwidth', 0)), reverse=True)

            print(f"📊 Found {len(qualities)} video qualities, {len(audio_tracks)} audio tracks, {len(subtitle_tracks)} subtitle tracks")

            # Print detailed quality information
            print(f"📋 DETAILED QUALITY LIST:")
            for i, quality in enumerate(qualities):
                print(f"📋   Quality {i+1}: {quality.get('quality_label', 'Unknown')} ({quality.get('resolution', 'Unknown')}) - UUID: {quality.get('uuid', quality.get('id', 'No UUID'))} - Bandwidth: {quality.get('bandwidth', 'Unknown')}")

            return {
                'qualities': qualities,
                'audio_tracks': audio_tracks,
                'subtitle_tracks': subtitle_tracks
            }

        except Exception as e:
            print(f"❌ Error parsing MPD file: {str(e)}")
            self.download_error.emit(f"Error parsing MPD file: {str(e)}")
            return None

    def get_quality_label(self, height):
        """Convert height to quality label"""
        if height >= 2160:
            return "4K"
        elif height >= 1440:
            return "1440p"
        elif height >= 1080:
            return "1080p"
        elif height >= 720:
            return "720p"
        elif height >= 480:
            return "480p"
        elif height >= 360:
            return "360p"
        elif height >= 240:
            return "240p"
        else:
            return f"{height}p"

    def download_movie(self, mpd_url, movie_data, selected_quality, audio_tracks=None, subtitle_tracks=None, drm_info=None):
        """Download movie with specified parameters - following original OSN.py structure"""
        try:
            # Check for 4K content and warn user
            if selected_quality:
                quality_label = selected_quality.get('quality_label', '')
                resolution = selected_quality.get('resolution', '')
                bandwidth = int(selected_quality.get('bandwidth', 0))

                if ('4K' in quality_label or '3840' in resolution or bandwidth > 15000000):
                    print("⚠️ 4K content detected!")
                    print("💡 If playback shows audio only, try selecting 1080p or lower quality")
                    print("🔧 Ensure your browser and hardware support 4K DRM content")

            # Start download in separate thread to prevent UI freezing
            download_thread = threading.Thread(
                target=self._download_movie_thread,
                args=(mpd_url, movie_data, selected_quality, audio_tracks, subtitle_tracks, drm_info),
                daemon=True
            )
            download_thread.start()

        except Exception as e:
            self.download_error.emit(f"Error starting movie download: {str(e)}")

    def _download_movie_thread(self, mpd_url, movie_data, selected_quality, audio_tracks, subtitle_tracks, drm_info):
        """Download movie in separate thread with comprehensive existing files check"""
        try:
            # Reset completion tracking for new download
            self._reset_completion_tracking()
            # Save DRM keys to file first
            if drm_info and drm_info.get('keys'):
                self.save_keys_to_file(drm_info, movie_data)

            # Prepare movie info like original code
            title = movie_data.get('title', {}).get('en', 'Unknown Movie')
            year = movie_data.get('year', '')

            # Sanitize title like original code
            sanitized_title = f"{title} - {year}".replace(":", " -").replace("?", "").replace("*", "").replace("<", "").replace(">", "").replace("|", "").replace("\\", "").replace("/", "")

            # Get resolution and UUID like original code
            resolution = selected_quality.get('resolution', '720p')
            uuid = selected_quality.get('uuid', selected_quality.get('id', ''))

            # Map resolution like original code
            resolution_map = {
                "256x144": "144p", "426x240": "240p", "426x252": "252p", "512x288": "288p",
                "640x360": "360p", "832x468": "468p", "854x480": "480p", "1024x576": "576p",
                "1280x720": "720p", "1920x1080": "1080p", "2560x1440": "1440p", "3840x2160": "2160p"
            }
            actual_resolution = resolution_map.get(resolution, resolution)

            print(f"🔍 Comprehensive file check for: {sanitized_title} ({actual_resolution})")

            # Check for existing files (downloads and cache)
            existing_check = self.handle_existing_files_check(sanitized_title, actual_resolution, "movie")

            if not existing_check['should_download']:
                print(f"✅ {existing_check['message']}")
                if existing_check['action'] == 'already_completed':
                    self._emit_completion_once(existing_check['file_path'], True)
                elif existing_check['action'] == 'merged_from_cache':
                    self._emit_completion_once(existing_check['file_path'], True)
                return

            print(f"🚀 {existing_check['message']}")

            # Create download directory like original code
            download_path = self.downloads_dir / sanitized_title
            download_path.mkdir(exist_ok=True)

            # Prepare output file like original code
            output_file = download_path / f"{sanitized_title}.{actual_resolution}.OSN+.WEB-DL.H264.AAC.mkv"

            # Download and save poster directly to movie folder (like original OSN.py)
            poster_url = movie_data.get('images', {}).get('longImageWithTitleUrl') or movie_data.get('images', {}).get('wideImageWithTitleUrl')
            if poster_url:
                print(f"📸 Found movie poster URL: {poster_url}")
                self.download_and_save_poster(poster_url, sanitized_title, download_path)
            else:
                print("⚠️ No poster URL found for movie")

            # Start download using original method
            self._download_movie_original(
                mpd_url=mpd_url,
                sanitized_title=sanitized_title,
                actual_resolution=actual_resolution,
                uuid=uuid,
                download_path=download_path,
                output_file=output_file,
                audio_tracks=audio_tracks,
                subtitle_tracks=subtitle_tracks
            )

        except Exception as e:
            self.download_error.emit(f"Error downloading movie: {str(e)}")

    def _build_selection_options(self, audio_tracks, subtitle_tracks):
        """Build audio and subtitle selection options based on user choices"""
        try:
            print(f"🔧 Building selection options...")
            print(f"🎵 Input audio_tracks: {audio_tracks}")
            print(f"📝 Input subtitle_tracks: {subtitle_tracks}")

            # Build audio option based on user selection
            if audio_tracks and len(audio_tracks) > 0:
                # Build more specific audio selection based on exact user choice
                audio_selectors = []
                for track in audio_tracks:
                    if isinstance(track, dict):
                        lang = track.get('language', 'en')
                        codecs = track.get('codecs', '')

                        # Create specific selector based on exact user choice
                        # Use the unique ID for precise track selection

                        # Method 1: Try using track ID if available (most precise)
                        track_id = track.get('id', None)
                        if track_id:
                            selector = f"id={track_id}"
                            print(f"🎯 Using track ID selector: {selector}")
                        else:
                            # Method 2: Try using track index if available
                            track_index = track.get('index', None)
                            if track_index is not None:
                                selector = f"index={track_index}"
                                print(f"🎯 Using track index selector: {selector}")
                            else:
                                # Method 3: Fallback to exact language matching
                                exact_lang = lang  # Keep original: en, en-ddp, en-da, fr, etc.
                                selector = f"lang={exact_lang}"
                                print(f"🎯 Using EXACT language selector: {selector} (original: {lang})")

                        audio_selectors.append(selector)
                        print(f"🎵 Created audio selector: {selector} (from {lang}, {codecs})")
                    else:
                        lang = str(track).replace('-ddp', '')
                        audio_selectors.append(f"lang={lang}")

                if audio_selectors:
                    # Use ONLY selected audio tracks - download exactly what user selected
                    if len(audio_selectors) == 1:
                        audio_option = f'--select-audio "{audio_selectors[0]}"'
                        print(f"🎵 User audio selection (single): {audio_option}")
                    else:
                        # Multiple audio tracks - N_m3u8DL-RE limitation workaround
                        # Use language-based selection for multiple tracks
                        selected_langs = set()
                        for selector in audio_selectors:
                            if selector.startswith('id='):
                                # Extract language from track data
                                track_id = selector.replace('id=', '')
                                for track in audio_tracks:
                                    if isinstance(track, dict) and track.get('id') == track_id:
                                        lang = track.get('language', 'en').split('-')[0]  # Remove -ddp, -da suffixes
                                        selected_langs.add(lang)
                                        break
                            elif selector.startswith('lang='):
                                lang = selector.replace('lang=', '').split('-')[0]
                                selected_langs.add(lang)

                        if selected_langs:
                            # Use :for=all to get all tracks of selected languages
                            combined_langs = "|".join(sorted(selected_langs))
                            audio_option = f'--select-audio "lang={combined_langs}:for=all"'
                            print(f"🎵 User audio selection (multiple languages): {audio_option}")
                            print(f"🎵 Selected languages: {combined_langs}")
                        else:
                            # Fallback to auto-select if we can't determine languages
                            audio_option = '--auto-select'
                            print(f"🎵 Fallback to auto-select for multiple tracks: {audio_option}")
                else:
                    audio_option = '--select-audio "lang=ar|en:for=best"'
                    print(f"🎵 Fallback audio selection: {audio_option}")
            else:
                # No audio selected - use default
                audio_option = '--select-audio "lang=ar|en:for=best"'
                print(f"🎵 Default audio selection: {audio_option}")

            # Build subtitle option based on user selection
            if subtitle_tracks and len(subtitle_tracks) > 0:
                # Extract language codes from user selections
                subtitle_langs = []
                for track in subtitle_tracks:
                    if isinstance(track, dict):
                        lang = track.get('language', 'ar')
                    else:
                        lang = str(track)
                    # Clean up language code
                    lang = lang.lower().strip()
                    if lang and lang not in subtitle_langs:
                        subtitle_langs.append(lang)

                if subtitle_langs:
                    # Download only selected subtitle languages
                    if len(subtitle_langs) == 1:
                        subtitle_option = f'--select-subtitle "lang={subtitle_langs[0]}"'
                        print(f"📝 User subtitle selection (single): {subtitle_option}")
                    else:
                        # Multiple subtitles - use pipe separator in single command
                        combined_langs = "|".join(subtitle_langs)
                        subtitle_option = f'--select-subtitle "lang={combined_langs}"'
                        print(f"📝 User subtitle selection (multiple): {subtitle_option}")
                else:
                    subtitle_option = '--drop-subtitle ".*"'
                    print(f"📝 No valid subtitles selected - dropping all subtitles")
            else:
                # No subtitles selected - drop all subtitles
                subtitle_option = '--drop-subtitle ".*"'
                print(f"📝 No subtitles selected - dropping all subtitles")

            print(f"✅ Final audio option: {audio_option}")
            print(f"✅ Final subtitle option: {subtitle_option}")
            return audio_option, subtitle_option

        except Exception as e:
            print(f"❌ Error building selection options: {str(e)}")
            # Return safe defaults - NO SUBTITLES unless explicitly selected
            return '--select-audio "lang=ar|en:for=best"', '--drop-subtitle ".*"'

    def _download_movie_original(self, mpd_url, sanitized_title, actual_resolution, uuid, download_path, output_file, audio_tracks, subtitle_tracks):
        """Download movie using original OSN.py method with friend's progress monitoring"""
        try:
            print(f"🚀 Starting movie download: {sanitized_title}")
            print(f"🎵 Selected audio tracks: {audio_tracks}")
            print(f"📝 Selected subtitle tracks: {subtitle_tracks}")

            # Store audio tracks data for merge process
            self.current_audio_tracks = audio_tracks if audio_tracks else []

            # Create cache directory like original code
            cache_dir = self.base_dir / "cache"
            cache_dir.mkdir(exist_ok=True)

            # Build audio and subtitle options based on user selections
            audio_option, subtitle_option = self._build_selection_options(audio_tracks, subtitle_tracks)

            # Prepare download name like original code
            download_name = f"{sanitized_title}.{actual_resolution}.OSN+.VIP.WEB-DL.H264.AAC"

            # Build download command like original code
            download_command = (
                f'"{self.n_m3u8dl_path}" "{mpd_url}" -mt '
                f'--select-video "id={uuid}" '
                f'{audio_option} '
                f'{subtitle_option} '
                f'--tmp-dir "{cache_dir}" '
                f'--save-dir "{cache_dir}" '
                f'--save-name "{download_name}" '
                f'--decryption-binary-path="{self.mp4decrypt_path}" '
                f'--key-text-file="{self.keys_file}" '
                f'--log-level "OFF"'
            )

            print(f"🔧 Movie download command: {download_command}")

            # Reset and start dynamic progress system
            self.reset_dynamic_progress()
            self.start_continuous_progress_monitor()
            self.update_dynamic_progress('video', 1, f"🚀 Starting movie download: {sanitized_title}")

            # Use friend's solution for progress monitoring
            process = subprocess.Popen(
                download_command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,  # Redirect stderr to stdout
                text=True,
                universal_newlines=True,
                bufsize=1  # Line buffered
            )

            # Calculate expected tracks for monitoring
            expected_tracks = {
                'audio': len(audio_tracks) if audio_tracks else 0,
                'subtitles': len(subtitle_tracks) if subtitle_tracks else 0
            }

            # Monitor progress using enhanced stdout monitoring
            self._monitor_stdout_progress(process, download_name, cache_dir, output_file, expected_tracks)

        except Exception as e:
            print(f"❌ Error in movie download: {str(e)}")
            self.download_error.emit(f"Movie download error: {str(e)}")

    def _monitor_stdout_progress(self, process, download_name, cache_dir, output_file, expected_tracks):
        """Monitor N_m3u8DL-RE progress using precise stdout reading"""
        try:
            print(f"🔍 Starting precise stdout monitoring for: {download_name}")

            import re
            import time

            # Reset and start dynamic progress system
            self.reset_dynamic_progress()
            self.start_continuous_progress_monitor()

            # Progress tracking variables
            current_progress_video = 0
            current_progress_audio = 0
            current_progress_subtitle = 0
            last_update_time = time.time()
            last_progress_sent = 0

            # Smooth progress variables
            target_progress = 0
            current_smooth_progress = 0
            smooth_progress_thread = None
            smooth_progress_running = False

            def smooth_progress_updater():
                """Update progress smoothly from current to target"""
                nonlocal current_smooth_progress, target_progress, smooth_progress_running

                while smooth_progress_running:
                    try:
                        if current_smooth_progress < target_progress:
                            # Move up by 1% every 200ms for smooth but not too fast animation
                            current_smooth_progress = min(current_smooth_progress + 1, target_progress)

                            # Calculate message based on current progress range
                            if current_smooth_progress <= 70:
                                video_prog = int(current_smooth_progress / 0.7) if current_smooth_progress > 0 else 0
                                message = f"📹 Video: {video_prog}%"
                            elif current_smooth_progress <= 95:
                                audio_prog = int((current_smooth_progress - 70) / 0.25) if current_smooth_progress > 70 else 0
                                message = f"🔊 Audio: {audio_prog}%"
                            else:
                                message = f"🔧 Merging: {current_smooth_progress}%"

                            # Emit smooth progress
                            self.download_progress.emit(int(current_smooth_progress), f"{message}")
                            print(f"🎯 Smooth Progress: {int(current_smooth_progress)}% - {message}")

                        time.sleep(0.2)  # Update every 200ms for smooth animation

                    except Exception as e:
                        print(f"⚠️ Smooth progress error: {e}")
                        break

            # Start smooth progress updater
            smooth_progress_running = True
            smooth_progress_thread = threading.Thread(target=smooth_progress_updater, daemon=True)
            smooth_progress_thread.start()

            # Start file monitoring in background
            file_monitor_thread = threading.Thread(
                target=self._comprehensive_file_monitor,
                args=(cache_dir, download_name, expected_tracks),
                daemon=True
            )
            file_monitor_thread.start()

            # Monitor stdout line by line
            while True:
                line = process.stdout.readline()
                if not line:
                    break

                line_content = line.strip()
                if not line_content:
                    continue

                # Emit status updates for non-progress lines
                if not (line_content.startswith("Vid ") and "---" in line_content) and \
                   not (line_content.startswith("Aud ") and "---" in line_content) and \
                   not (line_content.startswith("Sub ") and "---" in line_content):
                    print(f"📥 N_m3u8DL-RE: {line_content}")

                # Also check for any general progress patterns
                if "%" in line_content and not line_content.startswith(("Vid ", "Aud ", "Sub ")):
                    m = re.search(r"(\d+)%", line_content)
                    if m:
                        prog = int(m.group(1))
                        # Use this as general progress if no specific component progress is available
                        if current_progress_video == 0 and current_progress_audio == 0:
                            new_target = min(prog * 0.8, 80)  # General progress up to 80%
                            if new_target > target_progress:  # Only increase, never decrease
                                target_progress = new_target
                            print(f"📥 General Progress: {prog}% -> Target: {int(target_progress)}%")

                # Check for file progress updates from monitoring thread
                if hasattr(self, 'file_monitor_progress') and self.file_monitor_progress > 0:
                    # Use file monitor progress if no stdout progress is available
                    if current_progress_video == 0 and current_progress_audio == 0:
                        new_target = min(self.file_monitor_progress * 0.8, 80)
                        if int(new_target) > last_progress_sent and new_target > target_progress:
                            target_progress = new_target
                            print(f"📁 File Monitor Progress: {self.file_monitor_progress:.1f}% -> Target: {int(target_progress)}%")
                            last_progress_sent = int(new_target)

                # Parse video progress - updated pattern to match actual output
                if line_content.startswith("Vid ") and ":" in line_content:
                    m = re.search(r"(\d+)%", line_content)
                    if m:
                        prog = int(m.group(1))
                        if prog != current_progress_video:
                            current_progress_video = prog
                            # Update dynamic progress for video
                            self.update_dynamic_progress('video', prog, f"📹 Video: {prog}%")

                            # Calculate overall progress and update target for smooth animation
                            new_target = min(prog * 0.7, 70)  # Video is 70% of total
                            if new_target > target_progress:  # Only increase, never decrease
                                target_progress = new_target
                            print(f"📹 Video Progress: {prog}% -> Target: {int(target_progress)}%")

                # Parse audio progress - updated pattern to match actual output
                if line_content.startswith("Aud ") and ":" in line_content:
                    m = re.search(r"(\d+)%", line_content)
                    if m:
                        prog = int(m.group(1))
                        if prog != current_progress_audio:
                            current_progress_audio = prog
                            # Update dynamic progress for audio
                            self.update_dynamic_progress('audio', prog, f"🔊 Audio: {prog}%")

                            # Calculate overall progress and update target for smooth animation
                            # Audio is 25% of total, starts after video (70%)
                            audio_contribution = min(prog * 0.25, 25)  # Audio is 25% of total
                            new_target = min(current_progress_video * 0.7, 70) + audio_contribution
                            if new_target > target_progress:  # Only increase, never decrease
                                target_progress = new_target
                            print(f"🔊 Audio Progress: {prog}% -> Target: {int(target_progress)}%")

                # Parse subtitle progress - updated pattern to match actual output
                if line_content.startswith("Sub ") and ":" in line_content:
                    m = re.search(r"(\d+)%", line_content)
                    if m:
                        prog = int(m.group(1))
                        if prog != current_progress_subtitle:
                            current_progress_subtitle = prog
                            # Update dynamic progress for subtitle
                            self.update_dynamic_progress('subtitle', prog, f"📝 Subtitle: {prog}%")

                            # Calculate overall progress and update target for smooth animation
                            # Subtitle is 5% of total, starts after video+audio (95%)
                            subtitle_contribution = min(prog * 0.05, 5)  # Subtitle is 5% of total
                            video_contribution = min(current_progress_video * 0.7, 70)
                            audio_contribution = min(current_progress_audio * 0.25, 25)
                            new_target = video_contribution + audio_contribution + subtitle_contribution
                            if new_target > target_progress:  # Only increase, never decrease
                                target_progress = new_target
                            print(f"📝 Subtitle Progress: {prog}% -> Target: {int(target_progress)}%")

                # Check for completion indicators
                if ("任务结束" in line_content or "Task End" in line_content or
                    "All tasks completed" in line_content or "Download completed" in line_content or
                    "All downloads finished" in line_content):
                    print(f"🎯 Download completion detected in line: {line_content}")

                    # Stop dynamic progress immediately
                    self.stop_continuous_progress_monitor()

                    # Stop smooth progress and set target to 95% for merge
                    smooth_progress_running = False
                    target_progress = 95
                    print(f"🔧 Forcing immediate merge...")

                    # Force immediate merge
                    self._force_immediate_merge(cache_dir, output_file)
                    break

            # Wait for process to complete
            process.wait()

            # Stop smooth progress animation
            smooth_progress_running = False
            if smooth_progress_thread and smooth_progress_thread.is_alive():
                smooth_progress_thread.join(timeout=1)

            if process.returncode == 0:
                print("✅ Download completed successfully")

                # Set target to 95% for merge
                target_progress = 95

                # Final verification and merge
                if not hasattr(self, '_merge_completed') or not self._merge_completed:
                    print("🔧 Starting final merge verification...")
                    self._force_immediate_merge(cache_dir, output_file)

                # Ensure we reach 100% completion
                self._ensure_completion_signal(output_file)
            else:
                print(f"❌ Download failed with return code: {process.returncode}")
                self.download_error.emit(f"Download failed: {process.returncode}")

        except Exception as e:
            print(f"❌ Error in stdout monitoring: {str(e)}")
            self.download_error.emit(f"Monitor error: {str(e)}")
            # Stop smooth progress animation on error
            smooth_progress_running = False

    def _ensure_completion_signal(self, output_file):
        """Ensure 100% completion signal is sent"""
        try:
            import time

            # Wait a moment for any final operations
            time.sleep(1)

            # Check if output file exists and has reasonable size
            output_path = Path(output_file)
            if output_path.exists() and output_path.stat().st_size > 1024*1024:  # At least 1MB
                print(f"✅ Final verification: Output file exists and has good size")

                # Force progress to 100% and emit completion
                self.download_progress.emit(100, "✅ Download completed successfully!")
                self._emit_completion_once(output_file, True)

                # Mark merge as completed
                self._merge_completed = True

                print(f"🎉 Download and merge completed: {output_file}")
            else:
                print(f"⚠️ Output file verification failed: {output_file}")
                self.download_error.emit("Output file verification failed")

        except Exception as e:
            print(f"❌ Error in completion verification: {str(e)}")
            # Still try to emit completion if we got this far
            self.download_progress.emit(100, "✅ Download completed!")
            self._emit_completion_once(output_file, True)

    def _monitor_n_m3u8dl_movie_with_friend_solution(self, process, download_name, cache_dir, output_file):
        """Monitor N_m3u8DL-RE progress for movies using enhanced YANGO solution"""
        try:
            print(f"🔍 Starting enhanced movie progress monitoring for: {download_name}")

            import re
            import time
            import threading

            # Enhanced progress tracking system from YANGO
            current_progress = {
                'video': 0,
                'audio': 0,
                'subtitle': 0,
                'overall': 0
            }

            target_progress = 0
            smooth_progress = 0
            download_phase = "initializing"  # initializing, subtitles, video, audio, merging, completed

            # Send initial progress signal
            self.download_progress.emit(1, f"Initializing movie download: {download_name}")
            print(f"📡 Sent initial movie progress signal: 1%")

            # Enhanced smooth progress animation function for movies
            def smooth_progress_animation():
                nonlocal smooth_progress, target_progress, download_phase
                while smooth_progress < 100:
                    if smooth_progress < target_progress:
                        smooth_progress += 1

                        # Enhanced status messages based on phase
                        if download_phase == "subtitles":
                            status_msg = f"Movie subtitles: {smooth_progress}%"
                        elif download_phase == "video":
                            status_msg = f"Movie video: {smooth_progress}%"
                        elif download_phase == "audio":
                            status_msg = f"Movie audio: {smooth_progress}%"
                        elif download_phase == "merging":
                            status_msg = f"Movie merging: {smooth_progress}%"
                        else:
                            status_msg = f"Movie: {smooth_progress}%"

                        self.download_progress.emit(smooth_progress, status_msg)
                        print(f"🎯 Enhanced Movie Progress: {smooth_progress}% - {status_msg}")
                        time.sleep(0.1)  # Update every 100ms for smooth animation
                    else:
                        time.sleep(0.2)  # Wait for new target

            # Start smooth progress thread
            progress_thread = threading.Thread(target=smooth_progress_animation, daemon=True)
            progress_thread.start()

            while True:
                line = process.stdout.readline()
                if not line:
                    break

                line_content = line.strip()

                # Enhanced subtitle progress monitoring
                if line_content.startswith("Sub ") and "%" in line_content:
                    m = re.search(r"(\d+)%", line_content)
                    if m:
                        prog = int(m.group(1))
                        if prog != current_progress['subtitle']:
                            download_phase = "subtitles"
                            # Map subtitle progress to 0-15% range
                            mapped_progress = min(int(prog * 0.15), 15)
                            target_progress = mapped_progress
                            current_progress['subtitle'] = prog
                            print(f"📝 Movie Subtitle Progress: {prog}% -> Mapped: {mapped_progress}%")
                    continue

                # Enhanced video progress monitoring
                if line_content.startswith("Vid ") and "%" in line_content:
                    m = re.search(r"(\d+)%", line_content)
                    if m:
                        prog = int(m.group(1))
                        if prog != current_progress['video']:
                            download_phase = "video"
                            # Map video progress to 15-70% range
                            mapped_progress = 15 + int(prog * 0.55)
                            target_progress = mapped_progress
                            current_progress['video'] = prog
                            print(f"📹 Movie Video Progress: {prog}% -> Mapped: {mapped_progress}%")
                    continue

                # Enhanced audio progress monitoring
                if line_content.startswith("Aud ") and "%" in line_content:
                    m = re.search(r"(\d+)%", line_content)
                    if m:
                        prog = int(m.group(1))
                        if prog != current_progress['audio']:
                            download_phase = "audio"
                            # Map audio progress to 70-85% range
                            mapped_progress = 70 + int(prog * 0.15)
                            target_progress = mapped_progress
                            current_progress['audio'] = prog
                            print(f"🔊 Movie Audio Progress: {prog}% -> Mapped: {mapped_progress}%")
                    continue

                # Enhanced status update for other lines
                if line_content and not any(line_content.startswith(prefix) for prefix in ["Vid ", "Aud ", "Sub "]):
                    self.status_update.emit(f"OSN Movie: {line_content}")

            # Wait for process to complete
            process.wait()

            if process.returncode == 0:
                print("✅ Movie download completed successfully")
                download_phase = "merging"
                target_progress = 85
                time.sleep(0.3)  # Allow progress to reach 85%

                self.download_progress.emit(85, "Movie download completed, starting merge...")

                # Find downloaded files like original code
                video_file = cache_dir / f"{download_name}.mp4"
                audio_files = [f for f in cache_dir.glob("*.m4a")]

                if video_file.exists() and audio_files:
                    print("Downloaded movie files found, proceeding to merge...")

                    # Update progress for merge phase
                    target_progress = 90
                    time.sleep(0.3)

                    # Convert to string paths for merge function
                    audio_paths = [str(f) for f in audio_files]

                    # Merge using movie-specific merge function
                    self._merge_movie_with_mkvmerge(str(video_file), audio_paths, str(output_file), str(cache_dir))

                    # Clean up cache directory like original code
                    try:
                        shutil.rmtree(cache_dir)
                        print(f"Cache directory {cache_dir} has been deleted.")
                    except Exception as e:
                        print(f"An error occurred while deleting the cache directory: {e}")

                    # Set final target to 100% for smooth completion
                    download_phase = "completed"
                    target_progress = 100
                    time.sleep(0.5)  # Give time for smooth animation to reach 100%
                    self._emit_completion_once(output_file, True)
                    self.download_progress.emit(100, "✅ Movie download completed successfully!")
                else:
                    print("Movie download failed or required files not found. Nothing to merge.")
                    self.download_error.emit("Movie download failed or required files not found")
            else:
                print(f"❌ Movie download failed with code: {process.returncode}")
                self.download_error.emit(f"Movie download failed with code: {process.returncode}")

        except Exception as e:
            print(f"❌ Error in enhanced movie progress monitoring: {str(e)}")
            self.download_error.emit(f"Movie progress monitoring error: {str(e)}")

    def _merge_movie_with_mkvmerge(self, video_file, audio_files, output_file, cache_dir):
        """Merge video and audio files into MKV format using mkvmerge for movies - following original OSN.py"""
        try:
            print(f"🔧 Starting movie merge with mkvmerge...")
            self.update_dynamic_progress('merge', 95, "🔧 Merging movie files...")

            # Verify mkvmerge exists
            if not os.path.exists(self.mkvmerge_path):
                raise FileNotFoundError(f"mkvmerge not found: {self.mkvmerge_path}")

            print(f"✅ mkvmerge found at: {self.mkvmerge_path}")

            mkvmerge_command = [str(self.mkvmerge_path), "-o", output_file]

            # Add video file
            mkvmerge_command.append(video_file)

            # Add audio files with enhanced labels based on actual track data
            for i, audio_file in enumerate(audio_files):
                # Get enhanced track info from the selected audio tracks
                track_language = "und"
                track_label = "Unknown Audio"

                # Try to match with the original audio tracks data
                if hasattr(self, 'current_audio_tracks') and i < len(self.current_audio_tracks):
                    audio_track_data = self.current_audio_tracks[i]
                    track_language, track_label = self._get_enhanced_track_info(audio_track_data)
                else:
                    # Fallback to filename-based detection
                    audio_filename = Path(audio_file).name.lower()
                    track_language, track_label = self._detect_track_from_filename(audio_filename)

                    # Special handling for the first audio file if it's still unknown
                    if track_language == "und" and i == 0:
                        print(f"🎯 First audio file with unknown language, assuming English (Main)")
                        track_language = "eng"
                        track_label = "English (Main)"

                # Add the audio file to the mkvmerge command
                mkvmerge_command.extend([
                    "--language", f"0:{track_language}",
                    "--track-name", f"0:{track_label}",
                    audio_file
                ])
                print(f"🔊 Adding {track_label} audio to the movie MKV file")

            # Add subtitle files if they exist like original code
            subtitle_files = {
                "ar": next((f for f in Path(cache_dir).glob("*ar*.srt")), None),
                "en": next((f for f in Path(cache_dir).glob("*en*.srt")), None),
            }

            subtitle_map = {
                "ar": ("ara", "Arabic"),
                "en": ("eng", "English")
            }

            for lang, subtitle_file in subtitle_files.items():
                if subtitle_file:
                    language_code, label = subtitle_map.get(lang, ("und", "Unknown"))
                    subtitle_path = str(subtitle_file)

                    # Add the subtitle file to the mkvmerge command
                    mkvmerge_command.extend([
                        "--language", f"0:{language_code}",
                        "--track-name", f"0:{label}",
                        subtitle_path
                    ])
                    print(f"📝 Adding {label} subtitle to the movie MKV file")

            # Print and run the command like original code
            print("Running mkvmerge command:", " ".join(mkvmerge_command))
            result = subprocess.run(mkvmerge_command, check=True)

            if result.returncode == 0:
                print(f"✅ Movie merge completed successfully: {output_file}")

                # Mark merge as completed and force 100% progress
                self.update_dynamic_progress('merge', 100, "✅ Movie merge completed!")

                # Stop dynamic progress system immediately
                self.stop_continuous_progress_monitor()

                # Send final completion signals with 100% progress
                self.download_progress.emit(100, "✅ Movie download completed successfully!")
                self._emit_completion_once(output_file, True)

                # Clean up cache directory after successful merge
                self._cleanup_cache_directory(cache_dir)

                print(f"🎉 Movie download and merge completed: {output_file}")
            else:
                print(f"❌ Movie merge failed")
                self.stop_continuous_progress_monitor()
                self.download_error.emit("Movie merge failed")

        except Exception as e:
            print(f"❌ Error in movie merge process: {str(e)}")
            self.stop_continuous_progress_monitor()
            self.download_error.emit(f"Movie merge error: {str(e)}")

    def _get_enhanced_track_info(self, audio_track_data):
        """Get enhanced language code and label from audio track data"""
        try:
            language = audio_track_data.get('language', 'unknown').lower()
            codecs = audio_track_data.get('codecs', '').lower()

            # Language code mapping for MKV
            language_code_map = {
                'en': 'eng',
                'en-ddp': 'eng',
                'en-da': 'eng',
                'ar': 'ara',
                'fr': 'fre',
                'tr': 'tur',
                'es': 'spa',
                'de': 'ger',
                'it': 'ita',
                'pt': 'por',
                'ru': 'rus',
                'ja': 'jpn',
                'ko': 'kor',
                'zh': 'chi',
                'hi': 'hin',
                'ur': 'urd'
            }

            # Enhanced label mapping
            if language == 'en-ddp':
                track_language = 'eng'
                track_label = 'English Dolby Digital Plus'
            elif language == 'en-da':
                track_language = 'eng'
                track_label = 'English Audio Description'
            elif language == 'en-atmos':
                track_language = 'eng'
                track_label = 'English Dolby Atmos'
            else:
                # Get language code
                track_language = language_code_map.get(language, 'und')

                # Get language name
                language_name_map = {
                    'en': 'English',
                    'ar': 'Arabic',
                    'fr': 'French',
                    'tr': 'Turkish',
                    'es': 'Spanish',
                    'de': 'German',
                    'it': 'Italian',
                    'pt': 'Portuguese',
                    'ru': 'Russian',
                    'ja': 'Japanese',
                    'ko': 'Korean',
                    'zh': 'Chinese',
                    'hi': 'Hindi',
                    'ur': 'Urdu'
                }

                language_name = language_name_map.get(language, language.upper())

                # Determine audio type from codec
                if 'ec-3' in codecs or 'eac3' in codecs:
                    track_label = f"{language_name} Dolby Digital Plus"
                elif 'ac-3' in codecs:
                    track_label = f"{language_name} Dolby Digital"
                elif 'mp4a' in codecs:
                    track_label = f"{language_name} AAC"
                else:
                    track_label = f"{language_name} Audio"

            return track_language, track_label

        except Exception as e:
            print(f"❌ Error getting enhanced track info: {str(e)}")
            return "und", "Unknown Audio"

    def _detect_track_from_filename(self, audio_filename):
        """Enhanced track info detection from filename as fallback"""
        try:
            print(f"🔍 Analyzing audio filename: {audio_filename}")
            filename_lower = audio_filename.lower()

            # Enhanced audio mapping with more patterns
            audio_patterns = [
                # English variants
                (".en-ddp.m4a", ("eng", "English (Dolby Digital Plus)")),
                (".en-da.m4a", ("eng", "English (Audio Description)")),
                (".en-atmos.m4a", ("eng", "English (Atmos)")),
                (".en-2ch.m4a", ("eng", "English (Stereo)")),
                (".en.m4a", ("eng", "English")),
                ("_en.m4a", ("eng", "English")),
                (".english.m4a", ("eng", "English")),

                # Arabic variants
                (".ar.m4a", ("ara", "Arabic")),
                ("_ar.m4a", ("ara", "Arabic")),
                (".arabic.m4a", ("ara", "Arabic")),

                # French variants
                (".fr.m4a", ("fre", "French")),
                ("_fr.m4a", ("fre", "French")),
                (".french.m4a", ("fre", "French")),

                # Spanish variants
                (".es.m4a", ("spa", "Spanish")),
                ("_es.m4a", ("spa", "Spanish")),
                (".spanish.m4a", ("spa", "Spanish")),

                # German variants
                (".de.m4a", ("ger", "German")),
                ("_de.m4a", ("ger", "German")),
                (".german.m4a", ("ger", "German")),

                # Turkish variants
                (".tr.m4a", ("tur", "Turkish")),
                ("_tr.m4a", ("tur", "Turkish")),
                (".turkish.m4a", ("tur", "Turkish")),

                # Italian variants
                (".it.m4a", ("ita", "Italian")),
                ("_it.m4a", ("ita", "Italian")),
                (".italian.m4a", ("ita", "Italian")),
            ]

            # Check each pattern
            for pattern, (language_code, label) in audio_patterns:
                if pattern in filename_lower:
                    print(f"✅ Detected from pattern '{pattern}': {label} ({language_code})")
                    return language_code, label

            # Special handling for files that might be the main audio track
            # Example: "movie.360p.OSN+.VIP.WEB-DL.H264.AAC.mp4" -> main audio is usually English
            if any(indicator in filename_lower for indicator in [
                'h264.aac', 'web-dl.h264.aac', '.aac.', 'vip.web-dl'
            ]):
                # Check if this is the first audio file (usually main language)
                if not any(lang in filename_lower for lang in ['.ar.', '.fr.', '.es.', '.de.', '.tr.', '.it.']):
                    print(f"🎯 Assuming main audio track without language indicator is English")
                    return "eng", "English (Main)"

            # Try to extract language from filename parts
            # Split by dots and look for language codes
            parts = filename_lower.replace('_', '.').split('.')
            for part in parts:
                if part in ['en', 'eng', 'english']:
                    print(f"✅ Found English from part: {part}")
                    return "eng", "English"
                elif part in ['ar', 'ara', 'arabic']:
                    print(f"✅ Found Arabic from part: {part}")
                    return "ara", "Arabic"
                elif part in ['fr', 'fre', 'french']:
                    print(f"✅ Found French from part: {part}")
                    return "fre", "French"
                elif part in ['es', 'spa', 'spanish']:
                    print(f"✅ Found Spanish from part: {part}")
                    return "spa", "Spanish"
                elif part in ['de', 'ger', 'german']:
                    print(f"✅ Found German from part: {part}")
                    return "ger", "German"
                elif part in ['tr', 'tur', 'turkish']:
                    print(f"✅ Found Turkish from part: {part}")
                    return "tur", "Turkish"
                elif part in ['it', 'ita', 'italian']:
                    print(f"✅ Found Italian from part: {part}")
                    return "ita", "Italian"

            print(f"⚠️ Could not detect language from filename, using default")
            return "und", "Unknown Audio"

        except Exception as e:
            print(f"❌ Error detecting track from filename: {str(e)}")
            return "und", "Unknown Audio"

    def _monitor_complete_download_progress(self, process, download_name, cache_dir, output_file, expected_tracks):
        """Enhanced progress monitoring that waits for all files to complete"""
        try:
            print(f"🔍 Starting complete download monitoring for: {download_name}")
            print(f"📊 Expected tracks: Video=1, Audio={expected_tracks.get('audio', 0)}, Subtitles={expected_tracks.get('subtitles', 0)}")

            import time
            import threading

            # Track download phases
            phases = {
                'initializing': {'range': (0, 10), 'current': 0},
                'downloading': {'range': (10, 85), 'current': 10},
                'finalizing': {'range': (85, 95), 'current': 85},
                'merging': {'range': (95, 100), 'current': 95}
            }

            current_phase = 'initializing'
            last_progress = 0

            # Start file monitoring thread
            file_monitor_thread = threading.Thread(
                target=self._comprehensive_file_monitor,
                args=(cache_dir, download_name, expected_tracks),
                daemon=True
            )
            file_monitor_thread.start()

            # Start with gradual progress updates
            self._emit_gradual_progress(1, f"🚀 Initializing: {download_name}")

            # Monitor process output with smooth progress
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break

                if output:
                    line = output.strip()
                    print(f"📥 N_m3u8DL-RE: {line}")

                    # Check for REAL completion indicators - be very specific
                    if ("任务结束" in line or "Task End" in line or "All tasks completed" in line or
                        "Download completed" in line or "All downloads finished" in line):
                        print(f"🎯 REAL download completion detected in line: {line}")

                        # Stop dynamic progress immediately
                        self.stop_continuous_progress_monitor()

                        # Force immediate merge
                        print(f"🔧 Forcing immediate merge...")
                        self._force_immediate_merge(cache_dir, output_file)
                        break  # Exit the monitoring loop

                    # Don't trigger on individual track completion (Sub en: 100%, Vid: 100%, etc.)

                    # Parse progress and update phase
                    progress = self._parse_enhanced_progress(line, current_phase, phases)
                    if progress > last_progress:
                        phase_name = self._get_phase_name(current_phase)
                        # Use gradual progress emission
                        self._emit_gradual_progress(progress, f"{phase_name}: {download_name}", last_progress)
                        last_progress = progress

                        # Update current phase based on progress - but more gradually
                        if progress >= 15 and current_phase == 'initializing':
                            current_phase = 'downloading'
                            print(f"🔄 Phase transition: initializing → downloading")
                        elif progress >= 80 and current_phase == 'downloading':
                            # Don't transition to finalizing until we verify files
                            print(f"🔄 Download phase nearing completion at {progress}%")

            # Wait for process completion
            process.wait()

            # Final file verification with gradual progress
            if process.returncode == 0:
                # Gradual progress increase during verification
                if last_progress < 85:
                    self._emit_gradual_progress(85, "📥 Download completed, verifying files...", last_progress)
                    import time
                    time.sleep(0.8)  # Allow gradual progress to complete

                if self._verify_all_files_downloaded(cache_dir, expected_tracks):
                    current_phase = 'merging'
                    self._emit_gradual_progress(90, "✅ All files verified, preparing merge...", 85)
                    time.sleep(0.5)
                    self._emit_gradual_progress(95, "🔧 Starting merge process...", 90)

                    # Force merge to start
                    self._force_merge_start(cache_dir, output_file, expected_tracks)
                else:
                    print("⚠️ Some files may be missing, checking manually...")

                    # Manual file check - force merge if basic files exist
                    video_files = list(cache_dir.glob("*.mp4"))
                    audio_files = list(cache_dir.glob("*.m4a"))

                    if video_files and audio_files:
                        print("✅ Found video and audio files, forcing merge...")
                        self._emit_gradual_progress(88, "⚠️ Proceeding with available files...", 85)
                        self._force_merge_start(cache_dir, output_file, expected_tracks)
                    else:
                        print("❌ Critical files missing, cannot proceed")
                        self.download_error.emit("Critical files missing after download")
            else:
                print(f"❌ Download failed with return code: {process.returncode}")
                self.download_error.emit(f"Download failed: {process.returncode}")

        except Exception as e:
            print(f"❌ Error in complete download monitoring: {str(e)}")
            self.download_error.emit(f"Monitor error: {str(e)}")

    def _force_merge_start(self, cache_dir, output_file, expected_tracks):
        """Force merge to start when files are detected"""
        try:
            print(f"🔧 Force starting merge process...")

            # Wait a moment for files to be fully written
            import time
            time.sleep(1)

            # Find all files
            video_files = list(cache_dir.glob("*.mp4"))
            audio_files = list(cache_dir.glob("*.m4a"))

            print(f"🔍 Found {len(video_files)} video files and {len(audio_files)} audio files")

            if video_files:
                video_file = video_files[0]
                print(f"📹 Using video file: {video_file}")

                # Convert to string paths
                audio_paths = [str(f) for f in audio_files]

                # Start merge
                self._merge_movie_with_mkvmerge(str(video_file), audio_paths, str(output_file), str(cache_dir))

                # Clean up cache directory after successful merge
                self._cleanup_cache_directory(cache_dir)

            else:
                print("❌ No video files found for merge")
                self.download_error.emit("No video files found for merge")

        except Exception as e:
            print(f"❌ Error in force merge start: {str(e)}")
            self.download_error.emit(f"Force merge error: {str(e)}")

    def _force_immediate_merge(self, cache_dir, output_file):
        """Force immediate merge when download completion is detected"""
        try:
            print(f"🚀 IMMEDIATE MERGE: Download completed, starting merge NOW!")

            # Stop any ongoing progress monitoring
            self.stop_continuous_progress_monitor()

            # Wait a moment for files to be fully written
            import time
            time.sleep(2)

            # Find all files with detailed logging
            print(f"🔍 Scanning cache directory: {cache_dir}")
            all_files = list(cache_dir.glob("*"))
            print(f"📁 Found {len(all_files)} total files:")
            for file in all_files:
                print(f"   - {file.name} ({file.stat().st_size} bytes)")

            video_files = [f for f in all_files if f.suffix.lower() == '.mp4']
            audio_files = [f for f in all_files if f.suffix.lower() == '.m4a']

            print(f"📹 Video files: {len(video_files)}")
            print(f"🔊 Audio files: {len(audio_files)}")

            if video_files:
                video_file = video_files[0]
                print(f"📹 Using video file: {video_file}")

                # Convert to string paths
                audio_paths = [str(f) for f in audio_files]
                print(f"🔊 Audio paths: {audio_paths}")

                # Update progress to merge phase
                self.download_progress.emit(90, "🔧 Starting immediate merge...")

                # Start merge immediately
                self._merge_movie_with_mkvmerge(str(video_file), audio_paths, str(output_file), str(cache_dir))

                # Clean up cache directory after successful merge
                self._cleanup_cache_directory(cache_dir)

            else:
                print("❌ No video files found for immediate merge")
                self.download_error.emit("No video files found for immediate merge")

        except Exception as e:
            print(f"❌ Error in immediate merge: {str(e)}")
            import traceback
            print(f"❌ Full traceback: {traceback.format_exc()}")
            self.download_error.emit(f"Immediate merge error: {str(e)}")

    def _cleanup_cache_directory(self, cache_dir):
        """Clean up cache directory after successful merge"""
        try:
            import shutil
            from pathlib import Path

            cache_path = Path(cache_dir)
            if cache_path.exists() and cache_path.is_dir():
                shutil.rmtree(cache_path)
                print(f"🧹 Cache directory cleaned up successfully: {cache_dir}")
            else:
                print(f"⚠️ Cache directory not found or not a directory: {cache_dir}")

        except Exception as e:
            print(f"⚠️ Error cleaning up cache directory {cache_dir}: {str(e)}")
            # Don't raise the error - cleanup failure shouldn't stop the download completion

    def check_existing_files(self, title, resolution, content_type="movie"):
        """Check if files already exist in downloads or cache directories"""
        try:
            print(f"🔍 Checking for existing files: {title} ({resolution})")

            # Prepare expected filename patterns
            if content_type == "movie":
                # For movies: "Title - Year.resolution.OSN+.WEB-DL.H264.AAC"
                base_pattern = f"{title}.{resolution}.OSN+.WEB-DL.H264.AAC"
                cache_pattern = f"{title}.{resolution}.OSN+.VIP.WEB-DL.H264.AAC"
            else:
                # For episodes: "Series S01E01.resolution.OSN+.WEB-DL.H264.AAC"
                base_pattern = f"{title}.{resolution}.OSN+.WEB-DL.H264.AAC"
                cache_pattern = f"{title}.{resolution}.OSN+.VIP.WEB-DL.H264.AAC"

            # Check in downloads directory
            downloads_check = self._check_in_downloads(title, base_pattern)
            if downloads_check['exists']:
                return {
                    'status': 'completed',
                    'location': 'downloads',
                    'file_path': downloads_check['file_path'],
                    'message': f"✅ File already downloaded: {downloads_check['file_path']}"
                }

            # Check in cache directory
            cache_check = self._check_in_cache(cache_pattern)
            if cache_check['exists']:
                return {
                    'status': 'ready_to_merge',
                    'location': 'cache',
                    'cache_files': cache_check['files'],
                    'message': f"📁 Files found in cache, ready to merge: {len(cache_check['files'])} files"
                }

            # No existing files found
            return {
                'status': 'not_found',
                'location': None,
                'message': "🆕 No existing files found, proceeding with download"
            }

        except Exception as e:
            print(f"❌ Error checking existing files: {str(e)}")
            return {
                'status': 'error',
                'location': None,
                'message': f"Error checking files: {str(e)}"
            }

    def _check_in_downloads(self, title, pattern):
        """Check if file exists in downloads directory"""
        try:
            # Create expected directory path
            download_dir = self.downloads_dir / title

            if not download_dir.exists():
                return {'exists': False, 'file_path': None}

            # Look for MKV files matching the pattern
            mkv_files = list(download_dir.glob(f"{pattern}*.mkv"))

            if mkv_files:
                # Check file size to ensure it's not corrupted
                for mkv_file in mkv_files:
                    if mkv_file.stat().st_size > 10 * 1024 * 1024:  # At least 10MB
                        print(f"✅ Found completed download: {mkv_file}")
                        return {'exists': True, 'file_path': str(mkv_file)}

            return {'exists': False, 'file_path': None}

        except Exception as e:
            print(f"❌ Error checking downloads: {str(e)}")
            return {'exists': False, 'file_path': None}

    def _check_in_cache(self, pattern):
        """Check if files exist in cache directory"""
        try:
            cache_dir = self.base_dir / "cache"

            if not cache_dir.exists():
                return {'exists': False, 'files': []}

            # Look for video, audio, and subtitle files
            video_files = list(cache_dir.glob(f"{pattern}*.mp4"))
            audio_files = list(cache_dir.glob(f"{pattern}*.m4a"))
            subtitle_files = list(cache_dir.glob(f"{pattern}*.srt"))

            all_files = video_files + audio_files + subtitle_files

            if video_files and audio_files:  # Must have at least video and audio
                print(f"📁 Found cache files: {len(video_files)} video, {len(audio_files)} audio, {len(subtitle_files)} subtitles")
                return {
                    'exists': True,
                    'files': {
                        'video': [str(f) for f in video_files],
                        'audio': [str(f) for f in audio_files],
                        'subtitles': [str(f) for f in subtitle_files]
                    }
                }

            return {'exists': False, 'files': []}

        except Exception as e:
            print(f"❌ Error checking cache: {str(e)}")
            return {'exists': False, 'files': []}

    def merge_existing_cache_files(self, title, cache_files, resolution):
        """Merge existing files from cache without downloading"""
        try:
            print(f"🔧 Starting merge of existing cache files for: {title}")

            # Create output directory and file path
            output_dir = self.downloads_dir / title
            output_dir.mkdir(parents=True, exist_ok=True)

            # Create output filename
            output_filename = f"{title}.{resolution}.OSN+.WEB-DL.H264.AAC.mkv"
            output_file = output_dir / output_filename

            print(f"📁 Output directory: {output_dir}")
            print(f"📄 Output file: {output_file}")

            # Check if output already exists
            if output_file.exists():
                print(f"⚠️ Output file already exists: {output_file}")
                return {
                    'success': True,
                    'message': f"File already exists: {output_file}",
                    'output_path': str(output_file)
                }

            # Get file paths
            video_files = cache_files.get('video', [])
            audio_files = cache_files.get('audio', [])
            subtitle_files = cache_files.get('subtitles', [])

            if not video_files:
                return {
                    'success': False,
                    'message': "No video files found in cache"
                }

            # Start merge process with progress
            self.download_progress.emit(10, "🔧 Preparing merge from cache...")

            # Use the movie merge function
            cache_dir = Path(video_files[0]).parent
            self._merge_movie_with_mkvmerge(video_files[0], audio_files, str(output_file), str(cache_dir))

            return {
                'success': True,
                'message': f"Successfully merged from cache: {output_file}",
                'output_path': str(output_file)
            }

        except Exception as e:
            print(f"❌ Error merging cache files: {str(e)}")
            return {
                'success': False,
                'message': f"Error merging cache files: {str(e)}"
            }

    def handle_existing_files_check(self, title, resolution, content_type="movie"):
        """Handle the complete existing files check and response"""
        try:
            # Check for existing files
            check_result = self.check_existing_files(title, resolution, content_type)

            print(f"📋 File check result: {check_result['message']}")

            if check_result['status'] == 'completed':
                # File already downloaded
                self.download_progress.emit(100, "✅ File already downloaded!")
                self._emit_completion_once(check_result['file_path'], True)
                return {
                    'should_download': False,
                    'action': 'already_completed',
                    'message': check_result['message'],
                    'file_path': check_result['file_path']
                }

            elif check_result['status'] == 'ready_to_merge':
                # Files in cache, ready to merge
                self.download_progress.emit(5, "📁 Files found in cache, starting merge...")

                merge_result = self.merge_existing_cache_files(title, check_result['cache_files'], resolution)

                if merge_result['success']:
                    return {
                        'should_download': False,
                        'action': 'merged_from_cache',
                        'message': merge_result['message'],
                        'file_path': merge_result['output_path']
                    }
                else:
                    # Merge failed, proceed with download
                    print(f"⚠️ Cache merge failed: {merge_result['message']}")
                    return {
                        'should_download': True,
                        'action': 'download_needed',
                        'message': "Cache merge failed, proceeding with download"
                    }

            else:
                # No existing files, proceed with download
                return {
                    'should_download': True,
                    'action': 'download_needed',
                    'message': check_result['message']
                }

        except Exception as e:
            print(f"❌ Error handling existing files check: {str(e)}")
            # On error, proceed with download
            return {
                'should_download': True,
                'action': 'download_needed',
                'message': f"Error checking files: {str(e)}"
            }

    def _comprehensive_file_monitor(self, cache_dir, download_name, expected_tracks):
        """Monitor all expected files during download"""
        try:
            print(f"📁 Starting comprehensive file monitoring in: {cache_dir}")

            expected_files = {
                'video': 1,
                'audio': expected_tracks.get('audio', 0),
                'subtitles': expected_tracks.get('subtitles', 0)
            }

            found_files = {'video': 0, 'audio': 0, 'subtitles': 0}
            last_update = time.time()

            while True:
                try:
                    cache_path = Path(cache_dir)
                    if not cache_path.exists():
                        time.sleep(2)
                        continue

                    # Count different file types
                    all_files = list(cache_path.glob("*"))

                    # Reset counters
                    found_files = {'video': 0, 'audio': 0, 'subtitles': 0}

                    for file_path in all_files:
                        if file_path.is_file() and file_path.stat().st_size > 1024:  # At least 1KB
                            filename = file_path.name.lower()

                            # Classify file type
                            if any(ext in filename for ext in ['.mp4', '.mkv', '.m4v']):
                                found_files['video'] += 1
                            elif any(ext in filename for ext in ['.m4a', '.aac', '.mp3']):
                                found_files['audio'] += 1
                            elif any(ext in filename for ext in ['.srt', '.vtt', '.ass']):
                                found_files['subtitles'] += 1

                    # Calculate completion percentage
                    total_expected = sum(expected_files.values())
                    total_found = sum(found_files.values())

                    if total_expected > 0:
                        completion = min((total_found / total_expected) * 100, 100)
                    else:
                        completion = 0

                    # Store file monitor progress for dynamic system to check
                    self.file_monitor_progress = completion

                    # Update progress if significant change
                    current_time = time.time()
                    if current_time - last_update >= 3:  # Update every 3 seconds
                        print(f"📊 File Progress: {completion:.1f}% - Video:{found_files['video']}/{expected_files['video']}, Audio:{found_files['audio']}/{expected_files['audio']}, Subs:{found_files['subtitles']}/{expected_files['subtitles']}")
                        last_update = current_time

                    # Check if all files are complete
                    if (found_files['video'] >= expected_files['video'] and
                        found_files['audio'] >= expected_files['audio'] and
                        found_files['subtitles'] >= expected_files['subtitles']):
                        print("✅ All expected files detected!")
                        break

                    time.sleep(2)

                except Exception as e:
                    print(f"⚠️ Error in file monitoring loop: {e}")
                    time.sleep(3)

        except Exception as e:
            print(f"❌ Error in comprehensive file monitor: {str(e)}")

    def _parse_enhanced_progress(self, line, current_phase, phases):
        """Parse progress from N_m3u8DL-RE output with phase awareness"""
        try:
            import re

            # Look for percentage patterns
            percentage_matches = re.findall(r'(\d+)%', line)
            if percentage_matches:
                raw_percentage = int(percentage_matches[-1])

                # Map to current phase range with smoother progression
                phase_info = phases[current_phase]
                phase_start, phase_end = phase_info['range']

                # Create smoother mapping - don't jump to end of phase immediately
                if current_phase == 'downloading':
                    # For downloading phase, use more gradual progression
                    mapped_progress = phase_start + int((raw_percentage / 100) * (phase_end - phase_start) * 0.8)
                    mapped_progress = min(mapped_progress, phase_end - 5)  # Leave room for gradual increase
                else:
                    mapped_progress = phase_start + int((raw_percentage / 100) * (phase_end - phase_start))
                    mapped_progress = min(mapped_progress, phase_end)

                return mapped_progress

            # Look for completion indicators - but don't jump immediately
            if any(keyword in line.lower() for keyword in ['task end', '任务结束', 'done', 'completed']):
                # Don't jump to finalizing immediately, let file verification handle it
                if current_phase == 'downloading':
                    return min(phases['downloading']['range'][1] - 3, 82)  # Stay at 82% max
                else:
                    return phases['finalizing']['range'][0]

            return phases[current_phase]['current']

        except Exception as e:
            print(f"⚠️ Error parsing enhanced progress: {e}")
            return 0

    def _get_phase_name(self, phase):
        """Get user-friendly phase name"""
        phase_names = {
            'initializing': '🚀 Initializing',
            'downloading': '📥 Downloading',
            'finalizing': '🔄 Finalizing',
            'merging': '🔧 Merging'
        }
        return phase_names.get(phase, '📥 Processing')

    def _verify_all_files_downloaded(self, cache_dir, expected_tracks):
        """Verify that all expected files have been downloaded"""
        try:
            cache_path = Path(cache_dir)
            if not cache_path.exists():
                return False

            all_files = list(cache_path.glob("*"))

            # Count file types
            video_files = [f for f in all_files if f.is_file() and any(ext in f.name.lower() for ext in ['.mp4', '.mkv', '.m4v']) and f.stat().st_size > 1024*1024]  # At least 1MB
            audio_files = [f for f in all_files if f.is_file() and any(ext in f.name.lower() for ext in ['.m4a', '.aac', '.mp3']) and f.stat().st_size > 1024]  # At least 1KB
            subtitle_files = [f for f in all_files if f.is_file() and any(ext in f.name.lower() for ext in ['.srt', '.vtt', '.ass']) and f.stat().st_size > 0]

            expected_video = 1
            expected_audio = expected_tracks.get('audio', 0)
            expected_subtitles = expected_tracks.get('subtitles', 0)

            print(f"📊 File verification: Video={len(video_files)}/{expected_video}, Audio={len(audio_files)}/{expected_audio}, Subs={len(subtitle_files)}/{expected_subtitles}")

            # Check if we have at least the minimum required files
            has_video = len(video_files) >= expected_video
            has_audio = len(audio_files) >= expected_audio or expected_audio == 0
            has_subtitles = len(subtitle_files) >= expected_subtitles or expected_subtitles == 0

            return has_video and has_audio and has_subtitles

        except Exception as e:
            print(f"❌ Error verifying files: {str(e)}")
            return False

    def _emit_gradual_progress(self, target_progress, message, start_progress=None):
        """Emit progress gradually from start to target"""
        try:
            import time
            import threading

            if start_progress is None:
                start_progress = 0

            # If target is same or lower than start, emit directly
            if target_progress <= start_progress:
                self.download_progress.emit(target_progress, message)
                return

            # Calculate step size for smooth animation
            diff = target_progress - start_progress
            steps = min(diff, 10)  # Maximum 10 steps
            step_size = diff / steps if steps > 0 else 1
            delay = 0.1  # 100ms between updates

            def gradual_update():
                current = start_progress
                for i in range(steps):
                    current = min(start_progress + (i + 1) * step_size, target_progress)
                    self.download_progress.emit(int(current), message)
                    if i < steps - 1:  # Don't delay after last update
                        time.sleep(delay)

                # Ensure we reach the exact target
                self.download_progress.emit(target_progress, message)

            # Run in separate thread to avoid blocking
            thread = threading.Thread(target=gradual_update, daemon=True)
            thread.start()

        except Exception as e:
            print(f"⚠️ Error in gradual progress: {e}")
            # Fallback to direct emission
            self.download_progress.emit(target_progress, message)

    def download_episode(self, mpd_url, series_data, episode_data, selected_quality, audio_tracks=None, subtitle_tracks=None, drm_info=None):
        """Download series episode with specified parameters using threading"""
        try:
            # Save DRM keys to file first
            if drm_info and drm_info.get('keys'):
                self.save_keys_to_file(drm_info, episode_data)

            # Start download in separate thread to prevent UI freezing
            download_thread = threading.Thread(
                target=self._download_episode_thread,
                args=(mpd_url, series_data, episode_data, selected_quality, audio_tracks, subtitle_tracks, drm_info),
                daemon=True
            )
            download_thread.start()

        except Exception as e:
            self.download_error.emit(f"Error starting episode download: {str(e)}")

    def download_series_sequential(self, episodes_data, quality_preferences, audio_preferences, subtitle_preferences):
        """Download series episodes sequentially (one by one) with shared preferences"""
        try:
            print(f"🚀 Starting sequential series download for {len(episodes_data)} episodes")

            # Sort episodes by episode number to ensure correct order
            episodes_sorted = sorted(episodes_data, key=lambda ep: ep.get('episode_number', 0))
            print(f"📋 Episodes sorted by episode number for download:")
            for ep in episodes_sorted:
                print(f"   - Episode {ep.get('episode_number', 'Unknown')}: {ep.get('episode_data', {}).get('title', {}).get('en', 'Unknown Title')}")

            # Store preferences for all episodes
            self.quality_preferences = quality_preferences
            self.audio_preferences = audio_preferences
            self.subtitle_preferences = subtitle_preferences

            # Store episodes to download (sorted)
            self.episodes_to_download = episodes_sorted
            self.current_episode_index = 0
            self.sequential_download_active = True

            # Start downloading first episode
            self._download_next_episode_sequential()

        except Exception as e:
            print(f"❌ Error starting sequential download: {str(e)}")
            self.download_error.emit(f"Sequential download error: {str(e)}")

    def _download_next_episode_sequential(self):
        """Download the next episode in the sequential queue"""
        try:
            if not self.sequential_download_active or self.current_episode_index >= len(self.episodes_to_download):
                print(f"✅ All episodes completed or download stopped")
                self.sequential_download_active = False
                self.all_episodes_completed.emit()
                return

            # Get current episode data
            episode_data = self.episodes_to_download[self.current_episode_index]
            episode_number = episode_data.get('episode_number', self.current_episode_index + 1)

            print(f"📺 Starting episode {episode_number} ({self.current_episode_index + 1}/{len(self.episodes_to_download)})")

            # Start download in separate thread
            download_thread = threading.Thread(
                target=self._download_episode_sequential_thread,
                args=(episode_data,),
                daemon=True
            )
            download_thread.start()

        except Exception as e:
            print(f"❌ Error in sequential episode download: {str(e)}")
            self.download_error.emit(f"Sequential episode error: {str(e)}")

    def _download_episode_sequential_thread(self, episode_data):
        """Download single episode in sequential mode"""
        try:
            episode_number = episode_data.get('episode_number', self.current_episode_index + 1)
            mpd_url = episode_data.get('mpd_url')
            series_data = episode_data.get('series_data', {})
            drm_info = episode_data.get('drm_info', {})
            actual_episode_data = episode_data.get('episode_data', {})

            print(f"🎬 Processing episode {episode_number}")
            print(f"🔗 MPD URL: {mpd_url}")
            print(f"📋 Episode data keys: {list(episode_data.keys())}")
            print(f"📋 Actual episode data keys: {list(actual_episode_data.keys())}")

            # Save DRM keys first
            if drm_info and drm_info.get('keys'):
                self.save_keys_to_file(drm_info, actual_episode_data)

            # Parse MPD to get available qualities for this episode
            mpd_data = self.parse_mpd_file(mpd_url)
            if not mpd_data:
                print(f"❌ Failed to parse MPD for episode {episode_number}")
                self.episode_completed.emit(episode_number, "", False)
                self._move_to_next_episode()
                return

            # Find matching quality for this episode
            available_qualities = mpd_data.get('qualities', [])
            selected_quality = self._find_matching_quality(available_qualities, self.quality_preferences)

            if not selected_quality:
                print(f"❌ No matching quality found for episode {episode_number}")
                self.episode_completed.emit(episode_number, "", False)
                self._move_to_next_episode()
                return

            print(f"✅ Found matching quality for episode {episode_number}: {selected_quality.get('quality_label')} - UUID: {selected_quality.get('uuid')}")

            # Use the same download logic as single episode
            self._download_episode_thread(
                mpd_url=mpd_url,
                series_data=series_data,
                episode_data=episode_data,  # Pass the full episode_data which contains nested episode_data
                selected_quality=selected_quality,  # Use the matched quality with correct UUID
                audio_tracks=self.audio_preferences,
                subtitle_tracks=self.subtitle_preferences,
                drm_info=drm_info
            )

        except Exception as e:
            print(f"❌ Error in sequential episode thread: {str(e)}")
            self.episode_completed.emit(episode_number, "", False)
            self._move_to_next_episode()

    def _find_matching_quality(self, available_qualities, quality_preference):
        """Find matching quality based on preferences"""
        try:
            if not available_qualities or not quality_preference:
                return None

            target_resolution = quality_preference.get('resolution')
            target_quality_label = quality_preference.get('quality_label')

            print(f"🔍 Looking for quality - Resolution: {target_resolution}, Label: {target_quality_label}")
            print(f"🔍 Available qualities: {len(available_qualities)}")

            # Print available qualities for debugging
            for i, quality in enumerate(available_qualities):
                print(f"🔍   Quality {i}: {quality.get('quality_label', 'Unknown')} - {quality.get('resolution')} - UUID: {quality.get('uuid', quality.get('id', 'No UUID'))}")

            # Strategy 1: Try exact resolution match first (most reliable)
            if target_resolution:
                for quality in available_qualities:
                    if quality.get('resolution') == target_resolution:
                        print(f"📺 ✅ Found EXACT resolution match: {quality.get('quality_label', 'Unknown')} ({quality.get('resolution')}) - UUID: {quality.get('uuid', quality.get('id', 'No UUID'))}")
                        return quality

            # Strategy 2: Try quality label match (e.g., "360p")
            if target_quality_label:
                for quality in available_qualities:
                    if quality.get('quality_label') == target_quality_label:
                        print(f"📺 ✅ Found quality label match: {quality.get('quality_label', 'Unknown')} ({quality.get('resolution')}) - UUID: {quality.get('uuid', quality.get('id', 'No UUID'))}")
                        return quality

            # Strategy 3: Try to find by height match (e.g., 640x360 matches 360p)
            if target_quality_label:
                try:
                    # Extract height from quality label (e.g., "360p" -> "360")
                    target_height = target_quality_label.replace('p', '').replace('K', '').replace('4', '2160').replace('1440', '1440').replace('1080', '1080').replace('720', '720').replace('480', '480').replace('360', '360')

                    for quality in available_qualities:
                        quality_resolution = quality.get('resolution', '')
                        if 'x' in str(quality_resolution):
                            width, height = str(quality_resolution).split('x')
                            if height == target_height:
                                print(f"📺 ✅ Found height match: {quality.get('quality_label', 'Unknown')} ({quality.get('resolution')}) - UUID: {quality.get('uuid', quality.get('id', 'No UUID'))}")
                                return quality
                except Exception as e:
                    print(f"⚠️ Error in height matching: {e}")

            # Strategy 4: Use first available quality as fallback
            if available_qualities:
                selected_quality = available_qualities[0]
                print(f"📺 ⚠️ Using FALLBACK quality: {selected_quality.get('quality_label', 'Unknown')} ({selected_quality.get('resolution')}) - UUID: {selected_quality.get('uuid', selected_quality.get('id', 'No UUID'))}")
                return selected_quality

            return None

        except Exception as e:
            print(f"❌ Error finding matching quality: {str(e)}")
            return None

    def _move_to_next_episode(self):
        """Move to next episode in sequential download"""
        try:
            self.current_episode_index += 1

            # Use threading timer instead of QTimer for thread safety
            import threading
            timer = threading.Timer(2.0, self._download_next_episode_sequential)
            timer.start()

        except Exception as e:
            print(f"❌ Error moving to next episode: {str(e)}")

    def _download_episode_thread(self, mpd_url, series_data, episode_data, selected_quality, audio_tracks, subtitle_tracks, drm_info):
        """Download episode in separate thread using original OSN.py method"""
        try:
            # Prepare series info like original code
            series_title = series_data.get('title', {}).get('en', 'Unknown Series')

            # Handle different episode data formats
            if 'episode_data' in episode_data:
                # Data from sequential download (nested format)
                actual_episode_data = episode_data.get('episode_data', {})
                season_number = actual_episode_data.get('seasonNumber', 1)
                episode_number = actual_episode_data.get('episodeNumber', 1)
                episode_title = actual_episode_data.get('title', {}).get('en', 'Unknown Episode')
            else:
                # Direct episode data format
                season_number = episode_data.get('seasonNumber', 1)
                episode_number = episode_data.get('episodeNumber', 1)
                episode_title = episode_data.get('title', {}).get('en', 'Unknown Episode')

            # DEBUG: Print all episode data to see what we're getting
            print(f"🔍 DEBUG Episode Data:")
            print(f"   Series Title: {series_title}")
            print(f"   Season Number: {season_number} (type: {type(season_number)})")
            print(f"   Episode Number: {episode_number} (type: {type(episode_number)})")
            print(f"   Episode Title: {episode_title}")
            print(f"   Full episode_data keys: {list(episode_data.keys())}")
            print(f"   Full episode_data: {episode_data}")

            # Sanitize title like original code - use proper series title
            clean_series_title = series_title.replace('.', ' ').replace(':', ' -').replace('?', '').replace('*', '').replace('<', '').replace('>', '').replace('|', '').replace('\\', '').replace('/', '')
            sanitized_series_title = f"{clean_series_title}"
            sanitized_title = f"{clean_series_title} S{str(season_number).zfill(2)}E{str(episode_number).zfill(2)}"

            # Get resolution and UUID like original code
            resolution = selected_quality.get('resolution', '720p')
            uuid = selected_quality.get('uuid', selected_quality.get('id', ''))

            # Map resolution like original code
            resolution_map = {
                "256x144": "144p", "426x240": "240p", "426x252": "252p", "512x288": "288p",
                "640x360": "360p", "832x468": "468p", "854x480": "480p", "1024x576": "576p",
                "1280x720": "720p", "1920x1080": "1080p", "2560x1440": "1440p", "3840x2160": "2160p"
            }
            actual_resolution = resolution_map.get(resolution, resolution)

            # Create season folder path like original code
            season_folder_path = self.downloads_dir / sanitized_series_title
            season_folder_path.mkdir(parents=True, exist_ok=True)

            # Prepare output file like original code
            download_name = f"{sanitized_title}.{actual_resolution}.OSN+.WEB-DL.H264.AAC"
            output_file = season_folder_path / f"{download_name}.mkv"

            print(f"🔍 Comprehensive file check for episode: {sanitized_title} ({actual_resolution})")

            # Check for existing files (downloads and cache)
            existing_check = self.handle_existing_files_check(sanitized_title, actual_resolution, "episode")

            if not existing_check['should_download']:
                print(f"✅ {existing_check['message']}")

                # Handle different completion scenarios
                if existing_check['action'] == 'already_completed':
                    file_path = existing_check['file_path']
                elif existing_check['action'] == 'merged_from_cache':
                    file_path = existing_check['file_path']
                else:
                    file_path = str(output_file)

                # If in sequential mode, emit episode completed and move to next
                if self.sequential_download_active:
                    self.episode_completed.emit(episode_number, file_path, True)
                    self._move_to_next_episode()
                else:
                    self.download_completed.emit(file_path, True)
                return

            print(f"🚀 {existing_check['message']}")

            # Download poster to cache before starting episode download (like original OSN.py)
            # The poster URL should be passed from the UI when starting download
            poster_url = getattr(self, 'current_poster_url', None)
            if not poster_url and series_data:
                poster_url = series_data.get('images', {}).get('longImageWithTitleUrl') or series_data.get('images', {}).get('wideImageWithTitleUrl')

            if poster_url:
                print(f"📸 Found poster URL: {poster_url}")
                self.download_poster_to_cache(poster_url)
            else:
                print("⚠️ No poster URL found for series")

            # Start download using original method
            self._download_series_episode_original(
                mpd_url=mpd_url,
                series_title=series_title,
                season_number=season_number,
                episode_number=episode_number,
                uuid=uuid,
                actual_resolution=actual_resolution,
                download_name=download_name,
                season_folder_path=season_folder_path,
                output_file=output_file,
                audio_tracks=audio_tracks,
                subtitle_tracks=subtitle_tracks
            )

        except Exception as e:
            print(f"❌ Error in download thread: {str(e)}")

            # If in sequential mode, emit episode failed and move to next
            if self.sequential_download_active:
                episode_number = episode_data.get('episodeNumber', self.current_episode_index + 1)
                self.episode_completed.emit(episode_number, "", False)
                self._move_to_next_episode()
            else:
                self.download_error.emit(f"Error in download thread: {str(e)}")

    def save_keys_to_file(self, drm_info, episode_data):
        """Save DRM keys to OSNPLUS_KEYS.txt file like original code"""
        try:
            keys = drm_info.get('keys', [])

            if not keys:
                return

            # Create KEYS directory
            keys_dir = self.base_dir / "KEYS"
            keys_dir.mkdir(exist_ok=True)

            keys_file = keys_dir / "KEYS.txt"

            # Read existing keys
            existing_keys = set()
            if keys_file.exists():
                with open(keys_file, 'r') as f:
                    existing_keys = set(line.strip() for line in f if line.strip())

            # Filter new keys
            new_keys = [key for key in keys if key not in existing_keys]

            if new_keys:
                episode_title = episode_data.get('title', {}).get('en', 'Unknown Episode')
                print(f"🔑 OBTAINED KEYS for {episode_title}:")
                for key in keys:
                    print(f"🔑 --key {key}")

                # Append new keys to file
                with open(keys_file, 'a', encoding='utf-8') as f:
                    f.write(f'\n\n{episode_title}\n')
                    for key in new_keys:
                        f.write(f'{key}\n')

                print(f"✅ Success Saving KEYS to {keys_file}")
            else:
                print("🔑 KEYS Already Saved!")

        except Exception as e:
            print(f"❌ Error saving keys: {str(e)}")
            import traceback
            print(f"❌ Full traceback: {traceback.format_exc()}")

    def _download_series_episode_enhanced(self, mpd_url, series_title, season_number, episode_number, selected_quality, audio_tracks, subtitle_tracks, cache_dir, output_file, download_name):
        """Download episode using N_m3u8DL-RE with enhanced unified progress monitoring"""
        try:
            print(f"🚀 Starting enhanced download: {download_name}")

            # Get UUID from selected quality
            uuid = selected_quality.get('uuid', selected_quality.get('id', ''))
            resolution = selected_quality.get('resolution', '720p')

            if uuid:
                print(f"✅ Found UUID: {uuid} for resolution: {resolution}")
                print(f"🎯 Using video selection: id={uuid}")
            else:
                print(f"⚠️ No UUID found for resolution: {resolution}")
                print(f"🎯 Auto-selecting best quality available")
                uuid = "best"

            print(f"🎵 Selected audio tracks: {audio_tracks}")
            print(f"📝 Selected subtitle tracks: {subtitle_tracks}")

            # Build audio and subtitle options based on user selections
            audio_option, subtitle_option = self._build_selection_options(audio_tracks, subtitle_tracks)

            # Build final command with user selections
            download_command = (
                f'"{self.n_m3u8dl_path}" "{mpd_url}" -mt '
                f'--select-video "id={uuid}" '
                f'{audio_option} '
                f'{subtitle_option} '
                f'--tmp-dir "{cache_dir}" '
                f'--save-dir "{cache_dir}" '
                f'--save-name "{download_name}" '
                f'--decryption-binary-path="{self.mp4decrypt_path}" '
                f'--key-text-file="{self.keys_file}" '
                f'--log-level "OFF"'
            )

            # Start download with immediate progress update
            self.download_progress.emit(0, f"Starting download: {download_name}")
            print(f"🎯 UI: Starting download for: {download_name}")

            print(f"🔧 OSN.py format command: {download_command}")

            # Immediate progress update to show download started
            self.download_progress.emit(2, f"Initializing: {download_name}")
            print(f"🎯 UI: Download initialized")

            # Use subprocess.Popen to capture output for friend's solution
            import subprocess

            # CRITICAL: Use the EXACT same method as friend's solution
            process = subprocess.Popen(
                download_command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,  # Redirect stderr to stdout for complete output
                text=True,
                universal_newlines=True,
                bufsize=1  # Line buffered for real-time output
            )

            # Simple download without complex progress monitoring
            print(f"🚀 Starting download process...")

            # Wait for process to complete
            process.wait()

            if process.returncode == 0:
                print("✅ Download completed successfully")
                self.download_progress.emit(95, "Merging files...")

                # Find downloaded files
                video_files = list(cache_dir.glob(f"{download_name}*.mp4"))
                audio_files = list(cache_dir.glob(f"{download_name}*.m4a"))

                if video_files:
                    video_file = video_files[0]
                    print(f"📹 Found video file: {video_file}")

                    # Merge files
                    self._merge_episode_with_mkvmerge(video_file, audio_files, output_file, cache_dir)

                    # Clean up
                    try:
                        shutil.rmtree(cache_dir)
                        print(f"🧹 Cache cleaned")
                    except Exception as e:
                        print(f"⚠️ Error cleaning cache: {e}")

                    # Handle sequential vs single download completion
                    if self.sequential_download_active:
                        episode_number = self.current_episode_index + 1
                        print(f"✅ Episode {episode_number} completed successfully in sequential mode")
                        self.episode_completed.emit(episode_number, str(output_file), True)
                        self.download_progress.emit(100, f"✅ Episode {episode_number} completed!")
                        print(f"🔄 Moving to next episode after episode {episode_number}")
                        self._move_to_next_episode()
                    else:
                        self._emit_completion_once(output_file, True)
                        self.download_progress.emit(100, "✅ Completed!")
                else:
                    print("❌ No video files found")
                    self.download_error.emit("No video files found")
            else:
                print(f"❌ Download failed with code: {process.returncode}")
                self.download_error.emit(f"Download failed: {process.returncode}")

        except Exception as e:
            print(f"❌ Error in enhanced download: {str(e)}")
            self.download_error.emit(f"Enhanced download error: {str(e)}")


    def _parse_n_m3u8dl_progress(self, line):
        """Parse progress percentage from N_m3u8DL-RE output"""
        try:
            import re

            # Convert line to lowercase for easier matching
            line_lower = line.lower()

            # Look for specific N_m3u8DL-RE progress patterns
            patterns = [
                # Common N_m3u8DL-RE patterns
                r'(\d+\.?\d*)%',  # Basic percentage
                r'progress[:\s]*(\d+\.?\d*)%',  # Progress: XX%
                r'\[(\d+\.?\d*)%\]',  # [XX%]
                r'(\d+\.?\d*)\s*%\s*complete',  # XX% complete
                r'downloaded[:\s]*(\d+\.?\d*)%',  # Downloaded: XX%
                r'(\d+)/(\d+)\s*segments',  # X/Y segments
                r'segment\s*(\d+)\s*of\s*(\d+)',  # segment X of Y
            ]

            for pattern in patterns:
                match = re.search(pattern, line_lower)
                if match:
                    if len(match.groups()) == 2:  # X/Y format
                        current = float(match.group(1))
                        total = float(match.group(2))
                        if total > 0:
                            percentage = (current / total) * 100
                        else:
                            percentage = 0
                    else:
                        percentage = float(match.group(1))

                    # Map to 0-90% range (leave room for merge)
                    mapped_percentage = min(int(percentage * 0.90), 90)
                    if mapped_percentage > 0:
                        return mapped_percentage

            # Look for specific download phase indicators
            if any(keyword in line_lower for keyword in [
                'downloading', 'download started', 'fetching', 'getting',
                'processing', 'decrypting', 'segment'
            ]):
                return 10  # Show initial progress

            # Look for completion indicators
            if any(keyword in line_lower for keyword in [
                'completed', 'finished', 'done', 'success'
            ]):
                return 80  # Near completion

            return 0

        except Exception as e:
            print(f"⚠️ Error parsing progress from line '{line}': {e}")
            return 0

    def _simple_download_monitor(self, process, download_name, cache_dir, output_file):
        """Simple download monitor without complex progress tracking"""
        try:
            print(f"� Starting simple download monitor for: {download_name}")

            # Just wait for process to complete
            process.wait()

            if process.returncode == 0:
                print("✅ Download completed successfully")
                self.download_progress.emit(95, "Merging files...")

                # Find downloaded files
                video_files = list(cache_dir.glob(f"{download_name}*.mp4"))
                audio_files = list(cache_dir.glob(f"{download_name}*.m4a"))

                if video_files:
                    video_file = video_files[0]
                    print(f"📹 Found video file: {video_file}")

                    # Merge files
                    self._merge_episode_with_mkvmerge(video_file, audio_files, output_file, cache_dir)

                    # Clean up
                    try:
                        shutil.rmtree(cache_dir)
                        print(f"🧹 Cache cleaned")
                    except Exception as e:
                        print(f"⚠️ Error cleaning cache: {e}")

                    # Handle sequential vs single download completion
                    if self.sequential_download_active:
                        episode_number = self.current_episode_index + 1
                        print(f"✅ Episode {episode_number} completed successfully in sequential mode")
                        self.episode_completed.emit(episode_number, str(output_file), True)
                        self.download_progress.emit(100, f"✅ Episode {episode_number} completed!")
                        print(f"🔄 Moving to next episode after episode {episode_number}")
                        self._move_to_next_episode()
                    else:
                        self.download_completed.emit(str(output_file), True)
                        self.download_progress.emit(100, "✅ Completed!")
                else:
                    print("❌ No video files found")
                    self.download_error.emit("No video files found")
            else:
                print(f"❌ Download failed with code: {process.returncode}")
                self.download_error.emit(f"Download failed: {process.returncode}")

        except Exception as e:
            print(f"❌ Error in download monitor: {str(e)}")
            self.download_error.emit(f"Monitor error: {str(e)}")





    def _monitor_real_progress(self, process, download_name, cache_dir, output_file):
        """Monitor download progress using real file size tracking"""
        try:
            print(f"🔍 Starting real progress monitoring for: {download_name}")

            # Start file monitoring in separate thread
            monitor_thread = threading.Thread(
                target=self._file_size_monitor,
                args=(cache_dir, download_name, process),
                daemon=True
            )
            monitor_thread.start()

            # Also monitor N_m3u8DL-RE output for completion
            last_percentage = 0

            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break

                if output:
                    line = output.strip()
                    print(f"📥 N_m3u8DL-RE: {line}")

                    # Detect completion
                    if "任务结束" in line or "Task End" in line or "Done" in line or "All tasks completed" in line:
                        print(f"🎯 Download phase completed")
                        self.download_progress.emit(85, "Download completed, finalizing...")
                        last_percentage = 85

            # Wait for process to complete
            process.wait()

            if process.returncode == 0:
                print("✅ Real progress download completed successfully")
                self.download_progress.emit(95, "Download completed, starting final merge...")

                # Find downloaded files in cache
                video_files = list(cache_dir.glob(f"{download_name}*.mp4"))
                audio_files = list(cache_dir.glob(f"{download_name}*.m4a"))

                if video_files:
                    video_file = video_files[0]
                    print(f"📹 Found video file: {video_file}")

                    # Merge files using mkvmerge
                    self._merge_episode_with_mkvmerge(video_file, audio_files, output_file, cache_dir)

                    # Clean up cache directory
                    try:
                        shutil.rmtree(cache_dir)
                        print(f"🧹 Cache directory {cache_dir} has been deleted.")
                    except Exception as e:
                        print(f"⚠️ Error deleting cache directory: {e}")

                    # Handle sequential vs single download completion
                    if self.sequential_download_active:
                        episode_number = self.current_episode_index + 1
                        print(f"✅ Episode {episode_number} completed successfully in sequential mode")
                        self.episode_completed.emit(episode_number, str(output_file), True)
                        self.download_progress.emit(100, f"✅ Episode {episode_number} completed!")
                        print(f"🔄 Moving to next episode after episode {episode_number}")
                        self._move_to_next_episode()
                    else:
                        self.download_completed.emit(str(output_file), True)
                else:
                    print("❌ No video files found after download")
                    self.download_error.emit("No video files found after download")
            else:
                error_msg = process.stderr.read() if process.stderr else "Download failed"
                print(f"❌ Real progress download failed: {error_msg}")
                self.download_error.emit(f"Download failed: {error_msg}")

        except Exception as e:
            print(f"❌ Error monitoring real progress: {str(e)}")
            self.download_error.emit(f"Monitor error: {str(e)}")

    def _file_size_monitor(self, cache_dir, download_name, process):
        """Monitor file sizes in real-time to calculate actual download progress"""
        try:
            print(f"📊 Starting file size monitoring in: {cache_dir}")

            expected_files = []
            total_expected_size = 0
            last_progress = 0

            # Wait a bit for files to start appearing
            time.sleep(2)

            while process.poll() is None:
                try:
                    # Find all files being downloaded
                    current_files = list(cache_dir.glob(f"{download_name}*"))
                    temp_files = list(cache_dir.glob("*.tmp")) + list(cache_dir.glob("*.part"))
                    all_files = current_files + temp_files

                    if all_files:
                        total_current_size = 0

                        for file_path in all_files:
                            if file_path.exists():
                                try:
                                    file_size = file_path.stat().st_size
                                    total_current_size += file_size

                                    # Estimate total expected size based on current files
                                    if file_path not in expected_files:
                                        expected_files.append(file_path)
                                        # Rough estimation: assume each file will be at least 10MB
                                        total_expected_size += max(file_size * 2, 10 * 1024 * 1024)

                                except (OSError, PermissionError):
                                    continue

                        # Calculate progress based on file sizes
                        if total_expected_size > 0:
                            progress = min(int((total_current_size / total_expected_size) * 70), 70)
                        else:
                            # Fallback: use number of files as progress indicator
                            progress = min(len(all_files) * 10, 50)

                        # Only update if progress increased significantly
                        if progress > last_progress and progress - last_progress >= 2:
                            self.download_progress.emit(progress, f"Downloading: {download_name}")
                            print(f"📊 Real Progress: {progress}% (Size: {total_current_size/1024/1024:.1f}MB/{total_expected_size/1024/1024:.1f}MB, Files: {len(all_files)})")
                            last_progress = progress

                    time.sleep(1)  # Check every second

                except Exception as e:
                    print(f"⚠️ Error in file monitoring loop: {e}")
                    time.sleep(2)

            print(f"📊 File size monitoring completed")

        except Exception as e:
            print(f"❌ Error in file size monitor: {str(e)}")

    def _monitor_simple_progress(self, process, download_name, cache_dir, output_file):
        """Simple but effective progress monitoring with file size tracking"""
        try:
            print(f"🔍 Starting simple progress monitoring for: {download_name}")

            last_percentage = 0
            download_started = False

            # Start file size monitoring in parallel
            file_monitor_thread = threading.Thread(
                target=self._smart_file_monitor,
                args=(cache_dir, download_name, process),
                daemon=True
            )
            file_monitor_thread.start()

            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break

                if output:
                    line = output.strip()
                    print(f"📥 N_m3u8DL-RE: {line}")

                    # Detect when download actually starts
                    if not download_started and ("Selected" in line or "Vid " in line or "Aud " in line):
                        download_started = True
                        self.download_progress.emit(5, f"Starting download: {download_name}")
                        print(f"🚀 Download started!")
                        last_percentage = 5

                    # Look for percentage in the output - but be more conservative
                    if "%" in line and download_started:
                        try:
                            import re
                            # Find all percentages in the line
                            percentages = re.findall(r'(\d+)%', line)
                            if percentages:
                                # Get the highest percentage from the line
                                current_progress = max(int(p) for p in percentages)

                                # Be more conservative - don't let it jump too fast
                                # Only allow gradual increases
                                max_allowed_progress = last_percentage + 5  # Max 5% jump at once
                                display_progress = min(current_progress, max_allowed_progress, 80)

                                # Update only if it's a meaningful increase
                                if display_progress > last_percentage and display_progress - last_percentage >= 1:
                                    self.download_progress.emit(display_progress, f"Downloading: {download_name}")
                                    print(f"📊 N_m3u8DL Progress: {display_progress}% (Raw: {current_progress}%, Limited from {last_percentage}%)")
                                    last_percentage = display_progress

                        except Exception as e:
                            print(f"⚠️ Error parsing progress: {e}")

                    # Detect completion
                    if "任务结束" in line or "Task End" in line or "Done" in line or "All tasks completed" in line:
                        print(f"🎯 Download phase completed")
                        # Ensure we reach at least 75% before completion
                        if last_percentage < 75:
                            self.download_progress.emit(75, "Finalizing download...")
                            time.sleep(0.5)
                        self.download_progress.emit(85, "Download completed, finalizing...")
                        last_percentage = 85

            # Wait for process to complete
            process.wait()

            if process.returncode == 0:
                print("✅ Simple progress download completed successfully")
                self.download_progress.emit(95, "Download completed, starting final merge...")

                # Find downloaded files in cache
                video_files = list(cache_dir.glob(f"{download_name}*.mp4"))
                audio_files = list(cache_dir.glob(f"{download_name}*.m4a"))

                if video_files:
                    video_file = video_files[0]
                    print(f"📹 Found video file: {video_file}")

                    # Merge files using mkvmerge
                    self._merge_episode_with_mkvmerge(video_file, audio_files, output_file, cache_dir)

                    # Clean up cache directory
                    try:
                        shutil.rmtree(cache_dir)
                        print(f"🧹 Cache directory {cache_dir} has been deleted.")
                    except Exception as e:
                        print(f"⚠️ Error deleting cache directory: {e}")

                    self.download_completed.emit(str(output_file), True)
                else:
                    print("❌ No video files found after download")
                    self.download_error.emit("No video files found after download")
            else:
                error_msg = process.stderr.read() if process.stderr else "Download failed"
                print(f"❌ Simple progress download failed: {error_msg}")
                self.download_error.emit(f"Download failed: {error_msg}")

        except Exception as e:
            print(f"❌ Error monitoring simple progress: {str(e)}")
            self.download_error.emit(f"Monitor error: {str(e)}")

    def _smart_file_monitor(self, cache_dir, download_name, process):
        """Smart file monitoring with realistic progress calculation"""
        try:
            print(f"📊 Starting smart file monitoring in: {cache_dir}")

            last_progress = 0
            last_total_size = 0
            estimated_total_size = 0
            stable_size_count = 0

            # Wait for download to start
            time.sleep(3)

            while process.poll() is None:
                try:
                    # Find all files being downloaded
                    all_files = []

                    # Look for various file patterns
                    patterns = [
                        f"{download_name}*",
                        "*.mp4", "*.m4a", "*.vtt", "*.srt",
                        "*.tmp", "*.part", "*.downloading"
                    ]

                    for pattern in patterns:
                        all_files.extend(cache_dir.glob(pattern))

                    if all_files:
                        current_total_size = 0
                        file_count = len(all_files)

                        for file_path in all_files:
                            if file_path.exists():
                                try:
                                    file_size = file_path.stat().st_size
                                    current_total_size += file_size
                                except (OSError, PermissionError):
                                    continue

                        # Convert size to MB
                        size_mb = current_total_size / (1024 * 1024)

                        # Estimate total expected size based on growth pattern
                        if current_total_size > last_total_size:
                            # Files are growing - download is active
                            growth_rate = current_total_size - last_total_size

                            # Estimate total size based on current growth
                            if estimated_total_size == 0 and size_mb > 5:  # Wait for some data
                                # Rough estimate: assume current size is about 30-50% of total
                                estimated_total_size = current_total_size * 2.5
                                print(f"📊 Estimated total size: {estimated_total_size/1024/1024:.1f}MB")

                            stable_size_count = 0
                        else:
                            # Size is stable - might be near completion
                            stable_size_count += 1

                        # Calculate realistic progress
                        if estimated_total_size > 0:
                            # Use realistic calculation based on estimated total
                            real_progress = min(int((current_total_size / estimated_total_size) * 80), 80)
                        else:
                            # Fallback: gradual increment
                            real_progress = min(int(size_mb * 2), 60)  # 2% per MB, max 60%

                        # If size is stable for too long, gradually increase to completion
                        if stable_size_count > 5:  # 10+ seconds of stable size
                            real_progress = min(real_progress + stable_size_count, 80)

                        # Only update if progress increased meaningfully
                        if real_progress > last_progress and real_progress - last_progress >= 1:
                            self.download_progress.emit(real_progress, f"Downloading: {download_name} ({size_mb:.1f}MB, {file_count} files)")
                            print(f"📊 Real Progress: {real_progress}% (Size: {size_mb:.1f}MB, Growth: {(current_total_size-last_total_size)/1024:.1f}KB)")
                            last_progress = real_progress

                        last_total_size = current_total_size

                    time.sleep(1)  # Check every second for more responsiveness

                except Exception as e:
                    print(f"⚠️ Error in smart file monitoring: {e}")
                    time.sleep(2)

            print(f"📊 Smart file monitoring completed")

        except Exception as e:
            print(f"❌ Error in smart file monitor: {str(e)}")

    def _monitor_stderr_progress(self, process, download_name, cache_dir, output_file):
        """Simple and effective progress monitoring"""
        try:
            print(f"🔍 Starting simple progress monitoring for: {download_name}")

            # Start with initial progress
            self.download_progress.emit(5, "🚀 Starting download...")

            # Start file size monitoring in background
            file_monitor_thread = threading.Thread(
                target=self._simple_file_size_monitor,
                args=(cache_dir, download_name, process),
                daemon=True
            )
            file_monitor_thread.start()

            # Wait for process to complete
            process.wait()

            if process.returncode == 0:
                print("✅ Download completed successfully")
                self.download_progress.emit(90, "Download completed, starting final merge...")

                # Find downloaded files in cache
                video_files = list(cache_dir.glob(f"{download_name}*.mp4"))
                audio_files = list(cache_dir.glob(f"{download_name}*.m4a"))

                if video_files:
                    video_file = video_files[0]
                    print(f"📹 Found video file: {video_file}")

                    # Merge files using mkvmerge
                    self._merge_episode_with_mkvmerge(video_file, audio_files, output_file, cache_dir)

                    # Clean up cache directory
                    try:
                        shutil.rmtree(cache_dir)
                        print(f"🧹 Cache directory {cache_dir} has been deleted.")
                    except Exception as e:
                        print(f"⚠️ Error deleting cache directory: {e}")

                    self.download_completed.emit(str(output_file), True)
                else:
                    print("❌ No video files found after download")
                    self.download_error.emit("No video files found after download")
            else:
                print(f"❌ Download failed with return code: {process.returncode}")
                self.download_error.emit(f"Download failed with return code: {process.returncode}")

        except Exception as e:
            print(f"❌ Error monitoring progress: {str(e)}")
            self.download_error.emit(f"Monitor error: {str(e)}")

    def _simple_file_size_monitor(self, cache_dir, download_name, process):
        """Simple and effective file size monitoring with automatic progress"""
        try:
            print(f"🔄 Starting automatic progress monitor for: {download_name}")
            progress_counter = 10
            time_elapsed = 0

            import time
            time.sleep(3)  # Give download time to start

            while process.poll() is None:
                try:
                    # Get total size of all files in cache
                    total_size = 0
                    file_count = 0

                    for file_path in cache_dir.glob("*"):
                        if file_path.is_file():
                            try:
                                total_size += file_path.stat().st_size
                                file_count += 1
                            except (OSError, PermissionError):
                                continue

                    # Convert to MB
                    size_mb = total_size / (1024 * 1024)
                    time_elapsed += 3

                    # Automatic progress based on time and file presence
                    if file_count > 0:  # Files exist - download is working
                        # Gradual automatic progress
                        if time_elapsed < 30:  # First 30 seconds
                            progress_counter = min(10 + (time_elapsed * 1), 40)
                        elif time_elapsed < 60:  # Next 30 seconds
                            progress_counter = min(40 + ((time_elapsed - 30) * 0.8), 65)
                        elif time_elapsed < 120:  # Next minute
                            progress_counter = min(65 + ((time_elapsed - 60) * 0.3), 80)
                        else:  # After 2 minutes
                            progress_counter = min(80 + ((time_elapsed - 120) * 0.1), 85)

                        self.download_progress.emit(int(progress_counter), f"📁 Downloading... {size_mb:.1f}MB ({file_count} files)")
                        print(f"📊 Auto Progress: {int(progress_counter)}% ({size_mb:.1f}MB, {file_count} files, {time_elapsed}s)")
                    else:
                        # No files yet - still initializing
                        if time_elapsed < 15:
                            progress_counter = min(5 + (time_elapsed * 0.3), 10)
                            self.download_progress.emit(int(progress_counter), "🚀 Initializing download...")
                            print(f"📊 Init Progress: {int(progress_counter)}% (Initializing, {time_elapsed}s)")

                    time.sleep(3)  # Check every 3 seconds

                except Exception as e:
                    print(f"⚠️ Error in auto monitor: {e}")
                    time.sleep(3)

            print(f"🔄 Automatic progress monitor completed")

        except Exception as e:
            print(f"❌ Error in auto progress monitor: {str(e)}")

    def _process_output_line(self, line, source, current_progress):
        """Process a single line of output from N_m3u8DL-RE"""
        try:
            # Look for ANY percentage in the line
            if "%" in line:
                import re
                percentages = re.findall(r'(\d+)%', line)
                if percentages:
                    raw_progress = max(int(p) for p in percentages)

                    # Map to our progress range (5-80%)
                    mapped_progress = min(5 + int((raw_progress / 100) * 75), 80)

                    if mapped_progress > current_progress:
                        # Determine phase based on content
                        if "Vid " in line or "Video" in line or "video" in line.lower():
                            phase_msg = f"🎬 Video: {raw_progress}%"
                        elif "Aud " in line or "Audio" in line or "audio" in line.lower():
                            phase_msg = f"🔊 Audio: {raw_progress}%"
                        elif "Sub " in line or "Subtitle" in line or "subtitle" in line.lower():
                            phase_msg = f"📝 Subtitles: {raw_progress}%"
                        else:
                            phase_msg = f"📥 Downloading: {raw_progress}%"

                        self.download_progress.emit(mapped_progress, phase_msg)
                        print(f"📊 Progress Update: {mapped_progress}% (Raw: {raw_progress}%)")
                        return mapped_progress

            # Detect download start
            if any(keyword in line.lower() for keyword in ["starting", "begin", "download"]):
                if current_progress < 10:
                    self.download_progress.emit(10, "🚀 Download initialized...")
                    print(f"🚀 Download started")
                    return 10

            # Detect completion
            if any(keyword in line for keyword in ["任务结束", "Task End", "Done", "All tasks completed", "Success", "Finished"]):
                print(f"🎯 Download phase completed")
                self.download_progress.emit(85, "Download completed, finalizing...")
                return 85

            return current_progress

        except Exception as e:
            print(f"⚠️ Error processing output line: {e}")
            return current_progress

    def _download_components_separately(self, mpd_url, uuid, download_name, cache_dir, output_file, audio_tracks, subtitle_tracks):
        """Download audio, subtitles, and video separately for realistic progress"""
        try:
            print(f"🎯 Starting separate component downloads for: {download_name}")

            # Phase 1: Download Audio (0-25%)
            self.download_progress.emit(5, "🔊 Downloading audio tracks...")
            audio_files = self._download_audio_only(mpd_url, download_name, cache_dir, audio_tracks)
            self.download_progress.emit(25, "✅ Audio download completed")

            # Phase 2: Download Subtitles (25-35%)
            self.download_progress.emit(30, "📝 Downloading subtitles...")
            subtitle_files = self._download_subtitles_only(mpd_url, download_name, cache_dir, subtitle_tracks)
            self.download_progress.emit(35, "✅ Subtitles download completed")

            # Phase 3: Download Video (35-80%)
            self.download_progress.emit(40, "🎬 Downloading video...")
            video_files = self._download_video_only(mpd_url, uuid, download_name, cache_dir)
            self.download_progress.emit(80, "✅ Video download completed")

            # Phase 4: Merge Everything (80-100%)
            self.download_progress.emit(85, "🔧 Merging all components...")
            self._merge_all_components(video_files, audio_files, subtitle_files, output_file, cache_dir)
            self.download_progress.emit(95, "✅ Merge completed")

            # Cleanup
            try:
                shutil.rmtree(cache_dir)
                print(f"🧹 Cache directory {cache_dir} has been deleted.")
            except Exception as e:
                print(f"⚠️ Error deleting cache directory: {e}")

            self.download_completed.emit(str(output_file), True)
            self.download_progress.emit(100, "✅ Download completed successfully!")

        except Exception as e:
            print(f"❌ Error in separate downloads: {str(e)}")
            self.download_error.emit(f"Separate download error: {str(e)}")

    def _download_audio_only(self, mpd_url, download_name, cache_dir, selected_audio_tracks):
        """Download only selected audio tracks"""
        try:
            print(f"🔊 Starting audio-only download")
            print(f"🎵 Selected audio tracks: {selected_audio_tracks}")

            audio_command = [
                str(self.n_m3u8dl_path),
                mpd_url,
                "-mt",
                "--tmp-dir", str(cache_dir),
                "--save-dir", str(cache_dir),
                "--save-name", f"{download_name}_audio",
                "--decryption-binary-path", str(self.mp4decrypt_path),
                "--key-text-file", str(self.keys_file),
                "--binary-merge",
                "--del-after-done",
                "--skip-download"  # Skip actual download, just get info first
            ]

            # Add specific audio selection based on user choice - ONLY ONE TRACK
            if selected_audio_tracks and len(selected_audio_tracks) > 0:
                # Use the first selected audio track
                selected_audio = selected_audio_tracks[0]
                if 'language' in selected_audio:
                    lang = selected_audio['language']
                    # Use for=1 to get exactly ONE track
                    audio_command.extend(["--select-audio", f"lang={lang}:for=1"])
                    print(f"🎵 Using specific audio language: {lang} (SINGLE TRACK)")
                else:
                    audio_command.extend(["--select-audio", "for=1"])
                    print(f"🎵 Using best audio track (SINGLE TRACK)")
            else:
                # Fallback to English - SINGLE TRACK
                audio_command.extend(["--select-audio", "lang=en:for=1"])
                print(f"🎵 Fallback to English audio (SINGLE TRACK)")

            # Drop all subtitles for audio-only download
            audio_command.extend(["--drop-subtitle", ".*"])

            print(f"🔧 Audio command: {' '.join(audio_command)}")

            process = subprocess.Popen(
                audio_command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                bufsize=1
            )

            # Monitor audio download progress (5-25%)
            self._monitor_component_progress(process, "Audio", 5, 25)

            # Find downloaded audio files
            audio_files = list(cache_dir.glob(f"{download_name}_audio*.m4a"))
            if not audio_files:
                audio_files = list(cache_dir.glob("*.m4a"))

            print(f"🔊 Found {len(audio_files)} audio files")
            return audio_files

        except Exception as e:
            print(f"❌ Error downloading audio: {str(e)}")
            return []

    def _download_subtitles_only(self, mpd_url, download_name, cache_dir, selected_subtitle_tracks):
        """Download only selected subtitle tracks"""
        try:
            print(f"📝 Starting subtitles-only download")
            print(f"📝 Selected subtitle tracks: {selected_subtitle_tracks}")

            subtitle_command = [
                str(self.n_m3u8dl_path),
                mpd_url,
                "-mt",
                "--tmp-dir", str(cache_dir),
                "--save-dir", str(cache_dir),
                "--save-name", f"{download_name}_subs",
                "--drop-video", ".*",  # Drop all video
                "--drop-audio", ".*",  # Drop all audio
                "--sub-format", "SRT"
            ]

            # Add specific subtitle selection based on user choice - ONLY ONE TRACK
            if selected_subtitle_tracks and len(selected_subtitle_tracks) > 0:
                # Use the first selected subtitle track
                selected_subtitle = selected_subtitle_tracks[0]
                if 'language' in selected_subtitle:
                    lang = selected_subtitle['language']
                    # Use for=1 to get exactly ONE track
                    subtitle_command.extend(["--select-subtitle", f"lang={lang}:for=1"])
                    print(f"📝 Using specific subtitle language: {lang} (SINGLE TRACK)")
                else:
                    subtitle_command.extend(["--select-subtitle", "for=1"])
                    print(f"📝 Using best subtitle track (SINGLE TRACK)")
            else:
                # Fallback to Arabic or English - SINGLE TRACK
                subtitle_command.extend(["--select-subtitle", "lang=ar|en:for=1"])
                print(f"📝 Fallback to Arabic/English subtitles (SINGLE TRACK)")

            print(f"🔧 Subtitle command: {' '.join(subtitle_command)}")

            process = subprocess.Popen(
                subtitle_command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                bufsize=1
            )

            # Monitor subtitle download progress (25-35%)
            self._monitor_component_progress(process, "Subtitles", 25, 35)

            # Find downloaded subtitle files
            subtitle_files = list(cache_dir.glob(f"{download_name}_subs*.srt"))
            if not subtitle_files:
                subtitle_files = list(cache_dir.glob("*.srt"))

            print(f"📝 Found {len(subtitle_files)} subtitle files")
            return subtitle_files

        except Exception as e:
            print(f"❌ Error downloading subtitles: {str(e)}")
            return []

    def _download_video_only(self, mpd_url, uuid, download_name, cache_dir):
        """Download only video track"""
        try:
            print(f"🎬 Starting video-only download")

            video_command = [
                str(self.n_m3u8dl_path),
                mpd_url,
                "-mt",
                "--tmp-dir", str(cache_dir),
                "--save-dir", str(cache_dir),
                "--save-name", f"{download_name}_video",
                "--decryption-binary-path", str(self.mp4decrypt_path),
                "--key-text-file", str(self.keys_file),
                "--binary-merge",
                "--del-after-done",
                "--select-video", f"id={uuid}",
                "--drop-audio", ".*",  # Drop all audio
                "--drop-subtitle", ".*"  # Drop all subtitles
            ]

            print(f"🔧 Video command: {' '.join(video_command)}")

            process = subprocess.Popen(
                video_command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                bufsize=1
            )

            # Monitor video download progress (35-80%) - longest phase
            self._monitor_component_progress(process, "Video", 35, 80)

            # Find downloaded video files
            video_files = list(cache_dir.glob(f"{download_name}_video*.mp4"))
            if not video_files:
                video_files = list(cache_dir.glob("*.mp4"))

            print(f"🎬 Found {len(video_files)} video files")
            return video_files

        except Exception as e:
            print(f"❌ Error downloading video: {str(e)}")
            return []

    def _monitor_component_progress(self, process, component_name, start_percent, end_percent):
        """Monitor progress for a specific component"""
        try:
            print(f"📊 Monitoring {component_name} progress ({start_percent}%-{end_percent}%)")

            current_progress = start_percent
            progress_range = end_percent - start_percent

            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break

                if output:
                    line = output.strip()
                    print(f"📥 {component_name}: {line}")

                    # Look for percentage in the output
                    if "%" in line:
                        try:
                            import re
                            percentages = re.findall(r'(\d+)%', line)
                            if percentages:
                                raw_progress = max(int(p) for p in percentages)
                                # Map raw progress to our range
                                mapped_progress = start_percent + int((raw_progress / 100) * progress_range)

                                if mapped_progress > current_progress:
                                    self.download_progress.emit(mapped_progress, f"📥 {component_name}: {raw_progress}%")
                                    print(f"📊 {component_name} Progress: {mapped_progress}% (Raw: {raw_progress}%)")
                                    current_progress = mapped_progress

                        except Exception as e:
                            print(f"⚠️ Error parsing {component_name} progress: {e}")

                    # Detect completion
                    if "任务结束" in line or "Task End" in line or "Done" in line or "All tasks completed" in line:
                        print(f"🎯 {component_name} download completed")
                        self.download_progress.emit(end_percent, f"✅ {component_name} completed")
                        break

            # Wait for process to complete
            process.wait()

            if process.returncode == 0:
                print(f"✅ {component_name} download successful")
                self.download_progress.emit(end_percent, f"✅ {component_name} completed")
            else:
                error_msg = process.stderr.read() if process.stderr else f"{component_name} download failed"
                print(f"❌ {component_name} download failed: {error_msg}")

        except Exception as e:
            print(f"❌ Error monitoring {component_name}: {str(e)}")

    def _merge_all_components(self, video_files, audio_files, subtitle_files, output_file, cache_dir):
        """Merge all downloaded components into final file"""
        try:
            print(f"🔧 Starting merge of all components")

            if not video_files:
                raise Exception("No video files found for merging")

            video_file = video_files[0]
            print(f"📹 Using video file: {video_file}")

            # Use existing merge function
            self._merge_episode_with_mkvmerge(video_file, audio_files, output_file, cache_dir)

        except Exception as e:
            print(f"❌ Error merging components: {str(e)}")
            raise

    def _monitor_enhanced_progress(self, process, download_name, cache_dir, output_file):
        """Monitor N_m3u8DL-RE with enhanced dynamic progress tracking"""
        try:
            total_files = 0
            completed_files = 0

            # Track different phases
            phase = "starting"  # starting, downloading, merging, completed

            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break

                if output:
                    line = output.strip()
                    print(f"📥 N_m3u8DL-RE: {line}")

                    # Detect total files to download
                    if "Selected" in line and ("video" in line.lower() or "audio" in line.lower() or "subtitle" in line.lower()):
                        total_files += 1
                        print(f"📊 Total files to download: {total_files}")

                    # Enhanced progress tracking - look for any percentage in download lines
                    if "%" in line:
                        try:
                            import re
                            # Look for percentage patterns in any line
                            percentage_matches = re.findall(r'(\d+)%', line)
                            if percentage_matches:
                                current_progress = int(percentage_matches[-1])
                                print(f"🔍 Found progress: {current_progress}% in line: {line}")

                                # Determine component type and update dynamic progress
                                if line.startswith("Vid "):
                                    self.update_dynamic_progress('video', current_progress)
                                elif line.startswith("Aud "):
                                    self.update_dynamic_progress('audio', current_progress)
                                elif line.startswith("Sub "):
                                    self.update_dynamic_progress('subtitle', current_progress)
                                else:
                                    # General progress - update video as main component
                                    self.update_dynamic_progress('video', current_progress)

                        except Exception as e:
                            print(f"⚠️ Error parsing progress: {e}")

                    # Detect file completion
                    if "写入完成" in line or "Write completed" in line or "Downloaded" in line:
                        completed_files += 1
                        print(f"✅ File completed: {completed_files}/{total_files}")

                    # Detect merge phase
                    if "Muxing" in line or "Merging" in line or "合并" in line or "binary-merge" in line:
                        if phase != "merging":
                            phase = "merging"
                            self.update_dynamic_progress('merge', 25, "🔗 Starting merge phase")
                            print(f"🔗 Starting merge phase")

                    # Detect completion
                    if "任务结束" in line or "Task End" in line or "Done" in line or "All tasks completed" in line:
                        phase = "completed"
                        self.update_dynamic_progress('merge', 75, "🎯 Download phase completed")
                        print(f"🎯 Download phase completed")

            # Wait for process to complete
            process.wait()

            if process.returncode == 0:
                print("✅ Enhanced download completed successfully")
                self.download_progress.emit(95, "Download completed, starting final merge...")

                # Find downloaded files in cache
                video_files = list(cache_dir.glob(f"{download_name}*.mp4"))
                audio_files = list(cache_dir.glob(f"{download_name}*.m4a"))

                if video_files:
                    video_file = video_files[0]
                    print(f"📹 Found video file: {video_file}")

                    # Merge files using mkvmerge
                    self._merge_episode_with_mkvmerge(video_file, audio_files, output_file, cache_dir)

                    # Clean up cache directory
                    self._cleanup_cache_directory(cache_dir)

                    self.download_completed.emit(str(output_file), True)
                else:
                    print("❌ No video files found after download")
                    self.download_error.emit("No video files found after download")
            else:
                error_msg = process.stderr.read() if process.stderr else "Download failed"
                print(f"❌ Enhanced download failed: {error_msg}")
                self.download_error.emit(f"Download failed: {error_msg}")

        except Exception as e:
            print(f"❌ Error monitoring enhanced progress: {str(e)}")
            self.download_error.emit(f"Monitor error: {str(e)}")

    def _download_series_episode_original(self, mpd_url, series_title, season_number, episode_number, uuid, actual_resolution, download_name, season_folder_path, output_file, audio_tracks, subtitle_tracks):
        """Download episode using original OSN.py method with friend's progress monitoring"""
        try:
            print(f"🚀 Starting episode download: {download_name}")
            print(f"🎵 Selected audio tracks: {audio_tracks}")
            print(f"📝 Selected subtitle tracks: {subtitle_tracks}")

            # Store audio tracks data for merge process
            self.current_audio_tracks = audio_tracks if audio_tracks else []

            # Create cache directory like original code
            cache_dir = self.base_dir / "cache"
            cache_dir.mkdir(exist_ok=True)

            # Build audio and subtitle options based on user selections
            audio_option, subtitle_option = self._build_selection_options(audio_tracks, subtitle_tracks)

            # Build download command like original code
            download_command = (
                f'"{self.n_m3u8dl_path}" "{mpd_url}" -mt '
                f'--select-video "id={uuid}" '
                f'{audio_option} '
                f'{subtitle_option} '
                f'--tmp-dir "{cache_dir}" '
                f'--save-dir "{cache_dir}" '
                f'--save-name "{download_name}" '
                f'--decryption-binary-path="{self.mp4decrypt_path}" '
                f'--key-text-file="{self.keys_file}" '
                f'--log-level "OFF"'
            )

            print(f"🔧 Episode download command: {download_command}")

            # Start download progress
            self.download_progress.emit(0, f"Starting episode download: {download_name}")

            # Use friend's solution for progress monitoring
            process = subprocess.Popen(
                download_command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,  # Redirect stderr to stdout
                text=True,
                universal_newlines=True,
                bufsize=1  # Line buffered
            )

            # Monitor progress using friend's method
            self._monitor_n_m3u8dl_with_friend_solution(process, download_name, cache_dir, output_file, season_folder_path)

        except Exception as e:
            print(f"❌ Error in episode download: {str(e)}")
            self.download_error.emit(f"Episode download error: {str(e)}")

    def _monitor_n_m3u8dl_with_friend_solution(self, process, download_name, cache_dir, output_file, season_folder_path=None):
        """Monitor N_m3u8DL-RE progress using enhanced dynamic solution"""
        try:
            print(f"🔍 Starting enhanced dynamic progress monitoring for: {download_name}")

            import re
            import time
            import threading

            # Reset and start dynamic progress system
            self.reset_dynamic_progress()
            self.start_continuous_progress_monitor()
            self.update_dynamic_progress('video', 1, f"🚀 Initializing download: {download_name}")

            # Enhanced progress tracking system
            current_progress = {
                'video': 0,
                'audio': 0,
                'subtitle': 0,
                'overall': 0
            }

            while True:
                line = process.stdout.readline()
                if not line:
                    break

                line_content = line.strip()

                # Enhanced subtitle progress monitoring
                if line_content.startswith("Sub ") and "%" in line_content:
                    m = re.search(r"(\d+)%", line_content)
                    if m:
                        prog = int(m.group(1))
                        if prog != current_progress['subtitle']:
                            current_progress['subtitle'] = prog
                            self.update_dynamic_progress('subtitle', prog)
                            print(f"📝 Subtitle Progress: {prog}%")
                    continue

                # Enhanced video progress monitoring
                if line_content.startswith("Vid ") and "%" in line_content:
                    m = re.search(r"(\d+)%", line_content)
                    if m:
                        prog = int(m.group(1))
                        if prog != current_progress['video']:
                            current_progress['video'] = prog
                            self.update_dynamic_progress('video', prog)
                            print(f"📹 Video Progress: {prog}%")
                    continue

                # Enhanced audio progress monitoring
                if line_content.startswith("Aud ") and "%" in line_content:
                    m = re.search(r"(\d+)%", line_content)
                    if m:
                        prog = int(m.group(1))
                        if prog != current_progress['audio']:
                            current_progress['audio'] = prog
                            self.update_dynamic_progress('audio', prog)
                            print(f"🔊 Audio Progress: {prog}%")
                    continue

                # Enhanced status update for other lines
                if line_content and not any(line_content.startswith(prefix) for prefix in ["Vid ", "Aud ", "Sub "]):
                    self.status_update.emit(f"OSN: {line_content}")

            # Wait for process to complete
            process.wait()

            if process.returncode == 0:
                print("✅ Episode download completed successfully")
                self.update_dynamic_progress('merge', 25, "🔧 Download completed, starting merge...")

                # Find downloaded files like original code
                print(f"🔍 Looking for downloaded files in: {cache_dir}")

                # List all files in cache directory for debugging
                try:
                    all_files = list(cache_dir.glob("*"))
                    print(f"📁 All files in cache directory:")
                    for file in all_files:
                        print(f"   - {file.name} ({file.stat().st_size} bytes)")
                except Exception as e:
                    print(f"⚠️ Error listing cache files: {e}")

                video_file = cache_dir / f"{download_name}.mp4"
                audio_files = [f for f in cache_dir.glob("*.m4a")]

                print(f"🔍 Looking for video file: {video_file}")
                print(f"🔍 Video file exists: {video_file.exists()}")
                print(f"🔍 Found {len(audio_files)} audio files: {[f.name for f in audio_files]}")

                if video_file.exists() and audio_files:
                    print("✅ Downloaded files found, proceeding to merge...")

                    # Update progress for merge phase
                    self.update_dynamic_progress('merge', 50, "🔧 Preparing merge...")

                    # Convert to string paths for merge function
                    audio_paths = [str(f) for f in audio_files]
                    print(f"🔊 Audio paths for merge: {audio_paths}")

                    # Merge using episode-specific merge function
                    self._merge_episode_with_mkvmerge(str(video_file), audio_paths, str(output_file), str(cache_dir))
                elif video_file.exists() and not audio_files:
                    print("⚠️ Video file found but no audio files - proceeding with video only")

                    # Update progress for merge phase
                    self.update_dynamic_progress('merge', 50, "🔧 Preparing merge (video only)...")

                    # Merge with empty audio list
                    self._merge_episode_with_mkvmerge(str(video_file), [], str(output_file), str(cache_dir))
                elif not video_file.exists() and audio_files:
                    print("❌ Audio files found but no video file")
                    self.stop_continuous_progress_monitor()
                    self.download_error.emit("Audio files found but no video file")

                    # Move poster to final folder like original code
                    self.move_poster_to_final_folder(season_folder_path)

                    # Clean up cache directory like original code
                    self._cleanup_cache_directory(cache_dir)

                    # Handle sequential vs single download completion
                    if self.sequential_download_active:
                        # Extract episode number from download_name or use current index
                        episode_number = self.current_episode_index + 1
                        print(f"✅ Episode {episode_number} completed successfully in sequential mode")
                        self.episode_completed.emit(episode_number, str(output_file), True)

                        # Move to next episode after a short delay
                        print(f"🔄 Moving to next episode after episode {episode_number}")
                        self._move_to_next_episode()
                    else:
                        # Single episode download
                        self.download_completed.emit(str(output_file), True)
                else:
                    print("Download failed or required files not found. Nothing to merge.")
                    self.stop_continuous_progress_monitor()

                    # Handle sequential vs single download failure
                    if self.sequential_download_active:
                        episode_number = self.current_episode_index + 1
                        print(f"❌ Episode {episode_number} failed in sequential mode")
                        self.episode_completed.emit(episode_number, "", False)
                        self._move_to_next_episode()
                    else:
                        self.download_error.emit("Episode download failed or required files not found")
            else:
                print(f"❌ Episode download failed with code: {process.returncode}")
                self.stop_continuous_progress_monitor()

                # Handle sequential vs single download failure
                if self.sequential_download_active:
                    episode_number = self.current_episode_index + 1
                    print(f"❌ Episode {episode_number} failed with return code {process.returncode}")
                    self.episode_completed.emit(episode_number, "", False)
                    self._move_to_next_episode()
                else:
                    self.download_error.emit(f"Episode download failed with code: {process.returncode}")

        except Exception as e:
            print(f"❌ Error in enhanced progress monitoring: {str(e)}")
            self.stop_continuous_progress_monitor()

            # Handle sequential vs single download error
            if self.sequential_download_active:
                episode_number = self.current_episode_index + 1
                print(f"❌ Episode {episode_number} error in sequential mode: {str(e)}")
                self.episode_completed.emit(episode_number, "", False)
                self._move_to_next_episode()
            else:
                self.download_error.emit(f"Progress monitoring error: {str(e)}")

    def _merge_episode_with_mkvmerge(self, video_file, audio_files, output_file, cache_dir):
        """Merge video and audio files using mkvmerge for episodes - following original OSN.py"""
        try:
            print(f"🔧 Starting episode merge with mkvmerge...")
            print(f"📹 Video file: {video_file}")
            print(f"🔊 Audio files: {audio_files}")
            print(f"📁 Output file: {output_file}")

            # Update merge progress using dynamic system
            self.update_dynamic_progress('merge', 75, "🔧 Merging episode files...")

            # Verify mkvmerge exists
            if not os.path.exists(self.mkvmerge_path):
                raise FileNotFoundError(f"mkvmerge not found: {self.mkvmerge_path}")

            print(f"✅ mkvmerge found at: {self.mkvmerge_path}")

            # Verify files exist before merging
            if not os.path.exists(video_file):
                raise FileNotFoundError(f"Video file not found: {video_file}")

            if not audio_files:
                print("⚠️ No audio files provided for merging")

            # Verify audio files exist
            valid_audio_files = []
            for audio_file in audio_files:
                if os.path.exists(audio_file):
                    valid_audio_files.append(audio_file)
                    print(f"✅ Found audio file: {audio_file}")
                else:
                    print(f"⚠️ Audio file not found: {audio_file}")

            if not valid_audio_files:
                print("⚠️ No valid audio files found, proceeding with video only")

            mkvmerge_command = [str(self.mkvmerge_path), "-o", output_file]

            # Add video file
            mkvmerge_command.append(video_file)
            print(f"📹 Adding video file: {video_file}")

            # Add audio files with enhanced labels based on actual track data
            for i, audio_file in enumerate(valid_audio_files):
                # Get enhanced track info from the selected audio tracks
                track_language = "und"
                track_label = "Unknown Audio"

                # Try to match with the original audio tracks data
                if hasattr(self, 'current_audio_tracks') and i < len(self.current_audio_tracks):
                    audio_track_data = self.current_audio_tracks[i]
                    track_language, track_label = self._get_enhanced_track_info(audio_track_data)
                else:
                    # Fallback to filename-based detection
                    audio_filename = Path(audio_file).name.lower()
                    track_language, track_label = self._detect_track_from_filename(audio_filename)

                    # Special handling for the first audio file if it's still unknown
                    if track_language == "und" and i == 0:
                        print(f"🎯 First audio file with unknown language, assuming English (Main)")
                        track_language = "eng"
                        track_label = "English (Main)"

                mkvmerge_command.extend([
                    "--language", f"0:{track_language}",
                    "--track-name", f"0:{track_label}",
                    audio_file
                ])
                print(f"🔊 Adding {track_label} audio to the episode MKV file")

            # Add subtitle files if they exist like original code
            subtitle_files = {
                "ar": next((f for f in Path(cache_dir).glob("*ar*.srt")), None),
                "en": next((f for f in Path(cache_dir).glob("*en*.srt")), None),
            }

            subtitle_map = {
                "ar": ("ara", "Arabic"),
                "en": ("eng", "English")
            }

            for lang, subtitle_file in subtitle_files.items():
                if subtitle_file:
                    language_code, label = subtitle_map.get(lang, ("und", "Unknown"))
                    subtitle_path = str(subtitle_file)

                    # Add the subtitle file to the mkvmerge command
                    mkvmerge_command.extend([
                        "--language", f"0:{language_code}",
                        "--track-name", f"0:{label}",
                        subtitle_path
                    ])
                    print(f"📝 Adding {label} subtitle to the episode MKV file")

            # Print and run the command like original code
            print("🔧 Running mkvmerge command:")
            print(f"   {' '.join(mkvmerge_command)}")

            # Update merge progress using dynamic system
            self.update_dynamic_progress('merge', 90, "🔧 Running mkvmerge...")

            # Run mkvmerge with better error handling
            try:
                result = subprocess.run(
                    mkvmerge_command,
                    check=True,
                    capture_output=True,
                    text=True,
                    timeout=300  # 5 minute timeout
                )

                print(f"✅ mkvmerge completed successfully")
                print(f"📁 Output file should be: {output_file}")

                # Verify output file was created
                if os.path.exists(output_file):
                    file_size = os.path.getsize(output_file)
                    print(f"✅ Episode merge completed successfully: {output_file} ({file_size} bytes)")

                    # Mark merge as completed and force 100% progress
                    self.update_dynamic_progress('merge', 100, "✅ Episode merge completed!")
                    self.stop_continuous_progress_monitor()

                    # Force final 100% progress signal
                    self.download_progress.emit(100, "✅ Episode completed successfully!")

                    import time
                    time.sleep(0.5)  # Allow progress to complete

                    print(f"🎉 Episode download and merge completed: {output_file}")

                    # Clean up cache directory after successful merge
                    self._cleanup_cache_directory(cache_dir)

                    # Emit completion signal
                    self._emit_completion_once(output_file, True)
                    print(f"🎯 Final completion signal sent for: {output_file}")
                else:
                    print(f"❌ Output file was not created: {output_file}")
                    self.stop_continuous_progress_monitor()
                    self.download_error.emit("Merge completed but output file not found")

            except subprocess.TimeoutExpired:
                print(f"❌ mkvmerge timed out after 5 minutes")
                self.stop_continuous_progress_monitor()
                self.download_error.emit("Merge process timed out")

            except subprocess.CalledProcessError as e:
                print(f"❌ mkvmerge failed with return code: {e.returncode}")
                print(f"❌ mkvmerge stderr: {e.stderr}")
                print(f"❌ mkvmerge stdout: {e.stdout}")
                self.stop_continuous_progress_monitor()
                self.download_error.emit(f"Merge failed: {e.stderr}")

        except FileNotFoundError as e:
            print(f"❌ File not found error: {str(e)}")
            self.stop_continuous_progress_monitor()
            self.download_error.emit(f"File not found: {str(e)}")

        except Exception as e:
            print(f"❌ Error in episode merge process: {str(e)}")
            import traceback
            print(f"❌ Full traceback: {traceback.format_exc()}")
            self.stop_continuous_progress_monitor()
            self.download_error.emit(f"Episode merge error: {str(e)}")

    def _emit_gradual_progress(self, target_progress, message, start_progress=None):
        """Emit gradual progress updates for smooth animation"""
        try:
            import time
            import threading

            if start_progress is None:
                start_progress = target_progress - 5

            def gradual_update():
                current = start_progress
                while current <= target_progress:
                    self.download_progress.emit(current, message)
                    print(f"📊 Gradual Progress: {current}% - {message}")
                    current += 1
                    time.sleep(0.1)  # 100ms delay for smooth animation

            # Run in separate thread to avoid blocking
            thread = threading.Thread(target=gradual_update, daemon=True)
            thread.start()

        except Exception as e:
            print(f"⚠️ Error in gradual progress: {e}")
            # Fallback to direct emission
            self.download_progress.emit(target_progress, message)

    def _monitor_n_m3u8dl_progress(self, process, download_name, cache_dir, output_file):
        """Monitor N_m3u8DL-RE download progress using dynamic system"""
        try:
            import re
            current_progress_video = 0
            current_progress_audio = 0
            current_progress_subtitle = 0

            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break

                if output:
                    line = output.strip()
                    print(f"📥 N_m3u8DL-RE: {line}")  # Debug output

                    # Parse progress from N_m3u8DL-RE output using dynamic system
                    if "%" in line:
                        try:
                            # Video progress
                            if line.startswith("Vid "):
                                percentage_match = re.search(r'(\d+)%', line)
                                if percentage_match:
                                    percentage = int(percentage_match.group(1))
                                    if percentage != current_progress_video:
                                        current_progress_video = percentage
                                        self.update_dynamic_progress('video', percentage)

                            # Audio progress
                            elif line.startswith("Aud "):
                                percentage_match = re.search(r'(\d+)%', line)
                                if percentage_match:
                                    percentage = int(percentage_match.group(1))
                                    if percentage != current_progress_audio:
                                        current_progress_audio = percentage
                                        self.update_dynamic_progress('audio', percentage)

                            # Subtitle progress
                            elif line.startswith("Sub "):
                                percentage_match = re.search(r'(\d+)%', line)
                                if percentage_match:
                                    percentage = int(percentage_match.group(1))
                                    if percentage != current_progress_subtitle:
                                        current_progress_subtitle = percentage
                                        self.update_dynamic_progress('subtitle', percentage)

                            # General progress (fallback)
                            else:
                                percentage_match = re.search(r'(\d+)%', line)
                                if percentage_match:
                                    percentage = int(percentage_match.group(1))
                                    # If no specific component, update video as main component
                                    if percentage > current_progress_video:
                                        current_progress_video = percentage
                                        self.update_dynamic_progress('video', percentage)

                        except Exception as e:
                            print(f"⚠️ Error parsing progress: {e}")

                    # Check for completion indicators
                    if "任务结束" in line or "Task End" in line or "Done" in line:
                        self.update_dynamic_progress('merge', 25, "🔧 Download completed, starting merge...")

            # Wait for process to complete
            process.wait()

            if process.returncode == 0:
                print("✅ Download completed successfully")
                self.update_dynamic_progress('merge', 25, "🔧 Download completed, starting merge...")

                # Find downloaded files in cache
                print(f"🔍 Looking for downloaded files in: {cache_dir}")

                # List all files in cache directory for debugging
                try:
                    all_files = list(cache_dir.glob("*"))
                    print(f"📁 All files in cache directory:")
                    for file in all_files:
                        print(f"   - {file.name} ({file.stat().st_size} bytes)")
                except Exception as e:
                    print(f"⚠️ Error listing cache files: {e}")

                video_files = list(cache_dir.glob(f"{download_name}*.mp4"))
                audio_files = list(cache_dir.glob(f"{download_name}*.m4a"))

                print(f"🔍 Found {len(video_files)} video files: {[f.name for f in video_files]}")
                print(f"🔍 Found {len(audio_files)} audio files: {[f.name for f in audio_files]}")

                if video_files:
                    video_file = video_files[0]
                    print(f"📹 Using video file: {video_file}")

                    # Update merge progress
                    self.update_dynamic_progress('merge', 50, "🔧 Preparing merge...")

                    # Convert to string paths for merge function
                    audio_paths = [str(f) for f in audio_files]
                    print(f"🔊 Audio paths for merge: {audio_paths}")

                    # Merge files using mkvmerge like original code
                    self._merge_episode_with_mkvmerge(str(video_file), audio_paths, str(output_file), str(cache_dir))

                    # Clean up cache directory
                    try:
                        import shutil
                        shutil.rmtree(cache_dir)
                        print(f"🧹 Cache directory {cache_dir} has been deleted.")
                    except Exception as e:
                        print(f"⚠️ Error deleting cache directory: {e}")

                    # Wait for merge completion signal instead of emitting here
                    print("✅ Download and merge process completed")
                else:
                    print("❌ No video files found after download")
                    self.download_error.emit("No video files found after download")
            else:
                error_msg = process.stderr.read() if process.stderr else "Download failed"
                print(f"❌ Download failed: {error_msg}")
                self.download_error.emit(f"Download failed: {error_msg}")

        except Exception as e:
            print(f"❌ Error monitoring N_m3u8DL-RE progress: {str(e)}")
            self.download_error.emit(f"Monitor error: {str(e)}")

    def save_keys_to_file_duplicate(self, drm_info, content_data):
        """DUPLICATE FUNCTION - RENAMED TO AVOID CONFLICT"""
        try:
            if not drm_info or not drm_info.get('keys'):
                return

            # Create KEYS directory if it doesn't exist
            keys_dir = self.base_dir / "KEYS"
            keys_dir.mkdir(exist_ok=True)

            # Prepare content info
            title = content_data.get('title', {}).get('en', 'Unknown')
            content_type = 'Movie' if 'year' in content_data else 'Series'

            # Format key entry like original code
            key_entry = f"\n# {content_type}: {title}\n"
            for key in drm_info['keys']:
                key_entry += f"{key}\n"

            # Append to KEYS.txt file like original code
            keys_file_path = keys_dir / "KEYS.txt"
            with open(keys_file_path, 'a', encoding='utf-8') as f:
                f.write(key_entry)

            print(f"✅ DRM keys saved to: {keys_file_path}")

        except Exception as e:
            print(f"❌ Error saving DRM keys: {str(e)}")

    def _start_n_m3u8dl_download(self, mpd_url, output_dir, filename, selected_quality, audio_tracks, subtitle_tracks, drm_info, content_type):
        """Start N_m3u8DL-RE download process"""
        try:
            # Prepare command
            cmd = [str(self.n_m3u8dl_path), mpd_url]
            cmd.extend(["--save-dir", str(output_dir)])
            cmd.extend(["--save-name", filename])

            # Add quality selection
            if selected_quality and selected_quality.get('resolution'):
                # Use resolution for video selection
                resolution = selected_quality.get('resolution', '720p')
                cmd.extend(["--select-video", f"res={resolution}"])

            # Add audio selection
            if audio_tracks:
                for track in audio_tracks:
                    cmd.extend(["--select-audio", f"lang:{track}"])

            # Add subtitle selection
            if subtitle_tracks:
                for track in subtitle_tracks:
                    cmd.extend(["--select-subtitle", f"lang:{track}"])

            # Add DRM keys if available
            if drm_info and drm_info.get('formatted_key'):
                print(f"🔐 Adding DRM key: {drm_info['formatted_key']}")
                cmd.extend(["--key", drm_info['formatted_key']])
                cmd.extend(["--key-text-file", str(self.keys_file)])
                cmd.extend(["--decryption-binary-path", str(self.mp4decrypt_path)])
            else:
                print(f"⚠️ No DRM keys available. DRM info: {drm_info}")

            # Add other options
            cmd.extend(["--binary-merge"])
            cmd.extend(["--del-after-done"])
            cmd.extend(["-mt"])  # Concurrent download
            cmd.extend(["--download-retry-count", "3"])
            cmd.extend(["--ffmpeg-binary-path", str(self.ffmpeg_path)])

            # Reset and start dynamic progress system
            self.reset_dynamic_progress()
            self.start_continuous_progress_monitor()
            self.update_dynamic_progress('video', 1, f"🚀 Starting download: {filename}")

            # Print the command for debugging
            print(f"🚀 N_m3u8DL-RE Command:")
            print(f"   {' '.join(cmd)}")
            print(f"📁 Working Directory: {self.base_dir}")
            print(f"📁 Output Directory: {output_dir}")

            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,  # Redirect stderr to stdout for friend's solution
                text=True,
                universal_newlines=True,
                bufsize=1,  # Line buffered
                cwd=str(self.base_dir)
            )

            # Monitor download progress using N_m3u8DL-RE specific monitoring
            self._monitor_n_m3u8dl_progress(process, filename, output_dir, str(output_dir / f"{filename}.mkv"))

        except Exception as e:
            self.download_error.emit(f"Error starting download: {str(e)}")

    def download_poster_to_cache(self, poster_url):
        """Download poster to cache directory"""
        try:
            if not poster_url:
                return

            print(f"📸 Downloading poster to cache: {poster_url}")
            response = requests.get(poster_url)
            response.raise_for_status()

            # Save to cache directory
            poster_filename = "poster.jpg"
            poster_path = self.cache_dir / poster_filename

            with open(poster_path, 'wb') as f:
                f.write(response.content)

            print(f"✅ Poster downloaded to cache: {poster_path}")

        except Exception as e:
            print(f"⚠️ Error downloading poster: {str(e)}")

    def download_and_save_poster(self, poster_url, title, download_path):
        """Download and save poster directly to download folder (for movies)"""
        try:
            if not poster_url:
                return

            print(f"📸 Downloading poster for {title}: {poster_url}")
            response = requests.get(poster_url)
            response.raise_for_status()

            # Save to download directory
            poster_filename = "poster.jpg"
            poster_path = download_path / poster_filename

            with open(poster_path, 'wb') as f:
                f.write(response.content)

            print(f"✅ Poster saved to: {poster_path}")

        except Exception as e:
            print(f"⚠️ Error downloading poster: {str(e)}")

    def move_poster_to_final_folder(self, season_folder_path):
        """Move poster from cache to final series folder"""
        try:
            if not season_folder_path:
                return

            cache_poster = self.cache_dir / "poster.jpg"
            if cache_poster.exists():
                final_poster = season_folder_path / "poster.jpg"

                # Only move if doesn't exist in final location
                if not final_poster.exists():
                    shutil.move(str(cache_poster), str(final_poster))
                    print(f"📸 Poster moved to: {final_poster}")
                else:
                    # Remove cache poster if final already exists
                    cache_poster.unlink()
                    print(f"📸 Poster already exists in final location")

        except Exception as e:
            print(f"⚠️ Error moving poster: {str(e)}")

    def _monitor_download_progress(self, process, filename, output_file):
        """Monitor download progress using dynamic progress system"""
        try:
            print(f"🔍 Starting dynamic progress monitoring for: {filename}")

            import re
            current_progress_video = 0
            current_progress_audio = 0
            current_progress_subtitle = 0

            while True:
                line = process.stdout.readline()
                if not line:
                    break

                line_content = line.strip()

                # Video progress monitoring - update dynamic system
                if line_content.startswith("Vid ") and "%" in line_content:
                    m = re.search(r"(\d+)%", line_content)
                    if m:
                        prog = int(m.group(1))
                        if prog != current_progress_video:
                            current_progress_video = prog
                            self.update_dynamic_progress('video', prog)
                            print(f"📹 Video Progress: {prog}%")
                    continue

                # Audio progress monitoring - update dynamic system
                if line_content.startswith("Aud ") and "%" in line_content:
                    m = re.search(r"(\d+)%", line_content)
                    if m:
                        prog = int(m.group(1))
                        if prog != current_progress_audio:
                            current_progress_audio = prog
                            self.update_dynamic_progress('audio', prog)
                            print(f"🔊 Audio Progress: {prog}%")
                    continue

                # Subtitle progress monitoring - update dynamic system
                if line_content.startswith("Sub ") and "%" in line_content:
                    m = re.search(r"(\d+)%", line_content)
                    if m:
                        prog = int(m.group(1))
                        if prog != current_progress_subtitle:
                            current_progress_subtitle = prog
                            self.update_dynamic_progress('subtitle', prog)
                            print(f"📝 Subtitle Progress: {prog}%")
                    continue

                # Status update for other lines
                self.status_update.emit(f"N_m3u8DL: {line_content}")

            # Check if download completed successfully
            if process.returncode == 0:
                print(f"✅ Process completed successfully for: {filename}")
                # Update merge progress
                self.update_dynamic_progress('merge', 50, "🔧 Starting merge...")

                if os.path.exists(output_file):
                    print(f"✅ Output file exists: {output_file}")
                    self.update_dynamic_progress('merge', 100, "✅ Merge completed!")
                    self.stop_continuous_progress_monitor()
                    self._emit_completion_once(output_file, True)
                else:
                    print(f"⚠️ Output file not found: {output_file}")
                    self.update_dynamic_progress('merge', 100, "✅ Process completed!")
                    self.stop_continuous_progress_monitor()
            else:
                print(f"❌ Process failed with return code: {process.returncode}")
                self.stop_continuous_progress_monitor()
                self.download_error.emit(f"Download failed: {filename}")

        except Exception as e:
            self.stop_continuous_progress_monitor()
            self.download_error.emit(f"Error monitoring download: {str(e)}")

    def download_poster_to_cache(self, poster_url):
        """Download poster to cache directory like original OSN.py"""
        try:
            import requests

            # Create cache directory
            cache_dir = self.base_dir / "cache"
            cache_dir.mkdir(exist_ok=True)

            print(f"📸 Downloading poster from: {poster_url}")

            response = requests.get(poster_url, stream=True, timeout=10)
            response.raise_for_status()

            cache_poster_path = cache_dir / "poster.jpg"
            with open(cache_poster_path, "wb") as file:
                file.write(response.content)

            print(f"✅ Poster downloaded and saved as {cache_poster_path}")
            return str(cache_poster_path)

        except Exception as e:
            print(f"❌ Failed to download poster: {e}")
            return None

    def move_poster_to_final_folder(self, series_folder_path):
        """Move poster from cache to final series folder like original OSN.py"""
        try:
            cache_dir = self.base_dir / "cache"
            cache_poster_path = cache_dir / "poster.jpg"
            final_poster_path = series_folder_path / "poster.jpg"

            if cache_poster_path.exists():
                import shutil
                shutil.move(str(cache_poster_path), str(final_poster_path))
                print(f"📸 Poster moved to {final_poster_path}")
            else:
                print("⚠️ Poster not found in cache to move")

        except Exception as e:
            print(f"❌ Error while moving poster: {e}")

    def download_and_save_poster(self, poster_url, sanitized_title, download_path):
        """Download and save poster directly to movie folder like original OSN.py"""
        try:
            import requests

            print(f"📸 Downloading movie poster from: {poster_url}")

            response = requests.get(poster_url, stream=True, timeout=10)
            response.raise_for_status()

            filename = f"{sanitized_title}_poster.jpg"
            file_path = download_path / filename

            with open(file_path, "wb") as file:
                file.write(response.content)

            print(f"✅ Movie poster downloaded and saved as {file_path}")

        except Exception as e:
            print(f"❌ Failed to download movie poster: {e}")

    def set_poster_url(self, poster_url):
        """Set the poster URL for current download session"""
        self.current_poster_url = poster_url
        print(f"📸 Poster URL set for download session: {poster_url}")

    def _monitor_aria2c_progress(self, process, download_name, output_file):
        """Monitor aria2c download progress using enhanced YANGO solution"""
        try:
            print(f"🔍 Starting enhanced aria2c progress monitoring for: {download_name}")

            import re
            import time
            import threading

            # Enhanced progress tracking system from YANGO
            current_progress = 0
            target_progress = 0
            smooth_progress = 0
            download_phase = "downloading"

            # Send initial progress signal
            self.download_progress.emit(1, f"Starting aria2c download: {download_name}")
            print(f"📡 Sent initial aria2c progress signal: 1%")

            # Enhanced smooth progress animation function
            def smooth_progress_animation():
                nonlocal smooth_progress, target_progress, download_phase
                while smooth_progress < 100:
                    if smooth_progress < target_progress:
                        smooth_progress += 1
                        status_msg = f"Downloading: {smooth_progress}%"
                        self.download_progress.emit(smooth_progress, status_msg)
                        print(f"🎯 Enhanced aria2c Progress: {smooth_progress}% - {status_msg}")
                        time.sleep(0.1)  # Update every 100ms for smooth animation
                    else:
                        time.sleep(0.2)  # Wait for new target

            # Start smooth progress thread
            progress_thread = threading.Thread(target=smooth_progress_animation, daemon=True)
            progress_thread.start()

            while True:
                line = process.stdout.readline()
                if not line:
                    break

                line_content = line.strip()

                # Enhanced aria2c progress monitoring
                # Look for patterns like: [#1 SIZE/TOTAL(PERCENTAGE%)]
                if "[#" in line_content and "%" in line_content:
                    # Pattern: [#1 1.2MiB/10.5MiB(11%)]
                    m = re.search(r'\[#\d+.*?(\d+)%\]', line_content)
                    if m:
                        prog = int(m.group(1))
                        if prog != current_progress:
                            # Map aria2c progress to 0-95% range (leave room for completion)
                            mapped_progress = min(int(prog * 0.95), 95)
                            target_progress = mapped_progress
                            current_progress = prog
                            print(f"📊 aria2c Progress: {prog}% -> Mapped: {mapped_progress}%")
                    continue

                # Look for download speed and ETA information
                if "DL:" in line_content:
                    # Extract download speed for status
                    speed_match = re.search(r'DL:([^\s]+)', line_content)
                    if speed_match:
                        speed = speed_match.group(1)
                        self.status_update.emit(f"OSN aria2c: Speed {speed}")
                    continue

                # Enhanced status update for other lines
                if line_content and not line_content.startswith("[#"):
                    self.status_update.emit(f"OSN aria2c: {line_content}")

            # Wait for process to complete
            process.wait()

            if process.returncode == 0:
                print("✅ aria2c download completed successfully")
                download_phase = "completed"
                target_progress = 100
                time.sleep(0.5)  # Give time for smooth animation to reach 100%
                self.download_completed.emit(str(output_file), True)
                self.download_progress.emit(100, "✅ aria2c download completed successfully!")
            else:
                print(f"❌ aria2c download failed with code: {process.returncode}")
                self.download_error.emit(f"aria2c download failed with code: {process.returncode}")

        except Exception as e:
            print(f"❌ Error in enhanced aria2c progress monitoring: {str(e)}")
            self.download_error.emit(f"aria2c progress monitoring error: {str(e)}")

    def download_with_aria2c(self, url, output_path, filename):
        """Download file using aria2c with enhanced progress monitoring"""
        try:
            print(f"🚀 Starting aria2c download: {filename}")

            # Build aria2c command
            aria2c_command = [
                str(self.aria2c_path),
                "--continue=true",
                "--max-tries=5",
                "--retry-wait=3",
                "--timeout=60",
                "--max-connection-per-server=16",
                "--split=16",
                "--min-split-size=1M",
                "--dir", str(output_path),
                "--out", filename,
                url
            ]

            print(f"🔧 aria2c command: {' '.join(aria2c_command)}")

            # Start aria2c process
            process = subprocess.Popen(
                aria2c_command,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                universal_newlines=True,
                bufsize=1
            )

            # Monitor progress using enhanced solution
            output_file = output_path / filename
            self._monitor_aria2c_progress(process, filename, output_file)

        except Exception as e:
            print(f"❌ Error starting aria2c download: {str(e)}")
            self.download_error.emit(f"aria2c download error: {str(e)}")
