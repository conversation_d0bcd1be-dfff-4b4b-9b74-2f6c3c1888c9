"""
Shahid UI Module
This module handles the UI components for the Shahid application.
"""

from PySide6.QtCore import *
from PySide6.QtGui import *
from PySide6.QtWidgets import *
import os
import requests
from tabulate import tabulate


class CenterAlignDelegate(QStyledItemDelegate):
    """Delegate to center align content in table cells."""
    def initStyleOption(self, option, index):
        super().initStyleOption(option, index)
        option.displayAlignment = Qt.AlignCenter

class ShahidUI:
    def __init__(self, main_window, widgets):
        self.main_window = main_window  # Guardar referencia a la ventana principal
        self.widgets = widgets
        self.current_content = None
        self.setup_ui()

    def setup_ui(self):
        """Set up the UI components."""
        # Create home page widgets
        self.setup_home_page()

        # Create movies page widgets
        self.setup_movies_page()

        # Create series page widgets
        self.setup_series_page()

        # Create settings page widgets
        self.setup_settings_page()

    def setup_home_page(self):
        """Set up the home page UI."""
        # Create a layout for the home page
        self.home_layout = QVBoxLayout(self.widgets.home)
        self.home_layout.setContentsMargins(10, 10, 10, 10)

        # Add logo image with transparent background
        self.logo_label = QLabel()

        # Load Shahid logo using resource utils
        from ..resource_utils import load_shahid_image_with_fallback

        logo_loaded, logo_result, is_pixmap = load_shahid_image_with_fallback(
            "Shahid_logo_light.png",
            "Shahid Logo",
            "font-size: 24px; color: #ff79c6; margin: 10px;"
        )

        if logo_loaded and is_pixmap:
            self.logo_pixmap = logo_result
            # Create a new pixmap with transparent background
            transparent_pixmap = QPixmap(self.logo_pixmap.size())
            transparent_pixmap.fill(Qt.transparent)

            # Create a painter to draw on the transparent pixmap
            painter = QPainter(transparent_pixmap)

            # Set composition mode to only copy non-black pixels
            painter.setCompositionMode(QPainter.CompositionMode_Source)
            painter.drawPixmap(0, 0, self.logo_pixmap)
            painter.setCompositionMode(QPainter.CompositionMode_DestinationIn)

            # Create a mask to make black background transparent
            mask = self.logo_pixmap.createMaskFromColor(QColor(0, 0, 0), Qt.MaskOutColor)
            painter.drawPixmap(0, 0, mask)
            painter.end()

            # Increase the size of the logo
            scaled_pixmap = transparent_pixmap.scaled(250, 120, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            self.logo_label.setPixmap(scaled_pixmap)

            # Add some margin around the logo
            self.logo_label.setStyleSheet("margin: 10px; background-color: transparent;")
        else:
            # Use fallback text if logo failed to load
            self.logo_label.setText(logo_result)
            self.logo_label.setStyleSheet("font-size: 24px; color: #ff79c6; margin: 10px;")

        self.logo_label.setAlignment(Qt.AlignCenter)
        self.home_layout.addWidget(self.logo_label)

        # Add a welcome label
        self.welcome_label = QLabel("Shahid Downloader Pro")
        self.welcome_label.setAlignment(Qt.AlignCenter)
        self.welcome_label.setStyleSheet("font-size: 24px; color: #ff79c6; margin-bottom: 20px;")
        self.home_layout.addWidget(self.welcome_label)

        # Add search section
        self.search_group = QGroupBox("Search")
        self.search_group.setStyleSheet("QGroupBox { font-weight: bold; }")
        self.search_layout = QVBoxLayout(self.search_group)

        # URL input
        self.url_layout = QHBoxLayout()
        self.url_label = QLabel("Enter URL:")
        self.url_label.setMinimumWidth(80)
        self.url_input = QLineEdit()
        self.url_input.setPlaceholderText("https://shahid.mbc.net/ar/shows/... or content ID")
        self.url_input.setStyleSheet("padding: 8px;")
        self.search_button = QPushButton("Search")
        self.search_button.setStyleSheet(
            "background-color: #00bcd4; color: white; padding: 8px; min-width: 100px;"
        )
        self.url_layout.addWidget(self.url_label)
        self.url_layout.addWidget(self.url_input)
        self.url_layout.addWidget(self.search_button)
        self.search_layout.addLayout(self.url_layout)

        # Recent URLs
        self.recent_layout = QHBoxLayout()
        self.recent_label = QLabel("Recent URLs:")
        self.recent_label.setMinimumWidth(80)
        self.recent_combo = QComboBox()
        self.recent_combo.setStyleSheet("""
            padding: 8px;
            background-color: #282a36;
            border: 1px solid #44475a;
            border-radius: 4px;
            color: #f8f8f2;
            min-height: 30px;

            /* Estilo para el menú desplegable */
            QComboBox QAbstractItemView {
                background-color: #282a36;
                border: 1px solid #44475a;
                color: #f8f8f2;
                selection-background-color: #44475a;
                outline: none;
                padding: 5px;
            }

            /* Estilo para el botón desplegable */
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 20px;
                border-left: 1px solid #44475a;
                border-top-right-radius: 3px;
                border-bottom-right-radius: 3px;
            }

            /* Estilo para los elementos del menú */
            QComboBox::item {
                padding: 5px;
                min-height: 25px;
            }

            /* Estilo para los elementos seleccionados */
            QComboBox::item:selected {
                background-color: #44475a;
            }
        """)
        # Establecer el tamaño máximo de visualización para evitar que los elementos sean demasiado largos
        self.recent_combo.setMaximumWidth(400)
        self.recent_combo.setMaxVisibleItems(10)
        self.recent_combo.view().setTextElideMode(Qt.ElideMiddle)

        self.clear_button = QPushButton("Clear")
        self.clear_button.setStyleSheet(
            "background-color: #6c757d; color: white; padding: 8px; min-width: 100px;"
        )
        self.recent_layout.addWidget(self.recent_label)
        self.recent_layout.addWidget(self.recent_combo)
        self.recent_layout.addWidget(self.clear_button)
        self.search_layout.addLayout(self.recent_layout)

        self.home_layout.addWidget(self.search_group)

        # Add content tabs
        self.content_tabs = QTabWidget()
        self.content_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #44475a;
                background-color: #282a36;
            }
            QTabBar::tab {
                background-color: #282a36;
                color: #f8f8f2;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: #44475a;
                border-bottom: 3px solid #00bcd4;
            }
            QTabBar::tab:!selected {
                margin-top: 2px;
            }
        """)

        # Content Info Tab
        self.content_info_tab = QWidget()
        self.content_info_layout = QVBoxLayout(self.content_info_tab)
        self.content_info_layout.setContentsMargins(10, 10, 10, 10)

        # Placeholder for content info (will be populated when content is loaded)
        self.content_info_frame = QFrame()
        self.content_info_frame.setFrameShape(QFrame.StyledPanel)
        self.content_info_frame.setFrameShadow(QFrame.Raised)
        self.content_info_frame.setStyleSheet("""
            QFrame {
                background-color: #282a36;
                border-radius: 8px;
                border: 1px solid #44475a;
            }
        """)
        self.content_info_frame_layout = QHBoxLayout(self.content_info_frame)
        self.content_info_frame_layout.setSpacing(20)  # Add spacing between poster and details
        self.content_info_frame_layout.setContentsMargins(20, 20, 20, 20)  # Add padding inside the frame

        # Left side - Poster
        self.poster_label = QLabel()
        self.poster_label.setMinimumSize(240, 360)
        self.poster_label.setMaximumSize(240, 360)
        self.poster_label.setStyleSheet("""
            QLabel {
                background-color: #44475a;
                border-radius: 4px;
                border: 1px solid #6272a4;
            }
        """)
        self.poster_label.setAlignment(Qt.AlignCenter)
        self.poster_label.setScaledContents(True)  # Scale the image to fit the label
        self.content_info_frame_layout.addWidget(self.poster_label)

        # Right side - Content details
        self.details_widget = QWidget()
        self.details_layout = QVBoxLayout(self.details_widget)
        self.details_layout.setAlignment(Qt.AlignTop)
        self.details_layout.setSpacing(8)  # Add spacing between elements

        # Title with larger font and accent color
        self.title_label = QLabel()
        self.title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: #ff79c6; margin-bottom: 10px;")
        self.details_layout.addWidget(self.title_label)

        # Type with label styling
        self.type_label = QLabel()
        self.type_label.setStyleSheet("font-size: 14px; color: #f8f8f2;")
        self.details_layout.addWidget(self.type_label)

        # Year with label styling
        self.year_label = QLabel()
        self.year_label.setStyleSheet("font-size: 14px; color: #f8f8f2;")
        self.details_layout.addWidget(self.year_label)

        # Episodes count with label styling (for series)
        self.episodes_count_label = QLabel()
        self.episodes_count_label.setStyleSheet("font-size: 14px; color: #f8f8f2;")
        self.details_layout.addWidget(self.episodes_count_label)

        # Genres with label styling
        self.genres_label = QLabel()
        self.genres_label.setStyleSheet("font-size: 14px; color: #f8f8f2;")
        self.genres_label.setWordWrap(True)
        self.details_layout.addWidget(self.genres_label)

        # Cast with label styling
        self.cast_label = QLabel()
        self.cast_label.setStyleSheet("font-size: 14px; color: #f8f8f2;")
        self.cast_label.setWordWrap(True)
        self.details_layout.addWidget(self.cast_label)

        # Description with better styling
        self.description_text = QTextEdit()
        self.description_text.setReadOnly(True)
        self.description_text.setStyleSheet("""
            QTextEdit {
                background-color: transparent;
                border: none;
                color: #f8f8f2;
                font-size: 14px;
            }
        """)
        self.description_text.setMinimumHeight(100)
        self.details_layout.addWidget(self.description_text)

        self.content_info_frame_layout.addWidget(self.details_widget)
        self.content_info_layout.addWidget(self.content_info_frame)

        # Play and Continue buttons
        self.play_button = QPushButton("Play")
        self.play_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                padding: 10px;
                font-size: 14px;
                min-width: 120px;
                border-radius: 4px;
                border: none;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #a5d6a7;
                color: #e8f5e9;
            }
        """)
        self.play_button.setEnabled(False)

        self.continue_button = QPushButton("Continue")
        self.continue_button.setStyleSheet("""
            QPushButton {
                background-color: #00bcd4;
                color: white;
                padding: 10px;
                font-size: 14px;
                min-width: 120px;
                border-radius: 4px;
                border: none;
            }
            QPushButton:hover {
                background-color: #00acc1;
            }
            QPushButton:pressed {
                background-color: #0097a7;
            }
            QPushButton:disabled {
                background-color: #80deea;
                color: #e0f7fa;
            }
        """)
        self.continue_button.setEnabled(False)

        self.content_info_button_layout = QHBoxLayout()
        self.content_info_button_layout.addStretch()
        self.content_info_button_layout.addWidget(self.play_button)
        self.content_info_button_layout.addWidget(self.continue_button)
        self.content_info_layout.addLayout(self.content_info_button_layout)

        self.content_tabs.addTab(self.content_info_tab, "Content Info")

        # Seasons & Episodes Tab
        self.seasons_tab = QWidget()
        self.seasons_layout = QVBoxLayout(self.seasons_tab)
        self.seasons_layout.setContentsMargins(10, 10, 10, 10)

        # Seasons section
        self.seasons_label = QLabel("Seasons:")
        self.seasons_label.setStyleSheet("font-size: 16px; font-weight: bold;")
        self.seasons_layout.addWidget(self.seasons_label)

        self.seasons_list = QListWidget()
        self.seasons_list.setStyleSheet("""
            QListWidget {
                background-color: #282a36;
                border: 1px solid #44475a;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #44475a;
            }
            QListWidget::item:selected {
                background-color: #00bcd4;
                color: white;
            }
        """)
        self.seasons_layout.addWidget(self.seasons_list)

        # Episodes section
        self.episodes_label = QLabel("Episodes:")
        self.episodes_label.setStyleSheet("font-size: 16px; font-weight: bold;")
        self.seasons_layout.addWidget(self.episodes_label)

        # Episode selection controls
        self.episode_controls_layout = QHBoxLayout()
        self.select_all_button = QPushButton("Select All")
        self.select_all_button.setStyleSheet("background-color: #6c757d; color: white; padding: 6px;")
        self.select_none_button = QPushButton("Select None")
        self.select_none_button.setStyleSheet("background-color: #6c757d; color: white; padding: 6px;")

        self.range_label = QLabel("Select Range:")
        self.range_from = QSpinBox()
        self.range_from.setMinimum(1)
        self.range_from.setStyleSheet("padding: 6px;")
        self.range_to_label = QLabel("to")
        self.range_to = QSpinBox()
        self.range_to.setMinimum(1)
        self.range_to.setStyleSheet("padding: 6px;")
        self.select_range_button = QPushButton("Select")
        self.select_range_button.setStyleSheet("background-color: #6c757d; color: white; padding: 6px;")

        self.episode_controls_layout.addWidget(self.select_all_button)
        self.episode_controls_layout.addWidget(self.select_none_button)
        self.episode_controls_layout.addWidget(self.range_label)
        self.episode_controls_layout.addWidget(self.range_from)
        self.episode_controls_layout.addWidget(self.range_to_label)
        self.episode_controls_layout.addWidget(self.range_to)
        self.episode_controls_layout.addWidget(self.select_range_button)
        self.seasons_layout.addLayout(self.episode_controls_layout)

        self.episodes_list = QListWidget()
        self.episodes_list.setSelectionMode(QAbstractItemView.MultiSelection)
        self.episodes_list.setStyleSheet("""
            QListWidget {
                background-color: #282a36;
                border: 1px solid #44475a;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #44475a;
            }
            QListWidget::item:selected {
                background-color: #00bcd4;
                color: white;
            }
        """)
        self.seasons_layout.addWidget(self.episodes_list)

        # Play and Continue buttons for seasons tab
        self.seasons_play_button = QPushButton("Play")
        self.seasons_play_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                padding: 10px;
                font-size: 14px;
                min-width: 120px;
                border-radius: 4px;
                border: none;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #a5d6a7;
                color: #e8f5e9;
            }
        """)
        self.seasons_play_button.setEnabled(False)

        self.seasons_continue_button = QPushButton("Continue")
        self.seasons_continue_button.setStyleSheet("""
            QPushButton {
                background-color: #00bcd4;
                color: white;
                padding: 10px;
                font-size: 14px;
                min-width: 120px;
                border-radius: 4px;
                border: none;
            }
            QPushButton:hover {
                background-color: #00acc1;
            }
            QPushButton:pressed {
                background-color: #0097a7;
            }
            QPushButton:disabled {
                background-color: #80deea;
                color: #e0f7fa;
            }
        """)
        self.seasons_continue_button.setEnabled(False)

        self.seasons_button_layout = QHBoxLayout()
        self.seasons_button_layout.addStretch()
        self.seasons_button_layout.addWidget(self.seasons_play_button)
        self.seasons_button_layout.addWidget(self.seasons_continue_button)
        self.seasons_layout.addLayout(self.seasons_button_layout)

        self.content_tabs.addTab(self.seasons_tab, "Seasons & Episodes")

        # Download Options Tab
        self.download_options_tab = QWidget()
        self.download_options_layout = QVBoxLayout(self.download_options_tab)
        self.download_options_layout.setContentsMargins(10, 10, 10, 10)

        # Crear un widget para contener todo el contenido
        self.download_options_content = QWidget()
        self.download_options_content.setStyleSheet("""
            QWidget {
                background-color: #282a36;
            }
            QGroupBox {
                border: 1px solid #44475a;
                border-radius: 4px;
                margin-top: 1ex;
                font-weight: bold;
                color: #f8f8f2;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 3px;
            }
            QCheckBox {
                color: #f8f8f2;
                spacing: 5px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QCheckBox::indicator:unchecked {
                border: 1px solid #bd93f9;
                background-color: #282a36;
            }
            QCheckBox::indicator:checked {
                border: 1px solid #bd93f9;
                background-color: #bd93f9;
            }
            QComboBox {
                border: 1px solid #44475a;
                border-radius: 3px;
                padding: 5px;
                background-color: #282a36;
                color: #f8f8f2;
            }
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 20px;
                border-left-width: 1px;
                border-left-color: #44475a;
                border-left-style: solid;
            }
        """)

        self.download_options_content_layout = QVBoxLayout(self.download_options_content)

        # Codec selection
        self.codec_group = QGroupBox("Codec")
        self.codec_group_layout = QVBoxLayout(self.codec_group)

        self.h264_checkbox = QCheckBox("H.264 (en)")
        self.h264_checkbox.setChecked(True)
        self.h264_checkbox.setStyleSheet("""
            QCheckBox {
                color: #f8f8f2;
                spacing: 5px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QCheckBox::indicator:unchecked {
                border: 1px solid #bd93f9;
                background-color: #282a36;
                border-radius: 9px;
            }
            QCheckBox::indicator:checked {
                border: 1px solid #bd93f9;
                background-color: #bd93f9;
                border-radius: 9px;
            }
        """)

        self.h265_checkbox = QCheckBox("H.265")
        self.h265_checkbox.setStyleSheet("""
            QCheckBox {
                color: #f8f8f2;
                spacing: 5px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QCheckBox::indicator:unchecked {
                border: 1px solid #bd93f9;
                background-color: #282a36;
                border-radius: 9px;
            }
            QCheckBox::indicator:checked {
                border: 1px solid #bd93f9;
                background-color: #bd93f9;
                border-radius: 9px;
            }
        """)

        self.codec_group_layout.addWidget(self.h264_checkbox)
        self.codec_group_layout.addWidget(self.h265_checkbox)

        # Hacer que los checkboxes sean mutuamente excluyentes (como radio buttons)
        # Cuando se selecciona un codec, actualizar las resoluciones disponibles
        self.h264_checkbox.clicked.connect(lambda checked: self.handle_codec_change(self.h264_checkbox, self.h265_checkbox, checked, "H264"))
        self.h265_checkbox.clicked.connect(lambda checked: self.handle_codec_change(self.h265_checkbox, self.h264_checkbox, checked, "H265"))

        # Resolution selection
        self.resolution_group = QGroupBox("Resolution")
        self.resolution_group_layout = QVBoxLayout(self.resolution_group)

        self.resolution_combo = QComboBox()
        self.resolution_combo.addItems(["2160p", "1080p", "720p", "480p", "360p"])
        self.resolution_combo.setCurrentText("360p")  # Establecer 360p como predeterminado como en la imagen

        # Crear un menú desplegable personalizado para mostrar todas las opciones
        self.resolution_menu = QMenu()
        self.resolution_menu.setStyleSheet("""
            QMenu {
                background-color: #282a36;
                border: 1px solid #44475a;
            }
            QMenu::item {
                padding: 5px 20px;
                color: #f8f8f2;
            }
            QMenu::item:selected {
                background-color: #44475a;
            }
        """)

        for resolution in ["2160p", "1080p", "720p", "480p", "360p"]:
            action = self.resolution_menu.addAction(resolution)

        self.resolution_group_layout.addWidget(self.resolution_combo)

        # Layout horizontal para Codec y Resolution
        self.codec_resolution_layout = QHBoxLayout()
        self.codec_resolution_layout.addWidget(self.codec_group)
        self.codec_resolution_layout.addWidget(self.resolution_group)

        self.download_options_content_layout.addLayout(self.codec_resolution_layout)

        # Audio selection
        self.audio_group = QGroupBox("Audio")
        self.audio_group_layout = QVBoxLayout(self.audio_group)

        self.audio_ar_checkbox = QCheckBox("audio ar (ar)")
        self.audio_ar_checkbox.setChecked(True)
        self.audio_en_checkbox = QCheckBox("audio en (en)")
        self.audio_tr_checkbox = QCheckBox("audio tr (tr)")
        self.audio_tr_checkbox.setChecked(True)

        self.audio_group_layout.addWidget(self.audio_ar_checkbox)
        self.audio_group_layout.addWidget(self.audio_en_checkbox)
        self.audio_group_layout.addWidget(self.audio_tr_checkbox)

        self.download_options_content_layout.addWidget(self.audio_group)

        # Subtitle selection
        self.subtitle_group = QGroupBox("Subtitles")
        self.subtitle_group_layout = QVBoxLayout(self.subtitle_group)

        # Crear un widget para la lista de subtítulos
        self.subtitle_list_widget = QListWidget()
        self.subtitle_list_widget.setSelectionMode(QAbstractItemView.MultiSelection)
        self.subtitle_list_widget.setStyleSheet("""
            QListWidget {
                background-color: #282a36;
                border: none;
                color: #f8f8f2;
            }
            QListWidget::item {
                padding: 4px;
                border: none;
            }
            QListWidget::item:selected {
                background-color: #44475a;
                color: #f8f8f2;
            }
            QListWidget::item:hover {
                background-color: #44475a;
            }
        """)

        # Agregar subtítulos predeterminados
        self.add_subtitle_to_list("ar (ar)", True)
        self.add_subtitle_to_list("en (en)", True)

        # Mantener referencias a los checkboxes para compatibilidad con el código existente
        self.subtitle_ar_checkbox = QCheckBox("ar (ar)")
        self.subtitle_ar_checkbox.setVisible(False)
        self.subtitle_ar_checkbox.setChecked(True)

        self.subtitle_en_checkbox = QCheckBox("en (en)")
        self.subtitle_en_checkbox.setVisible(False)
        self.subtitle_en_checkbox.setChecked(True)

        # Conectar la selección de la lista con los checkboxes ocultos
        self.subtitle_list_widget.itemSelectionChanged.connect(self.update_subtitle_checkboxes)

        self.subtitle_group_layout.addWidget(self.subtitle_list_widget)
        self.download_options_content_layout.addWidget(self.subtitle_group)

        # DRM Info Label
        self.drm_info_label = QLabel()
        self.drm_info_label.setStyleSheet("""
            QLabel {
                background-color: #44475a;
                color: #f8f8f2;
                padding: 10px;
                border-radius: 4px;
                margin-top: 10px;
            }
        """)
        self.drm_info_label.setVisible(False)  # Oculto por defecto
        self.download_options_content_layout.addWidget(self.drm_info_label)

        # Agregar el contenido al layout principal
        self.download_options_layout.addWidget(self.download_options_content)

        # Selection buttons
        self.selection_layout = QHBoxLayout()
        self.select_all_options_button = QPushButton("Select All")
        self.select_all_options_button.setStyleSheet("""
            QPushButton {
                background-color: #44475a;
                color: white;
                padding: 8px;
                border-radius: 4px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #6272a4;
            }
        """)

        self.select_none_options_button = QPushButton("Select None")
        self.select_none_options_button.setStyleSheet("""
            QPushButton {
                background-color: #44475a;
                color: white;
                padding: 8px;
                border-radius: 4px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #6272a4;
            }
        """)

        self.selection_layout.addWidget(self.select_all_options_button)
        self.selection_layout.addWidget(self.select_none_options_button)
        self.selection_layout.addStretch()

        # Play button
        self.download_options_play_button = QPushButton("Play")
        self.download_options_play_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                padding: 10px;
                font-size: 14px;
                min-width: 120px;
                border-radius: 4px;
                border: none;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #a5d6a7;
                color: #e8f5e9;
            }
        """)
        self.download_options_play_button.setEnabled(False)
        self.selection_layout.addWidget(self.download_options_play_button)

        # Add to queue button
        self.add_to_queue_button = QPushButton("Add to Queue")
        self.add_to_queue_button.setStyleSheet("""
            QPushButton {
                background-color: #00bcd4;
                color: white;
                padding: 10px;
                font-size: 14px;
                min-width: 120px;
                border-radius: 4px;
                border: none;
            }
            QPushButton:hover {
                background-color: #00acc1;
            }
            QPushButton:pressed {
                background-color: #0097a7;
            }
            QPushButton:disabled {
                background-color: #80deea;
                color: #e0f7fa;
            }
        """)
        self.selection_layout.addWidget(self.add_to_queue_button)

        # Download with IDM button
        self.download_with_idm_button = QPushButton("Download with IDM")
        self.download_with_idm_button.setStyleSheet("""
            QPushButton {
                background-color: #ff9800;
                color: white;
                padding: 10px;
                font-size: 14px;
                min-width: 120px;
                border-radius: 4px;
                border: none;
            }
            QPushButton:hover {
                background-color: #f57c00;
            }
            QPushButton:pressed {
                background-color: #ef6c00;
            }
            QPushButton:disabled {
                background-color: #ffcc80;
                color: #fff3e0;
            }
        """)
        self.selection_layout.addWidget(self.download_with_idm_button)

        # Initially hide IDM button - will be shown if IDM is enabled in settings
        self.download_with_idm_button.setVisible(False)

        self.download_options_layout.addLayout(self.selection_layout)

        self.content_tabs.addTab(self.download_options_tab, "Download Options")

        # Downloads Tab
        self.downloads_tab = QWidget()
        self.downloads_layout = QVBoxLayout(self.downloads_tab)
        self.downloads_layout.setContentsMargins(10, 10, 10, 10)

        # Downloads table
        self.downloads_table = QTableWidget()
        self.downloads_table.setColumnCount(8)  # Añadimos una columna para las acciones
        self.downloads_table.setHorizontalHeaderLabels(["Title", "Season", "Episode", "Resolution", "Codec", "Progress", "Status", "Actions"])

        # Configurar el estiramiento de las columnas
        self.downloads_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)  # Title column stretches

        # Establecer anchos fijos para otras columnas
        self.downloads_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Fixed)  # Season
        self.downloads_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.Fixed)  # Episode
        self.downloads_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.Fixed)  # Resolution
        self.downloads_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.Fixed)  # Codec
        self.downloads_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.Fixed)  # Progress
        self.downloads_table.horizontalHeader().setSectionResizeMode(6, QHeaderView.Fixed)  # Status
        self.downloads_table.horizontalHeader().setSectionResizeMode(7, QHeaderView.Fixed)  # Actions

        # Establecer anchos específicos
        self.downloads_table.setColumnWidth(1, 80)  # Season
        self.downloads_table.setColumnWidth(2, 80)  # Episode
        self.downloads_table.setColumnWidth(3, 100)  # Resolution
        self.downloads_table.setColumnWidth(4, 80)  # Codec
        self.downloads_table.setColumnWidth(5, 120)  # Progress
        self.downloads_table.setColumnWidth(6, 120)  # Status
        self.downloads_table.setColumnWidth(7, 100)  # Actions

        # Establecer altura de filas
        self.downloads_table.verticalHeader().setDefaultSectionSize(40)  # Altura de las filas

        # Estilo de la tabla
        self.downloads_table.setStyleSheet("""
            QTableWidget {
                background-color: #282a36;
                border: 1px solid #44475a;
            }
            QHeaderView::section {
                background-color: #44475a;
                padding: 6px;
                border: 1px solid #282a36;
                color: white;
                font-weight: bold;
                text-align: center;
            }
            QTableWidget::item {
                padding: 6px;
                border-bottom: 1px solid #44475a;
                color: white;
            }
            QProgressBar {
                border: 1px solid #44475a;
                border-radius: 5px;
                text-align: center;
                background-color: #282a36;
            }
            QProgressBar::chunk {
                background-color: #50fa7b;
                width: 10px;
                margin: 0.5px;
            }
        """)

        # Centrar el contenido de todas las celdas
        self.downloads_table.setItemDelegate(CenterAlignDelegate())

        self.downloads_layout.addWidget(self.downloads_table)

        # Overall progress
        self.overall_progress_layout = QHBoxLayout()
        self.overall_progress_label = QLabel("Overall Progress: 0%")
        self.overall_progress_layout.addWidget(self.overall_progress_label)
        self.overall_progress_layout.addStretch()
        self.downloads_layout.addLayout(self.overall_progress_layout)

        # Download control buttons
        self.download_controls_layout = QHBoxLayout()
        self.start_download_button = QPushButton("Start Download")
        self.start_download_button.setStyleSheet(
            "background-color: #00bcd4; color: white; padding: 8px; min-width: 120px;"
        )
        self.clear_completed_button = QPushButton("Clear Completed")
        self.clear_completed_button.setStyleSheet(
            "background-color: #6c757d; color: white; padding: 8px;"
        )
        self.clear_all_button = QPushButton("Clear All")
        self.clear_all_button.setStyleSheet(
            "background-color: #6c757d; color: white; padding: 8px;"
        )

        self.download_controls_layout.addWidget(self.start_download_button)
        self.download_controls_layout.addWidget(self.clear_completed_button)
        self.download_controls_layout.addWidget(self.clear_all_button)
        self.download_controls_layout.addStretch()

        self.downloads_layout.addLayout(self.download_controls_layout)

        self.content_tabs.addTab(self.downloads_tab, "Downloads")

        # Add tabs to main layout
        self.home_layout.addWidget(self.content_tabs)

        # Initially hide the tabs until content is loaded
        self.content_tabs.setVisible(False)

        # Add token input section (will be shown only if token is not found)
        self.token_frame = QFrame()
        self.token_frame.setFrameShape(QFrame.StyledPanel)
        self.token_frame.setFrameShadow(QFrame.Raised)
        self.token_layout = QVBoxLayout(self.token_frame)

        self.token_label = QLabel("Enter your Shahid token:")
        self.token_label.setStyleSheet("font-size: 14px; margin-bottom: 10px;")
        self.token_layout.addWidget(self.token_label)

        self.token_input = QLineEdit()
        self.token_input.setPlaceholderText("Paste your token here...")
        self.token_input.setStyleSheet("padding: 8px;")
        self.token_layout.addWidget(self.token_input)

        self.token_button = QPushButton("Save Token")
        self.token_button.setStyleSheet(
            "background-color: #bd93f9; color: white; padding: 8px; margin-top: 10px;"
        )
        self.token_layout.addWidget(self.token_button)

        self.home_layout.addWidget(self.token_frame)

        # Add a spacer to push everything to the top
        self.home_layout.addStretch()

    def setup_movies_page(self):
        """Set up the movies page UI."""
        # Create a layout for the movies page
        self.movies_layout = QVBoxLayout(self.widgets.widgets)
        self.movies_layout.setContentsMargins(10, 10, 10, 10)

        # Add a title label
        self.movies_title = QLabel("Movies")
        self.movies_title.setAlignment(Qt.AlignCenter)
        self.movies_title.setStyleSheet("font-size: 24px; color: #ff79c6; margin-bottom: 20px;")
        self.movies_layout.addWidget(self.movies_title)

        # Add a search section
        self.movies_search_frame = QFrame()
        self.movies_search_layout = QHBoxLayout(self.movies_search_frame)

        self.movies_search_input = QLineEdit()
        self.movies_search_input.setPlaceholderText("Search for movies...")
        self.movies_search_input.setStyleSheet("padding: 8px;")
        self.movies_search_layout.addWidget(self.movies_search_input)

        self.movies_search_button = QPushButton("Search")
        self.movies_search_button.setStyleSheet(
            "background-color: #bd93f9; color: white; padding: 8px;"
        )
        self.movies_search_layout.addWidget(self.movies_search_button)

        self.movies_layout.addWidget(self.movies_search_frame)

        # Add a table for movies
        self.movies_table = QTableWidget()
        self.movies_table.setColumnCount(3)
        self.movies_table.setHorizontalHeaderLabels(["ID", "Title", "Actions"])
        self.movies_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.movies_table.setStyleSheet("background-color: transparent;")
        self.movies_layout.addWidget(self.movies_table)

        # Add buttons for movie actions
        self.movies_buttons_frame = QFrame()
        self.movies_buttons_layout = QHBoxLayout(self.movies_buttons_frame)

        self.movies_refresh_button = QPushButton("Refresh List")
        self.movies_refresh_button.setStyleSheet(
            "background-color: #bd93f9; color: white; padding: 8px;"
        )
        self.movies_buttons_layout.addWidget(self.movies_refresh_button)

        self.movies_add_button = QPushButton("Add Movie")
        self.movies_add_button.setStyleSheet(
            "background-color: #bd93f9; color: white; padding: 8px;"
        )
        self.movies_buttons_layout.addWidget(self.movies_add_button)

        self.movies_layout.addWidget(self.movies_buttons_frame)

    def setup_series_page(self):
        """Set up the series page UI."""
        # Create a new page for series if it doesn't exist
        if not hasattr(self.widgets, 'new_page'):
            self.widgets.new_page = QWidget()
            self.widgets.new_page.setObjectName(u"new_page")
            self.widgets.stackedWidget.addWidget(self.widgets.new_page)

        # Create a layout for the series page
        self.series_layout = QVBoxLayout(self.widgets.new_page)
        self.series_layout.setContentsMargins(10, 10, 10, 10)

        # Add a title label
        self.series_title = QLabel("Series")
        self.series_title.setAlignment(Qt.AlignCenter)
        self.series_title.setStyleSheet("font-size: 24px; color: #ff79c6; margin-bottom: 20px;")
        self.series_layout.addWidget(self.series_title)

        # Add a search section
        self.series_search_frame = QFrame()
        self.series_search_layout = QHBoxLayout(self.series_search_frame)

        self.series_search_input = QLineEdit()
        self.series_search_input.setPlaceholderText("Search for series...")
        self.series_search_input.setStyleSheet("padding: 8px;")
        self.series_search_layout.addWidget(self.series_search_input)

        self.series_search_button = QPushButton("Search")
        self.series_search_button.setStyleSheet(
            "background-color: #bd93f9; color: white; padding: 8px;"
        )
        self.series_search_layout.addWidget(self.series_search_button)

        self.series_layout.addWidget(self.series_search_frame)

        # Add a table for series
        self.series_table = QTableWidget()
        self.series_table.setColumnCount(3)
        self.series_table.setHorizontalHeaderLabels(["ID", "Title", "Actions"])
        self.series_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.series_table.setStyleSheet("background-color: transparent;")
        self.series_layout.addWidget(self.series_table)

        # Add buttons for series actions
        self.series_buttons_frame = QFrame()
        self.series_buttons_layout = QHBoxLayout(self.series_buttons_frame)

        self.series_refresh_button = QPushButton("Refresh List")
        self.series_refresh_button.setStyleSheet(
            "background-color: #bd93f9; color: white; padding: 8px;"
        )
        self.series_buttons_layout.addWidget(self.series_refresh_button)

        self.series_add_button = QPushButton("Add Series")
        self.series_add_button.setStyleSheet(
            "background-color: #bd93f9; color: white; padding: 8px;"
        )
        self.series_buttons_layout.addWidget(self.series_add_button)

        self.series_layout.addWidget(self.series_buttons_frame)

    def setup_settings_page(self):
        """Set up the settings page UI."""
        # For now, we'll just use the existing page
        pass

    def populate_movies_table(self, movies_list):
        """Populate the movies table with data."""
        self.movies_table.setRowCount(0)

        for i, (movie_id, movie_title) in enumerate(movies_list):
            row_position = self.movies_table.rowCount()
            self.movies_table.insertRow(row_position)

            # Add movie ID
            id_item = QTableWidgetItem(str(movie_id))
            self.movies_table.setItem(row_position, 0, id_item)

            # Add movie title
            title_item = QTableWidgetItem(movie_title)
            self.movies_table.setItem(row_position, 1, title_item)

            # Create a widget for the buttons
            buttons_widget = QWidget()
            buttons_layout = QHBoxLayout(buttons_widget)
            buttons_layout.setContentsMargins(0, 0, 0, 0)
            buttons_layout.setAlignment(Qt.AlignCenter)

            # Add download button
            download_button = QPushButton("Download")
            download_button.setStyleSheet(
                "background-color: #00bcd4; color: white; padding: 5px;"
            )
            download_button.clicked.connect(lambda _, id=movie_id: self.main_window.search_content(id))
            buttons_layout.addWidget(download_button)

            # Add delete button
            delete_button = QPushButton("Delete")
            delete_button.setStyleSheet(
                "background-color: #ff5555; color: white; padding: 5px;"
            )
            delete_button.clicked.connect(lambda _, id=movie_id: self.delete_movie(id))
            buttons_layout.addWidget(delete_button)

            self.movies_table.setCellWidget(row_position, 2, buttons_widget)

    def delete_movie(self, movie_id):
        """Delete a movie from the database."""
        # Confirm deletion
        reply = QMessageBox.question(
            self.main_window,
            "Confirm Deletion",
            f"Are you sure you want to delete movie with ID {movie_id}?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Delete from database (this would be implemented in the API)
            # For now, just refresh the list
            self.main_window.refresh_movies_list()

    def populate_series_table(self, series_list):
        """Populate the series table with data."""
        self.series_table.setRowCount(0)

        for i, (series_id, series_title) in enumerate(series_list):
            row_position = self.series_table.rowCount()
            self.series_table.insertRow(row_position)

            # Add series ID
            id_item = QTableWidgetItem(str(series_id))
            self.series_table.setItem(row_position, 0, id_item)

            # Add series title
            title_item = QTableWidgetItem(series_title)
            self.series_table.setItem(row_position, 1, title_item)

            # Create a widget for the buttons
            buttons_widget = QWidget()
            buttons_layout = QHBoxLayout(buttons_widget)
            buttons_layout.setContentsMargins(0, 0, 0, 0)
            buttons_layout.setAlignment(Qt.AlignCenter)

            # Add download button
            download_button = QPushButton("Download")
            download_button.setStyleSheet(
                "background-color: #00bcd4; color: white; padding: 5px;"
            )
            download_button.clicked.connect(lambda _, id=series_id: self.main_window.search_content(id))
            buttons_layout.addWidget(download_button)

            # Add delete button
            delete_button = QPushButton("Delete")
            delete_button.setStyleSheet(
                "background-color: #ff5555; color: white; padding: 5px;"
            )
            delete_button.clicked.connect(lambda _, id=series_id: self.delete_series(id))
            buttons_layout.addWidget(delete_button)

            self.series_table.setCellWidget(row_position, 2, buttons_widget)

    def delete_series(self, series_id):
        """Delete a series from the database."""
        # Confirm deletion
        reply = QMessageBox.question(
            self.main_window,
            "Confirm Deletion",
            f"Are you sure you want to delete series with ID {series_id}?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Delete from database (this would be implemented in the API)
            # For now, just refresh the list
            self.main_window.refresh_series_list()

    def add_subtitle_to_list(self, subtitle_text, selected=False):
        """Add a subtitle to the subtitle list widget."""
        item = QListWidgetItem()
        # Guardar el texto original como datos del ítem para facilitar la extracción posterior
        item.setData(Qt.UserRole, subtitle_text)
        item.setText(subtitle_text)
        item.setFlags(item.flags() | Qt.ItemIsUserCheckable)

        # Crear un widget personalizado para mostrar el ítem con viñeta
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(5, 2, 5, 2)

        # Crear un label con viñeta
        bullet_label = QLabel("•")
        bullet_label.setStyleSheet("color: #bd93f9; font-size: 16px; font-weight: bold;")
        bullet_label.setObjectName("bullet_label")

        # Crear un label para el texto
        text_label = QLabel(subtitle_text)
        text_label.setStyleSheet("color: #f8f8f2;")
        text_label.setObjectName("subtitle_text_label")  # Asignar un nombre para facilitar la búsqueda

        layout.addWidget(bullet_label)
        layout.addWidget(text_label, 1)  # El 1 hace que este widget tome el espacio disponible

        # Agregar el ítem a la lista
        self.subtitle_list_widget.addItem(item)
        self.subtitle_list_widget.setItemWidget(item, widget)

        # Seleccionar el ítem si es necesario
        if selected:
            item.setSelected(True)

    def update_subtitle_checkboxes(self):
        """Update the hidden subtitle checkboxes based on list selection."""
        # Obtener todos los ítems de la lista
        all_items = [self.subtitle_list_widget.item(i) for i in range(self.subtitle_list_widget.count())]

        # Actualizar los checkboxes ocultos basados en la selección (para compatibilidad)
        for item in all_items:
            # Primero intentar obtener el texto de los datos del ítem (más confiable)
            subtitle_text = item.data(Qt.UserRole)

            # Si no hay datos almacenados, usar el texto del ítem
            if not subtitle_text:
                subtitle_text = item.text()

            # Verificar si contiene códigos de idioma específicos
            if subtitle_text:
                if "ar (ar)" in subtitle_text or "(ar)" in subtitle_text:
                    self.subtitle_ar_checkbox.setChecked(item.isSelected())
                    print(f"[UI] Updated Arabic subtitle checkbox: {item.isSelected()}")
                elif "en (en)" in subtitle_text or "(en)" in subtitle_text:
                    self.subtitle_en_checkbox.setChecked(item.isSelected())
                    print(f"[UI] Updated English subtitle checkbox: {item.isSelected()}")
                else:
                    # Para otros idiomas, extraer el código y mostrar información
                    try:
                        if '(' in subtitle_text and ')' in subtitle_text:
                            code = subtitle_text.split('(')[1].split(')')[0]
                            if len(code) in [2, 3]:  # Verificar que sea un código de idioma válido
                                print(f"[UI] Subtitle selected: {subtitle_text} - Selected: {item.isSelected()}")
                    except Exception as e:
                        print(f"[UI] Error processing subtitle: {subtitle_text} - {e}")

    def handle_codec_change(self, selected_checkbox, other_checkbox, checked, codec_type):
        """Handle codec selection change and update resolutions."""
        if checked:
            # Si este checkbox se marca, desmarca el otro
            other_checkbox.setChecked(False)

            # Notificar a la ventana principal para actualizar las resoluciones disponibles
            if hasattr(self, 'main_window') and self.main_window:
                self.main_window.update_resolutions_for_codec(codec_type)

                # También actualizar las pistas de audio disponibles
                self.main_window.update_audio_tracks_for_codec(codec_type)

    def get_selected_resolution(self):
        """Get the currently selected resolution."""
        return self.resolution_combo.currentText()

    def get_selected_codec(self):
        """Get the currently selected codec."""
        if self.h264_checkbox.isChecked():
            return "H264"
        elif self.h265_checkbox.isChecked():
            return "H265"
        else:
            return "H264"  # Default to H264

    def get_selected_audio_tracks(self):
        """Get the currently selected audio tracks."""
        selected_audio_tracks = []

        # Recorrer todos los checkboxes de audio
        for i in range(self.audio_group_layout.count()):
            widget = self.audio_group_layout.itemAt(i).widget()
            if isinstance(widget, QCheckBox) and widget.isChecked():
                text = widget.text()
                # Extraer el código del idioma del texto (formato: "name (code)")
                if '(' in text and ')' in text:
                    code = text.split('(')[1].split(')')[0]
                    selected_audio_tracks.append(code)

        # Verificar que se haya seleccionado al menos una pista de audio
        if not selected_audio_tracks:
            # Si no hay pistas de audio seleccionadas, seleccionar árabe por defecto
            selected_audio_tracks = ["ar"]
            print("[UI] No audio tracks selected, defaulting to Arabic (ar)")

        return selected_audio_tracks

    def get_selected_subtitle_tracks(self):
        """Get the currently selected subtitle tracks."""
        selected_subtitle_tracks = []

        # Obtener subtítulos seleccionados de la lista
        selected_items = self.subtitle_list_widget.selectedItems()
        for item in selected_items:
            # Primero intentar obtener el texto de los datos del ítem (más confiable)
            subtitle_text = item.data(Qt.UserRole)

            # Si no hay datos almacenados, usar el texto del ítem
            if not subtitle_text:
                subtitle_text = item.text()

            # Extraer el código del idioma
            if subtitle_text and '(' in subtitle_text and ')' in subtitle_text:
                try:
                    code = subtitle_text.split('(')[1].split(')')[0]
                    if code not in selected_subtitle_tracks:
                        selected_subtitle_tracks.append(code)
                        print(f"[UI] Selected subtitle: {subtitle_text} -> {code}")
                except Exception as e:
                    print(f"[UI] Error processing subtitle: {subtitle_text} - {e}")

        # Mantener compatibilidad con los checkboxes ocultos
        if self.subtitle_ar_checkbox.isChecked() and 'ar' not in selected_subtitle_tracks:
            selected_subtitle_tracks.append('ar')
            print(f"[UI] Added 'ar' subtitle from hidden checkbox")
        if self.subtitle_en_checkbox.isChecked() and 'en' not in selected_subtitle_tracks:
            selected_subtitle_tracks.append('en')
            print(f"[UI] Added 'en' subtitle from hidden checkbox")

        return selected_subtitle_tracks
